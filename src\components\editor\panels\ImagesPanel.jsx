"use client";

import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { addImageToCanvas } from "@/fabric/fabric-utils";
import { X, Upload, Search, Image as ImageIcon } from "lucide-react";
import { useState } from "react";

export default function ImagesPanel() {
  const { setActivePanel, canvas, markAsModified } = useEditorStore();
  const [imageUrl, setImageUrl] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  const handleClose = () => {
    setActivePanel('select');
  };

  const addImageFromUrl = async () => {
    if (!canvas || !imageUrl.trim()) return;
    
    try {
      await addImageToCanvas(canvas, imageUrl.trim());
      markAsModified();
      setImageUrl('');
    } catch (error) {
      console.error("Error adding image:", error);
      alert("Failed to load image. Please check the URL and try again.");
    }
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files?.[0];
    if (!file || !canvas) return;

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const dataUrl = e.target?.result;
        if (dataUrl) {
          await addImageToCanvas(canvas, dataUrl);
          markAsModified();
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error("Error uploading image:", error);
      alert("Failed to upload image. Please try again.");
    }
  };

  // Sample images for demonstration
  const sampleImages = [
    {
      id: 1,
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
      title: 'Mountain Landscape'
    },
    {
      id: 2,
      url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop',
      title: 'Forest Path'
    },
    {
      id: 3,
      url: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=300&h=200&fit=crop',
      title: 'Lake View'
    },
    {
      id: 4,
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
      title: 'Ocean Waves'
    },
    {
      id: 5,
      url: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=300&h=200&fit=crop',
      title: 'City Skyline'
    },
    {
      id: 6,
      url: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=300&h=200&fit=crop',
      title: 'Desert Sunset'
    },
  ];

  const addSampleImage = async (imageUrl) => {
    if (!canvas) return;
    
    try {
      await addImageToCanvas(canvas, imageUrl);
      markAsModified();
    } catch (error) {
      console.error("Error adding sample image:", error);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Images</h3>
          <p className="text-xs text-gray-500">Add images to your design</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Upload Section */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Upload</h4>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
            <input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
              id="image-upload"
            />
            <label
              htmlFor="image-upload"
              className="cursor-pointer flex flex-col items-center"
            >
              <Upload className="w-8 h-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-600">Click to upload image</span>
              <span className="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 10MB</span>
            </label>
          </div>
        </div>

        {/* URL Input */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Add from URL</h4>
          
          <div className="flex gap-2">
            <Input
              placeholder="Paste image URL here"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={addImageFromUrl}
              disabled={!imageUrl.trim()}
              size="sm"
            >
              Add
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Search Images</h4>
          
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search for images..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button size="sm" variant="outline">
              Search
            </Button>
          </div>
          
          <p className="text-xs text-gray-500">
            Search functionality coming soon. Use sample images below for now.
          </p>
        </div>

        {/* Sample Images */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Sample Images</h4>
          
          <div className="grid grid-cols-2 gap-3">
            {sampleImages.map((image) => (
              <button
                key={image.id}
                onClick={() => addSampleImage(image.url)}
                className="group relative bg-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <ImageIcon className="w-8 h-8 text-gray-400" />
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                  <span className="text-white text-sm opacity-0 group-hover:opacity-100 transition-opacity">
                    Add to canvas
                  </span>
                </div>
                <div className="p-2">
                  <p className="text-xs font-medium text-gray-700 truncate">
                    {image.title}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Tips */}
        <div className="bg-blue-50 rounded-lg p-3">
          <h5 className="text-sm font-medium text-blue-900 mb-1">Tips</h5>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Use high-resolution images for better quality</li>
            <li>• Images will be automatically resized to fit</li>
            <li>• Supported formats: PNG, JPG, GIF, SVG</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
