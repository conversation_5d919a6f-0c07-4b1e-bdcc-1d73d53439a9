"use client";

import { useEffect, useState } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cloneSelectedObject, deletedSelectedObject } from "@/fabric/fabric-utils";
import {
  Copy,
  Trash2,
  FlipHorizontal,
  FlipVertical,
  MoveUp,
  MoveDown,
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  RotateCw,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  Palette,
  Sliders,
  Type,
  Image as ImageIcon,
  Square,
  Circle,
  Triangle
} from "lucide-react";

export default function PropertiesPanel() {
  const { canvas, selectedObject, markAsModified } = useEditorStore();
  
  // Common properties
  const [opacity, setOpacity] = useState(100);
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);
  const [left, setLeft] = useState(0);
  const [top, setTop] = useState(0);
  const [rotation, setRotation] = useState(0);
  const [scaleX, setScaleX] = useState(1);
  const [scaleY, setScaleY] = useState(1);
  const [visible, setVisible] = useState(true);
  const [locked, setLocked] = useState(false);

  // Color properties
  const [fill, setFill] = useState("#000000");
  const [stroke, setStroke] = useState("#000000");
  const [strokeWidth, setStrokeWidth] = useState(0);
  const [strokeDashArray, setStrokeDashArray] = useState([]);
  const [strokeLineCap, setStrokeLineCap] = useState("butt");
  const [strokeLineJoin, setStrokeLineJoin] = useState("miter");

  // Text properties
  const [text, setText] = useState("");
  const [fontSize, setFontSize] = useState(20);
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontWeight, setFontWeight] = useState("normal");
  const [fontStyle, setFontStyle] = useState("normal");
  const [textAlign, setTextAlign] = useState("left");
  const [textDecoration, setTextDecoration] = useState("");
  const [lineHeight, setLineHeight] = useState(1.16);
  const [charSpacing, setCharSpacing] = useState(0);
  const [textBackgroundColor, setTextBackgroundColor] = useState("");

  // Shadow properties
  const [shadowColor, setShadowColor] = useState("#000000");
  const [shadowBlur, setShadowBlur] = useState(0);
  const [shadowOffsetX, setShadowOffsetX] = useState(0);
  const [shadowOffsetY, setShadowOffsetY] = useState(0);

  // Image properties
  const [brightness, setBrightness] = useState(0);
  const [contrast, setContrast] = useState(0);
  const [saturation, setSaturation] = useState(0);
  const [hue, setHue] = useState(0);
  const [blur, setBlur] = useState(0);
  const [sepia, setSepia] = useState(0);
  const [grayscale, setGrayscale] = useState(0);

  // Border radius for shapes
  const [borderRadius, setBorderRadius] = useState(0);

  // Update properties when selection changes
  useEffect(() => {
    if (!selectedObject || !canvas) return;

    // Common properties
    setOpacity(Math.round((selectedObject.opacity || 1) * 100));
    setWidth(Math.round(selectedObject.width * (selectedObject.scaleX || 1)));
    setHeight(Math.round(selectedObject.height * (selectedObject.scaleY || 1)));
    setLeft(Math.round(selectedObject.left || 0));
    setTop(Math.round(selectedObject.top || 0));
    setRotation(Math.round(selectedObject.angle || 0));
    setScaleX(selectedObject.scaleX || 1);
    setScaleY(selectedObject.scaleY || 1);
    setVisible(selectedObject.visible !== false);
    setLocked(!selectedObject.selectable);

    // Color properties
    setFill(selectedObject.fill || "#000000");
    setStroke(selectedObject.stroke || "#000000");
    setStrokeWidth(selectedObject.strokeWidth || 0);
    setStrokeDashArray(selectedObject.strokeDashArray || []);
    setStrokeLineCap(selectedObject.strokeLineCap || "butt");
    setStrokeLineJoin(selectedObject.strokeLineJoin || "miter");

    // Shadow properties
    setShadowColor(selectedObject.shadow?.color || "#000000");
    setShadowBlur(selectedObject.shadow?.blur || 0);
    setShadowOffsetX(selectedObject.shadow?.offsetX || 0);
    setShadowOffsetY(selectedObject.shadow?.offsetY || 0);

    // Text properties
    if (selectedObject.type === "textbox" || selectedObject.type === "text") {
      setText(selectedObject.text || "");
      setFontSize(selectedObject.fontSize || 20);
      setFontFamily(selectedObject.fontFamily || "Arial");
      setFontWeight(selectedObject.fontWeight || "normal");
      setFontStyle(selectedObject.fontStyle || "normal");
      setTextAlign(selectedObject.textAlign || "left");
      setTextDecoration(selectedObject.textDecoration || "");
      setLineHeight(selectedObject.lineHeight || 1.16);
      setCharSpacing(selectedObject.charSpacing || 0);
      setTextBackgroundColor(selectedObject.textBackgroundColor || "");
    }

    // Image properties (filters)
    if (selectedObject.type === "image") {
      const filters = selectedObject.filters || [];
      setBrightness(getFilterValue(filters, 'Brightness') || 0);
      setContrast(getFilterValue(filters, 'Contrast') || 0);
      setSaturation(getFilterValue(filters, 'Saturation') || 0);
      setHue(getFilterValue(filters, 'HueRotation') || 0);
      setBlur(getFilterValue(filters, 'Blur') || 0);
      setSepia(getFilterValue(filters, 'Sepia') || 0);
      setGrayscale(getFilterValue(filters, 'Grayscale') || 0);
    }

    // Border radius for shapes
    if (selectedObject.type === "rect") {
      setBorderRadius(selectedObject.rx || 0);
    }
  }, [selectedObject, canvas]);

  // Helper function to get filter values
  const getFilterValue = (filters, filterType) => {
    const filter = filters.find(f => f.type === filterType);
    return filter ? filter.value || filter.brightness || filter.contrast || filter.saturation || filter.rotation || filter.blur || filter.sepia || filter.grayscale : 0;
  };

  const updateObjectProperty = (property, value) => {
    if (!selectedObject || !canvas) return;

    selectedObject.set(property, value);
    canvas.renderAll();
    markAsModified();
  };

  // Enhanced formatting functions
  const toggleBold = () => {
    if (!selectedObject || !canvas) return;
    const currentWeight = selectedObject.fontWeight;
    const newWeight = currentWeight === "bold" ? "normal" : "bold";
    setFontWeight(newWeight);
    updateObjectProperty("fontWeight", newWeight);
  };

  const toggleItalic = () => {
    if (!selectedObject || !canvas) return;
    const currentStyle = selectedObject.fontStyle;
    const newStyle = currentStyle === "italic" ? "normal" : "italic";
    setFontStyle(newStyle);
    updateObjectProperty("fontStyle", newStyle);
  };

  const toggleUnderline = () => {
    if (!selectedObject || !canvas) return;
    const currentDecoration = selectedObject.textDecoration;
    const newDecoration = currentDecoration === "underline" ? "" : "underline";
    setTextDecoration(newDecoration);
    updateObjectProperty("textDecoration", newDecoration);
  };

  const setTextAlignment = (alignment) => {
    if (!selectedObject || !canvas) return;
    setTextAlign(alignment);
    updateObjectProperty("textAlign", alignment);
  };

  const toggleVisibility = () => {
    if (!selectedObject || !canvas) return;
    const newVisible = !selectedObject.visible;
    setVisible(newVisible);
    updateObjectProperty("visible", newVisible);
  };

  const toggleLock = () => {
    if (!selectedObject || !canvas) return;
    const newLocked = selectedObject.selectable;
    setLocked(newLocked);
    selectedObject.set({
      selectable: !newLocked,
      evented: !newLocked,
      lockMovementX: newLocked,
      lockMovementY: newLocked,
      lockRotation: newLocked,
      lockScalingX: newLocked,
      lockScalingY: newLocked
    });
    canvas.renderAll();
    markAsModified();
  };

  const updateShadow = (property, value) => {
    if (!selectedObject || !canvas) return;

    const currentShadow = selectedObject.shadow || {};
    const newShadow = { ...currentShadow, [property]: value };

    if (newShadow.blur > 0 || newShadow.offsetX !== 0 || newShadow.offsetY !== 0) {
      selectedObject.set("shadow", newShadow);
    } else {
      selectedObject.set("shadow", null);
    }

    canvas.renderAll();
    markAsModified();
  };

  const applyImageFilter = async (filterType, value) => {
    if (!selectedObject || !canvas || selectedObject.type !== "image") return;

    try {
      const fabric = await import("fabric");
      let filters = selectedObject.filters ? [...selectedObject.filters] : [];

      // Remove existing filter of the same type
      filters = filters.filter(f => f.type !== filterType);

      // Add new filter if value is not default
      if (value !== 0) {
        let filter;
        switch (filterType) {
          case 'Brightness':
            filter = new fabric.Image.filters.Brightness({ brightness: value });
            break;
          case 'Contrast':
            filter = new fabric.Image.filters.Contrast({ contrast: value });
            break;
          case 'Saturation':
            filter = new fabric.Image.filters.Saturation({ saturation: value });
            break;
          case 'HueRotation':
            filter = new fabric.Image.filters.HueRotation({ rotation: value });
            break;
          case 'Blur':
            filter = new fabric.Image.filters.Blur({ blur: value });
            break;
          case 'Sepia':
            filter = new fabric.Image.filters.Sepia();
            break;
          case 'Grayscale':
            filter = new fabric.Image.filters.Grayscale();
            break;
        }

        if (filter) {
          filter.type = filterType;
          filters.push(filter);
        }
      }

      selectedObject.filters = filters;
      selectedObject.applyFilters();
      canvas.renderAll();
      markAsModified();
    } catch (error) {
      console.error('Error applying filter:', error);
    }
  };

  const handleClone = () => {
    if (canvas) {
      cloneSelectedObject(canvas);
      markAsModified();
    }
  };

  const handleDelete = () => {
    if (canvas) {
      deletedSelectedObject(canvas);
      markAsModified();
    }
  };

  const handleFlipHorizontal = () => {
    if (!selectedObject || !canvas) return;
    selectedObject.set('flipX', !selectedObject.flipX);
    canvas.renderAll();
    markAsModified();
  };

  const handleFlipVertical = () => {
    if (!selectedObject || !canvas) return;
    selectedObject.set('flipY', !selectedObject.flipY);
    canvas.renderAll();
    markAsModified();
  };

  const handleBringForward = () => {
    if (!selectedObject || !canvas) return;
    canvas.bringObjectForward(selectedObject);
    canvas.renderAll();
    markAsModified();
  };

  const handleSendBackward = () => {
    if (!selectedObject || !canvas) return;
    canvas.sendObjectBackwards(selectedObject);
    canvas.renderAll();
    markAsModified();
  };

  if (!selectedObject) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500">
          <p className="text-sm">Select an object to view properties</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <h3 className="text-sm font-medium">Properties</h3>
        <p className="text-xs text-gray-500 mt-1">
          {selectedObject.type.charAt(0).toUpperCase() + selectedObject.type.slice(1)} selected
        </p>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Object Info */}
        <div className="space-y-2">
          <Label className="text-xs font-medium flex items-center gap-2">
            {selectedObject.type === "textbox" || selectedObject.type === "text" ? (
              <Type className="h-3 w-3" />
            ) : selectedObject.type === "image" ? (
              <ImageIcon className="h-3 w-3" />
            ) : selectedObject.type === "rect" ? (
              <Square className="h-3 w-3" />
            ) : selectedObject.type === "circle" ? (
              <Circle className="h-3 w-3" />
            ) : selectedObject.type === "triangle" ? (
              <Triangle className="h-3 w-3" />
            ) : (
              <Square className="h-3 w-3" />
            )}
            {selectedObject.type?.charAt(0).toUpperCase() + selectedObject.type?.slice(1)} Properties
          </Label>
        </div>

        {/* Object Actions */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Actions</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button size="sm" onClick={handleClone} className="h-8">
              <Copy className="h-3 w-3 mr-1" />
              Clone
            </Button>
            <Button size="sm" variant="destructive" onClick={handleDelete} className="h-8">
              <Trash2 className="h-3 w-3 mr-1" />
              Delete
            </Button>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <Button
              size="sm"
              variant={visible ? "outline" : "secondary"}
              onClick={toggleVisibility}
              className="h-8"
            >
              {visible ? <Eye className="h-3 w-3 mr-1" /> : <EyeOff className="h-3 w-3 mr-1" />}
              {visible ? "Hide" : "Show"}
            </Button>
            <Button
              size="sm"
              variant={locked ? "secondary" : "outline"}
              onClick={toggleLock}
              className="h-8"
            >
              {locked ? <Lock className="h-3 w-3 mr-1" /> : <Unlock className="h-3 w-3 mr-1" />}
              {locked ? "Unlock" : "Lock"}
            </Button>
          </div>
          <div className="grid grid-cols-4 gap-2">
            <Button size="sm" variant="outline" onClick={handleFlipHorizontal} className="h-8 p-0">
              <FlipHorizontal className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleFlipVertical} className="h-8 p-0">
              <FlipVertical className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleBringForward} className="h-8 p-0">
              <MoveUp className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleSendBackward} className="h-8 p-0">
              <MoveDown className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Size & Position */}
        <div className="space-y-3">
          <Label className="text-xs font-medium">Size & Position</Label>
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-1">
              <Label className="text-xs">Width</Label>
              <div className="h-8 px-3 py-2 border rounded-md flex items-center text-sm">
                {width}
              </div>
            </div>
            <div className="space-y-1">
              <Label className="text-xs">Height</Label>
              <div className="h-8 px-3 py-2 border rounded-md flex items-center text-sm">
                {height}
              </div>
            </div>
            <div className="space-y-1">
              <Label className="text-xs">X Position</Label>
              <div className="h-8 px-3 py-2 border rounded-md flex items-center text-sm">
                {left}
              </div>
            </div>
            <div className="space-y-1">
              <Label className="text-xs">Y Position</Label>
              <div className="h-8 px-3 py-2 border rounded-md flex items-center text-sm">
                {top}
              </div>
            </div>
          </div>
        </div>

        {/* Opacity */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Opacity</Label>
          <div className="flex items-center space-x-2">
            <input
              type="range"
              min="0"
              max="100"
              value={opacity}
              onChange={(e) => {
                const newOpacity = parseInt(e.target.value);
                setOpacity(newOpacity);
                updateObjectProperty("opacity", newOpacity / 100);
              }}
              className="flex-1"
            />
            <span className="text-xs text-gray-500 w-10">{opacity}%</span>
          </div>
        </div>

        {/* Colors */}
        <div className="space-y-3">
          <Label className="text-xs font-medium">Colors</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Label className="text-xs w-12">Fill</Label>
              <input
                type="color"
                value={fill}
                onChange={(e) => {
                  setFill(e.target.value);
                  updateObjectProperty("fill", e.target.value);
                }}
                className="w-8 h-8 rounded border"
              />
              <Input
                value={fill}
                onChange={(e) => {
                  setFill(e.target.value);
                  updateObjectProperty("fill", e.target.value);
                }}
                className="flex-1 h-8 text-xs"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Label className="text-xs w-12">Stroke</Label>
              <input
                type="color"
                value={stroke}
                onChange={(e) => {
                  setStroke(e.target.value);
                  updateObjectProperty("stroke", e.target.value);
                }}
                className="w-8 h-8 rounded border"
              />
              <Input
                value={stroke}
                onChange={(e) => {
                  setStroke(e.target.value);
                  updateObjectProperty("stroke", e.target.value);
                }}
                className="flex-1 h-8 text-xs"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Label className="text-xs w-12">Width</Label>
              <Input
                type="number"
                value={strokeWidth}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0;
                  setStrokeWidth(value);
                  updateObjectProperty("strokeWidth", value);
                }}
                className="flex-1 h-8 text-xs"
                min="0"
                max="20"
              />
            </div>
          </div>
        </div>

        {/* Text Properties */}
        {(selectedObject.type === "textbox" || selectedObject.type === "text") && (
          <div className="space-y-4">
            <Label className="text-xs font-medium">Text Content</Label>
            <textarea
              value={text}
              onChange={(e) => {
                setText(e.target.value);
                updateObjectProperty("text", e.target.value);
              }}
              className="w-full h-20 px-3 py-2 border rounded-md text-sm resize-none"
              placeholder="Enter your text here..."
            />

            {/* Text Formatting Toolbar */}
            <div className="space-y-3">
              <Label className="text-xs font-medium">Formatting</Label>
              <div className="grid grid-cols-4 gap-1">
                <Button
                  size="sm"
                  variant={fontWeight === "bold" ? "default" : "outline"}
                  onClick={toggleBold}
                  className="h-8 p-0"
                  title="Bold"
                >
                  <Bold className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={fontStyle === "italic" ? "default" : "outline"}
                  onClick={toggleItalic}
                  className="h-8 p-0"
                  title="Italic"
                >
                  <Italic className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={textDecoration === "underline" ? "default" : "outline"}
                  onClick={toggleUnderline}
                  className="h-8 p-0"
                  title="Underline"
                >
                  <Underline className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setRotation(rotation + 90);
                    updateObjectProperty("angle", rotation + 90);
                  }}
                  className="h-8 p-0"
                  title="Rotate"
                >
                  <RotateCw className="h-3 w-3" />
                </Button>
              </div>

              {/* Text Alignment */}
              <div className="grid grid-cols-4 gap-1">
                <Button
                  size="sm"
                  variant={textAlign === "left" ? "default" : "outline"}
                  onClick={() => setTextAlignment("left")}
                  className="h-8 p-0"
                  title="Align Left"
                >
                  <AlignLeft className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={textAlign === "center" ? "default" : "outline"}
                  onClick={() => setTextAlignment("center")}
                  className="h-8 p-0"
                  title="Align Center"
                >
                  <AlignCenter className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={textAlign === "right" ? "default" : "outline"}
                  onClick={() => setTextAlignment("right")}
                  className="h-8 p-0"
                  title="Align Right"
                >
                  <AlignRight className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant={textAlign === "justify" ? "default" : "outline"}
                  onClick={() => setTextAlignment("justify")}
                  className="h-8 p-0"
                  title="Justify"
                >
                  <AlignJustify className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {/* Font Settings */}
            <div className="space-y-3">
              <Label className="text-xs font-medium">Font Settings</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-xs">Font Size</Label>
                  <Input
                    type="number"
                    value={fontSize}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 12;
                      setFontSize(value);
                      updateObjectProperty("fontSize", value);
                    }}
                    className="h-8 text-xs"
                    min="8"
                    max="200"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Line Height</Label>
                  <Input
                    type="number"
                    step="0.1"
                    value={lineHeight}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value) || 1.16;
                      setLineHeight(value);
                      updateObjectProperty("lineHeight", value);
                    }}
                    className="h-8 text-xs"
                    min="0.5"
                    max="3"
                  />
                </div>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Font Family</Label>
                <select
                  value={fontFamily}
                  onChange={(e) => {
                    setFontFamily(e.target.value);
                    updateObjectProperty("fontFamily", e.target.value);
                  }}
                  className="w-full h-8 px-2 border rounded text-xs"
                >
                  <option value="Arial">Arial</option>
                  <option value="Helvetica">Helvetica</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Georgia">Georgia</option>
                  <option value="Verdana">Verdana</option>
                  <option value="Courier New">Courier New</option>
                  <option value="Impact">Impact</option>
                  <option value="Comic Sans MS">Comic Sans MS</option>
                  <option value="Roboto">Roboto</option>
                  <option value="Open Sans">Open Sans</option>
                  <option value="Lato">Lato</option>
                  <option value="Montserrat">Montserrat</option>
                  <option value="Poppins">Poppins</option>
                </select>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Character Spacing</Label>
                <Input
                  type="number"
                  value={charSpacing}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    setCharSpacing(value);
                    updateObjectProperty("charSpacing", value);
                  }}
                  className="h-8 text-xs"
                  min="-50"
                  max="200"
                />
              </div>
            </div>
          </div>
        )}

        {/* Image Properties */}
        {selectedObject.type === "image" && (
          <div className="space-y-4">
            <Label className="text-xs font-medium flex items-center gap-2">
              <ImageIcon className="h-3 w-3" />
              Image Filters
            </Label>

            <div className="space-y-3">
              <div className="space-y-1">
                <Label className="text-xs">Brightness</Label>
                <Input
                  type="range"
                  min="-1"
                  max="1"
                  step="0.1"
                  value={brightness}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setBrightness(value);
                    applyImageFilter("Brightness", value);
                  }}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{Math.round(brightness * 100)}%</div>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Contrast</Label>
                <Input
                  type="range"
                  min="-1"
                  max="1"
                  step="0.1"
                  value={contrast}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setContrast(value);
                    applyImageFilter("Contrast", value);
                  }}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{Math.round(contrast * 100)}%</div>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Saturation</Label>
                <Input
                  type="range"
                  min="-1"
                  max="1"
                  step="0.1"
                  value={saturation}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setSaturation(value);
                    applyImageFilter("Saturation", value);
                  }}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{Math.round(saturation * 100)}%</div>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Hue</Label>
                <Input
                  type="range"
                  min="-2"
                  max="2"
                  step="0.1"
                  value={hue}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setHue(value);
                    applyImageFilter("HueRotation", value);
                  }}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{Math.round(hue * 180)}°</div>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Blur</Label>
                <Input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={blur}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setBlur(value);
                    applyImageFilter("Blur", value);
                  }}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{Math.round(blur * 100)}%</div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <Button
                  size="sm"
                  variant={sepia > 0 ? "default" : "outline"}
                  onClick={() => {
                    const newSepia = sepia > 0 ? 0 : 1;
                    setSepia(newSepia);
                    applyImageFilter("Sepia", newSepia);
                  }}
                  className="h-8"
                >
                  Sepia
                </Button>
                <Button
                  size="sm"
                  variant={grayscale > 0 ? "default" : "outline"}
                  onClick={() => {
                    const newGrayscale = grayscale > 0 ? 0 : 1;
                    setGrayscale(newGrayscale);
                    applyImageFilter("Grayscale", newGrayscale);
                  }}
                  className="h-8"
                >
                  Grayscale
                </Button>
              </div>

              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setBrightness(0);
                  setContrast(0);
                  setSaturation(0);
                  setHue(0);
                  setBlur(0);
                  setSepia(0);
                  setGrayscale(0);
                  if (selectedObject) {
                    selectedObject.filters = [];
                    selectedObject.applyFilters();
                    canvas.renderAll();
                    markAsModified();
                  }
                }}
                className="w-full h-8"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        )}

        {/* Shadow Properties */}
        <div className="space-y-3">
          <Label className="text-xs font-medium flex items-center gap-2">
            <Sliders className="h-3 w-3" />
            Shadow & Effects
          </Label>

          <div className="space-y-3">
            <div className="space-y-1">
              <Label className="text-xs">Shadow Color</Label>
              <div className="flex items-center space-x-2">
                <input
                  type="color"
                  value={shadowColor}
                  onChange={(e) => {
                    setShadowColor(e.target.value);
                    updateShadow("color", e.target.value);
                  }}
                  className="w-8 h-8 rounded border"
                />
                <Input
                  value={shadowColor}
                  onChange={(e) => {
                    setShadowColor(e.target.value);
                    updateShadow("color", e.target.value);
                  }}
                  className="flex-1 h-8 text-xs"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-1">
                <Label className="text-xs">Shadow Blur</Label>
                <Input
                  type="number"
                  value={shadowBlur}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    setShadowBlur(value);
                    updateShadow("blur", value);
                  }}
                  className="h-8 text-xs"
                  min="0"
                  max="50"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Shadow Offset X</Label>
                <Input
                  type="number"
                  value={shadowOffsetX}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    setShadowOffsetX(value);
                    updateShadow("offsetX", value);
                  }}
                  className="h-8 text-xs"
                  min="-50"
                  max="50"
                />
              </div>
            </div>

            <div className="space-y-1">
              <Label className="text-xs">Shadow Offset Y</Label>
              <Input
                type="number"
                value={shadowOffsetY}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0;
                  setShadowOffsetY(value);
                  updateShadow("offsetY", value);
                }}
                className="h-8 text-xs"
                min="-50"
                max="50"
              />
            </div>
          </div>
        </div>

        {/* Advanced Transform */}
        <div className="space-y-3">
          <Label className="text-xs font-medium">Advanced Transform</Label>

          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1">
              <Label className="text-xs">Rotation</Label>
              <Input
                type="number"
                value={rotation}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0;
                  setRotation(value);
                  updateObjectProperty("angle", value);
                }}
                className="h-8 text-xs"
                min="-360"
                max="360"
              />
            </div>
            <div className="space-y-1">
              <Label className="text-xs">Scale X</Label>
              <Input
                type="number"
                step="0.1"
                value={scaleX}
                onChange={(e) => {
                  const value = parseFloat(e.target.value) || 1;
                  setScaleX(value);
                  updateObjectProperty("scaleX", value);
                }}
                className="h-8 text-xs"
                min="0.1"
                max="5"
              />
            </div>
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Scale Y</Label>
            <Input
              type="number"
              step="0.1"
              value={scaleY}
              onChange={(e) => {
                const value = parseFloat(e.target.value) || 1;
                setScaleY(value);
                updateObjectProperty("scaleY", value);
              }}
              className="h-8 text-xs"
              min="0.1"
              max="5"
            />
          </div>

          {/* Border Radius for Rectangles */}
          {selectedObject.type === "rect" && (
            <div className="space-y-1">
              <Label className="text-xs">Border Radius</Label>
              <Input
                type="number"
                value={borderRadius}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0;
                  setBorderRadius(value);
                  updateObjectProperty("rx", value);
                  updateObjectProperty("ry", value);
                }}
                className="h-8 text-xs"
                min="0"
                max="100"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
