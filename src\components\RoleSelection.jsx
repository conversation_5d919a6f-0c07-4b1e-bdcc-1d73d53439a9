"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTemplateStore } from "@/store/templateStore";
import { Settings, Users, Palette, ArrowRight, Crown, User } from "lucide-react";

export default function RoleSelection() {
  const router = useRouter();
  const { setUserRole } = useTemplateStore();
  const [selectedRole, setSelectedRole] = useState(null);

  const handleRoleSelect = (role) => {
    setSelectedRole(role);
    setUserRole(role);
    
    // Navigate based on role
    if (role === 'admin') {
      router.push('/admin');
    } else if (role === 'user') {
      router.push('/templates');
    }
  };

  const roles = [
    {
      id: 'admin',
      title: 'Template Creator',
      subtitle: 'Admin Mode',
      description: 'Create and manage poster templates with full design capabilities',
      icon: Crown,
      color: 'from-purple-500 to-indigo-600',
      features: [
        'Full canvas editor access',
        'Create template layouts',
        'Define editable zones',
        'Set user constraints',
        'Manage template library'
      ]
    },
    {
      id: 'user',
      title: 'Template User',
      subtitle: 'User Mode',
      description: 'Select and customize existing templates for your projects',
      icon: User,
      color: 'from-blue-500 to-cyan-600',
      features: [
        'Browse template gallery',
        'Customize editable areas',
        'Add your content',
        'Export final designs',
        'Save your projects'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
      <div className="max-w-6xl w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
              <Palette className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Poster Editor
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Choose your role to get started with creating or customizing poster templates
          </p>
        </div>

        {/* Role Cards */}
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {roles.map((role) => {
            const IconComponent = role.icon;
            return (
              <div
                key={role.id}
                className={`
                  relative group cursor-pointer transform transition-all duration-300 hover:scale-105
                  ${selectedRole === role.id ? 'scale-105' : ''}
                `}
                onClick={() => handleRoleSelect(role.id)}
              >
                {/* Card Background */}
                <div className="relative bg-gray-800 rounded-2xl p-8 border border-gray-700 hover:border-gray-600 transition-colors">
                  {/* Gradient Overlay */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${role.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity`} />
                  
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-r ${role.color} rounded-xl flex items-center justify-center mb-6`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <div className="relative z-10">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-2xl font-bold text-white">{role.title}</h3>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full bg-gradient-to-r ${role.color} text-white`}>
                        {role.subtitle}
                      </span>
                    </div>
                    
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      {role.description}
                    </p>

                    {/* Features */}
                    <div className="space-y-3 mb-6">
                      {role.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${role.color}`} />
                          <span className="text-gray-400 text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Action Button */}
                    <button className={`
                      w-full flex items-center justify-center gap-2 py-3 px-6 rounded-xl
                      bg-gradient-to-r ${role.color} text-white font-medium
                      transform transition-all duration-200 hover:scale-105 hover:shadow-lg
                      group-hover:shadow-xl
                    `}>
                      <span>Get Started</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-800 rounded-full border border-gray-700">
            <Settings className="w-4 h-4 text-gray-400" />
            <span className="text-gray-400 text-sm">You can switch roles anytime</span>
          </div>
        </div>

        {/* Quick Access */}
        <div className="mt-8 text-center">
          <button
            onClick={() => router.push('/editor')}
            className="inline-flex items-center gap-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors"
          >
            <Palette className="w-4 h-4" />
            <span>Skip to Free Editor</span>
          </button>
        </div>
      </div>
    </div>
  );
}
