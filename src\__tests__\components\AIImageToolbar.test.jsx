/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import AIImageToolbar from '../../components/editor/AIImageToolbar';
import { useEditorStore } from '../../store';
import { aiImageService } from '../../services/aiImageService';

// Mock the editor store
jest.mock('../../store', () => ({
  useEditorStore: jest.fn()
}));

// Mock the AI image service
jest.mock('../../services/aiImageService', () => ({
  aiImageService: {
    getImageDataFromFabricObject: jest.fn(),
    removeBackground: jest.fn(),
    upscaleImage: jest.fn(),
    enhanceImage: jest.fn(),
    colorizeImage: jest.fn(),
    denoiseImage: jest.fn()
  }
}));

// Mock fabric.js
jest.mock('fabric', () => ({
  Image: {
    fromURL: jest.fn((url, callback) => {
      const mockImage = {
        set: jest.fn(),
        left: 100,
        top: 100,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        opacity: 1,
        flipX: false,
        flipY: false,
        id: 'test-image',
        name: 'Test Image'
      };
      setTimeout(() => callback(mockImage), 100);
    })
  }
}));

describe('AIImageToolbar Component', () => {
  const mockCanvas = {
    remove: jest.fn(),
    add: jest.fn(),
    setActiveObject: jest.fn(),
    renderAll: jest.fn()
  };

  const mockSelectedObject = {
    type: 'image',
    getBoundingRect: jest.fn(() => ({
      left: 100,
      top: 100,
      width: 200,
      height: 150
    })),
    left: 100,
    top: 100,
    scaleX: 1,
    scaleY: 1,
    angle: 0,
    opacity: 1,
    flipX: false,
    flipY: false,
    id: 'test-image'
  };

  const mockMarkAsModified = jest.fn();
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    useEditorStore.mockReturnValue({
      canvas: mockCanvas,
      markAsModified: mockMarkAsModified
    });

    aiImageService.getImageDataFromFabricObject.mockReturnValue('data:image/png;base64,mockdata');
    aiImageService.removeBackground.mockResolvedValue('data:image/png;base64,processed');
    aiImageService.upscaleImage.mockResolvedValue('data:image/png;base64,upscaled');
    aiImageService.enhanceImage.mockResolvedValue('data:image/png;base64,enhanced');
    aiImageService.colorizeImage.mockResolvedValue('data:image/png;base64,colorized');
    aiImageService.denoiseImage.mockResolvedValue('data:image/png;base64,denoised');
  });

  test('renders AI toolbar for image objects', () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    expect(screen.getByText('AI Image Tools')).toBeInTheDocument();
    expect(screen.getByText('Remove Background')).toBeInTheDocument();
    expect(screen.getByText('Upscale 2x')).toBeInTheDocument();
    expect(screen.getByText('Enhance')).toBeInTheDocument();
    expect(screen.getByText('Colorize')).toBeInTheDocument();
    expect(screen.getByText('Denoise')).toBeInTheDocument();
  });

  test('does not render for non-image objects', () => {
    const textObject = { ...mockSelectedObject, type: 'text' };
    
    const { container } = render(
      <AIImageToolbar 
        selectedObject={textObject} 
        onClose={mockOnClose} 
      />
    );

    expect(container.firstChild).toBeNull();
  });

  test('calls onClose when close button is clicked', () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  test('handles remove background operation', async () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    const removeBackgroundButton = screen.getByText('Remove Background');
    fireEvent.click(removeBackgroundButton);

    expect(screen.getByText('Processing...')).toBeInTheDocument();

    await waitFor(() => {
      expect(aiImageService.getImageDataFromFabricObject).toHaveBeenCalledWith(mockSelectedObject);
      expect(aiImageService.removeBackground).toHaveBeenCalledWith('data:image/png;base64,mockdata');
    });

    await waitFor(() => {
      expect(mockCanvas.remove).toHaveBeenCalledWith(mockSelectedObject);
      expect(mockCanvas.add).toHaveBeenCalled();
      expect(mockCanvas.setActiveObject).toHaveBeenCalled();
      expect(mockCanvas.renderAll).toHaveBeenCalled();
      expect(mockMarkAsModified).toHaveBeenCalled();
    });
  });

  test('handles upscale operation', async () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    const upscaleButton = screen.getByText('Upscale 2x');
    fireEvent.click(upscaleButton);

    await waitFor(() => {
      expect(aiImageService.upscaleImage).toHaveBeenCalledWith('data:image/png;base64,mockdata', 2);
    });
  });

  test('handles enhance operation', async () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    const enhanceButton = screen.getByText('Enhance');
    fireEvent.click(enhanceButton);

    await waitFor(() => {
      expect(aiImageService.enhanceImage).toHaveBeenCalledWith('data:image/png;base64,mockdata');
    });
  });

  test('disables buttons during processing', async () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    const removeBackgroundButton = screen.getByText('Remove Background');
    const upscaleButton = screen.getByText('Upscale 2x');

    fireEvent.click(removeBackgroundButton);

    expect(upscaleButton).toBeDisabled();
    expect(removeBackgroundButton.closest('button')).toHaveClass('animate-pulse');
  });

  test('shows processing status', async () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    const removeBackgroundButton = screen.getByText('Remove Background');
    fireEvent.click(removeBackgroundButton);

    expect(screen.getByText('Processing your image with AI... This may take a few moments.')).toBeInTheDocument();
  });

  test('handles AI operation errors', async () => {
    aiImageService.removeBackground.mockRejectedValue(new Error('AI service error'));
    
    // Mock window.alert
    window.alert = jest.fn();

    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    const removeBackgroundButton = screen.getByText('Remove Background');
    fireEvent.click(removeBackgroundButton);

    await waitFor(() => {
      expect(window.alert).toHaveBeenCalledWith(
        expect.stringContaining('Failed to apply Remove Background')
      );
    });
  });

  test('shows reset button', () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    expect(screen.getByText('Reset')).toBeInTheDocument();
  });

  test('handles reset filters', () => {
    const mockSelectedObjectWithFilters = {
      ...mockSelectedObject,
      filters: [],
      applyFilters: jest.fn()
    };

    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObjectWithFilters} 
        onClose={mockOnClose} 
      />
    );

    const resetButton = screen.getByText('Reset');
    fireEvent.click(resetButton);

    expect(mockSelectedObjectWithFilters.applyFilters).toHaveBeenCalled();
    expect(mockCanvas.renderAll).toHaveBeenCalled();
    expect(mockMarkAsModified).toHaveBeenCalled();
  });

  test('displays AI tools count', () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    expect(screen.getByText('5 AI tools available')).toBeInTheDocument();
  });

  test('shows informational content', () => {
    render(
      <AIImageToolbar 
        selectedObject={mockSelectedObject} 
        onClose={mockOnClose} 
      />
    );

    expect(screen.getByText('AI-Powered Image Enhancement')).toBeInTheDocument();
    expect(screen.getByText(/These tools use artificial intelligence/)).toBeInTheDocument();
  });
});
