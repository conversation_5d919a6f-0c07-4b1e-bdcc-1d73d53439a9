export const shapeDefinitions = {
  rectangle: {
    type: "rect",
    label: "Rectangle",
    defaultProps: {
      width: 100,
      height: 60,
      fill: "#3b82f6",
      stroke: "#1e40af",
      strokeWidth: 2,
    },
  },
  square: {
    type: "rect",
    label: "Square",
    defaultProps: {
      width: 80,
      height: 80,
      fill: "#3b82f6",
      stroke: "#1e40af",
      strokeWidth: 2,
    },
  },
  circle: {
    type: "circle",
    label: "Circle",
    defaultProps: {
      radius: 50,
      fill: "#10b981",
      stroke: "#047857",
      strokeWidth: 2,
    },
  },
  triangle: {
    type: "triangle",
    label: "Triangle",
    defaultProps: {
      width: 80,
      height: 80,
      fill: "#f59e0b",
      stroke: "#d97706",
      strokeWidth: 2,
    },
  },
  ellipse: {
    type: "ellipse",
    label: "Ellipse",
    defaultProps: {
      rx: 60,
      ry: 40,
      fill: "#8b5cf6",
      stroke: "#7c3aed",
      strokeWidth: 2,
    },
  },
  line: {
    type: "line",
    label: "Line",
    defaultProps: {
      x1: 0,
      y1: 0,
      x2: 100,
      y2: 0,
      stroke: "#374151",
      strokeWidth: 3,
    },
  },
  star: {
    type: "polygon",
    label: "Star",
    defaultProps: {
      fill: "#fbbf24",
      stroke: "#f59e0b",
      strokeWidth: 2,
    },
  },
  arrow: {
    type: "polygon",
    label: "Arrow",
    defaultProps: {
      fill: "#ef4444",
      stroke: "#dc2626",
      strokeWidth: 2,
    },
  },
  pentagon: {
    type: "polygon",
    label: "Pentagon",
    defaultProps: {
      fill: "#06b6d4",
      stroke: "#0891b2",
      strokeWidth: 2,
    },
  },
  hexagon: {
    type: "polygon",
    label: "Hexagon",
    defaultProps: {
      fill: "#84cc16",
      stroke: "#65a30d",
      strokeWidth: 2,
    },
  },
  octagon: {
    type: "polygon",
    label: "Octagon",
    defaultProps: {
      fill: "#f97316",
      stroke: "#ea580c",
      strokeWidth: 2,
    },
  },
  heart: {
    type: "path",
    label: "Heart",
    defaultProps: {
      path: "M 50,90 C 35,80 10,60 10,40 C 10,20 25,10 40,10 C 45,10 50,15 50,20 C 50,15 55,10 60,10 C 75,10 90,20 90,40 C 90,60 65,80 50,90 Z",
      fill: "#ec4899",
      stroke: "#db2777",
      strokeWidth: 2,
    },
  },
};

export const shapeTypes = [
  "rectangle",
  "square",
  "circle",
  "triangle",
  "ellipse",
  "line",
  "star",
  "arrow",
  "pentagon",
  "hexagon",
  "octagon",
  "heart",
];
