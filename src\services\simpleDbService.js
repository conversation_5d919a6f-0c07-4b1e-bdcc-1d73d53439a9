// Simple database service using raw SQL queries to bypass Prisma client issues
import { Pool } from 'pg';

// Create a singleton connection pool
let pool;

function getPool() {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 5, // Maximum number of connections
      idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
      connectionTimeoutMillis: 2000, // Return error after 2 seconds if connection could not be established
    });
  }
  return pool;
}

// Get all templates (from both templates and designs tables)
export async function getAllTemplates() {
  try {
    const client = await getPool().connect();
    
    try {
      // Get templates from templates table
      const templatesResult = await client.query(`
        SELECT
          id, name, description, category, width, height,
          "canvasData", thumbnail, "isPublic", "isPremium",
          tags, "editableZones", "createdAt", "updatedAt"
        FROM templates
        WHERE "isPublic" = true
        ORDER BY "updatedAt" DESC
      `);
      
      // Get designs from designs table (treat as templates)
      const designsResult = await client.query(`
        SELECT 
          id, name, description, width, height, 
          "canvasData", thumbnail, "isPublic", 
          "createdAt", "updatedAt"
        FROM designs 
        WHERE "isPublic" = true 
        ORDER BY "updatedAt" DESC
      `);
      
      // Convert designs to template format
      const designsAsTemplates = designsResult.rows.map(design => ({
        id: design.id,
        name: design.name,
        description: design.description,
        category: 'general',
        width: design.width,
        height: design.height,
        canvasData: design.canvasData,
        thumbnail: design.thumbnail,
        isPublic: design.isPublic,
        isPremium: false,
        tags: [],
        editableZones: null,
        createdAt: design.createdAt,
        updatedAt: design.updatedAt,
      }));
      
      // Parse canvasData for templates (convert string back to JSON)
      const parsedTemplates = templatesResult.rows.map(template => ({
        ...template,
        canvasData: typeof template.canvasData === 'string'
          ? JSON.parse(template.canvasData)
          : template.canvasData
      }));

      // Parse canvasData for designs converted to templates
      const parsedDesignsAsTemplates = designsAsTemplates.map(design => ({
        ...design,
        canvasData: typeof design.canvasData === 'string'
          ? JSON.parse(design.canvasData)
          : design.canvasData
      }));

      // Combine templates and designs
      const allTemplates = [
        ...parsedTemplates,
        ...parsedDesignsAsTemplates
      ];
      
      return {
        success: true,
        templates: allTemplates
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error getting templates:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Get a specific template by ID (try both tables)
export async function getTemplateById(templateId) {
  try {
    const client = await getPool().connect();
    
    try {
      // Try templates table first
      const templateResult = await client.query(`
        SELECT 
          id, name, description, category, width, height, 
          "canvasData", thumbnail, "isPublic", "isPremium", 
          tags, "editableZones", "createdAt", "updatedAt"
        FROM templates 
        WHERE id = $1
      `, [templateId]);
      
      if (templateResult.rows.length > 0) {
        const template = templateResult.rows[0];

        // Parse canvasData if it's a string
        const parsedTemplate = {
          ...template,
          canvasData: typeof template.canvasData === 'string'
            ? JSON.parse(template.canvasData)
            : template.canvasData
        };

        return {
          success: true,
          template: parsedTemplate
        };
      }
      
      // Try designs table
      const designResult = await client.query(`
        SELECT 
          id, name, description, width, height, 
          "canvasData", thumbnail, "isPublic", 
          "createdAt", "updatedAt"
        FROM designs 
        WHERE id = $1
      `, [templateId]);
      
      if (designResult.rows.length > 0) {
        const design = designResult.rows[0];
        return {
          success: true,
          template: {
            id: design.id,
            name: design.name,
            description: design.description,
            category: 'general',
            width: design.width,
            height: design.height,
            canvasData: typeof design.canvasData === 'string'
              ? JSON.parse(design.canvasData)
              : design.canvasData,
            thumbnail: design.thumbnail,
            isPublic: design.isPublic,
            isPremium: false,
            tags: [],
            editableZones: null,
            createdAt: design.createdAt,
            updatedAt: design.updatedAt,
          }
        };
      }
      
      return {
        success: false,
        error: 'Template not found'
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error getting template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Delete a template by ID (try both tables)
export async function deleteTemplateById(templateId) {
  try {
    const client = await getPool().connect();
    
    try {
      // Try to delete from templates table first
      const templateDeleteResult = await client.query(`
        DELETE FROM templates WHERE id = $1
      `, [templateId]);
      
      if (templateDeleteResult.rowCount > 0) {
        console.log('Deleted from templates table:', templateId);
        return {
          success: true,
          message: 'Template deleted from templates table'
        };
      }
      
      // Try to delete from designs table
      const designDeleteResult = await client.query(`
        DELETE FROM designs WHERE id = $1
      `, [templateId]);
      
      if (designDeleteResult.rowCount > 0) {
        console.log('Deleted from designs table:', templateId);
        return {
          success: true,
          message: 'Template deleted from designs table'
        };
      }
      
      return {
        success: false,
        error: 'Template not found in either table'
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error deleting template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Save a new template
export async function saveTemplate(templateData) {
  try {
    const client = await getPool().connect();

    try {
      // Generate a unique ID using cuid (similar to Prisma's default)
      const { createId } = await import('@paralleldrive/cuid2');
      const id = createId();

      const result = await client.query(`
        INSERT INTO templates (
          id, name, description, category, width, height,
          "canvasData", thumbnail, "isPublic", "isPremium",
          tags, "editableZones", "createdById", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW(), NOW())
        RETURNING *
      `, [
        id,
        templateData.name || 'Untitled Template',
        templateData.description || '',
        templateData.category || 'general',
        templateData.width || 800,
        templateData.height || 600,
        typeof templateData.canvasData === 'string' ? templateData.canvasData : JSON.stringify(templateData.canvasData || {}),
        templateData.thumbnail || null,
        templateData.isPublic !== false,
        templateData.isPremium || false,
        templateData.tags || [],
        templateData.editableZones ? JSON.stringify(templateData.editableZones) : null,
        templateData.createdById || 'demo_user'
      ]);

      return {
        success: true,
        template: result.rows[0]
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error saving template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Update a template
export async function updateTemplate(templateId, templateData) {
  try {
    const client = await getPool().connect();

    try {
      // First try to update in templates table
      const templateResult = await client.query(`
        UPDATE templates SET
          name = $2,
          description = $3,
          category = $4,
          width = $5,
          height = $6,
          "canvasData" = $7,
          thumbnail = $8,
          "isPublic" = $9,
          "isPremium" = $10,
          tags = $11,
          "editableZones" = $12,
          "updatedAt" = NOW()
        WHERE id = $1
        RETURNING *
      `, [
        templateId,
        templateData.name,
        templateData.description,
        templateData.category,
        templateData.width,
        templateData.height,
        typeof templateData.canvasData === 'string' ? templateData.canvasData : JSON.stringify(templateData.canvasData || {}),
        templateData.thumbnail,
        templateData.isPublic,
        templateData.isPremium,
        templateData.tags,
        templateData.editableZones ? JSON.stringify(templateData.editableZones) : null
      ]);

      if (templateResult.rowCount > 0) {
        return {
          success: true,
          template: templateResult.rows[0]
        };
      }

      // If not found in templates, try designs table
      const designResult = await client.query(`
        UPDATE designs SET
          name = $2,
          description = $3,
          width = $4,
          height = $5,
          "canvasData" = $6,
          thumbnail = $7,
          "isPublic" = $8,
          "updatedAt" = NOW()
        WHERE id = $1
        RETURNING *
      `, [
        templateId,
        templateData.name,
        templateData.description,
        templateData.width,
        templateData.height,
        JSON.stringify(templateData.canvasData),
        templateData.thumbnail,
        templateData.isPublic
      ]);

      if (designResult.rowCount > 0) {
        return {
          success: true,
          template: {
            ...designResult.rows[0],
            category: 'general',
            isPremium: false,
            tags: [],
            editableZones: null
          }
        };
      }

      return {
        success: false,
        error: 'Template not found'
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error updating template:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
