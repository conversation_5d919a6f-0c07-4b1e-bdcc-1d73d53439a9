# Debug Guide for Redo Button Issue

## Quick Debugging Steps

### 1. Open Browser Console
Open the app and press F12 to open developer tools, go to Console tab.

### 2. Run Comprehensive Test
```javascript
// Run the comprehensive undo/redo test
window.testUndoRedo();
```

### 3. Manual Step-by-Step Test
```javascript
// Get store reference
const store = window.useEditorStore?.getState();

// Check initial state
store.debugHistory();

// Add a shape manually
window.testAddShape();

// Wait 1 second, then check history
setTimeout(() => {
  console.log('=== After adding shape ===');
  store.debugHistory();
  
  // Test undo
  console.log('=== Testing undo ===');
  store.undo();
  
  setTimeout(() => {
    console.log('=== After undo ===');
    store.debugHistory();
    
    // Test redo
    console.log('=== Testing redo ===');
    store.redo();
    
    setTimeout(() => {
      console.log('=== After redo ===');
      store.debugHistory();
    }, 500);
  }, 500);
}, 1000);
```

### 4. Check Button States
```javascript
// Check if buttons are enabled/disabled correctly
const store = window.useEditorStore?.getState();
console.log('Can Undo:', store.canUndo());
console.log('Can Redo:', store.canRedo());
console.log('History Manager:', !!store.historyManager);
```

## Expected Console Output

### Working Redo Should Show:
```
=== REDO CALLED ===
Can redo: true
Current index: 0
History length: 2
📝 Redoing operation: add for object: rectangle-123456
🔄 Applying redo for operation: add, objectId: rectangle-123456
📦 Restoring added object: rectangle-123456
✅ Successfully applied redo for add
✅ Redo completed. New index: 1, Can undo: true, Can redo: false
=== REDO FINISHED ===
```

### If Redo Fails, Look For:
1. **"Cannot redo - exiting"** - History index issue
2. **"No history manager available"** - Store initialization issue  
3. **"Failed to restore object"** - Object restoration issue
4. **"Unknown operation type"** - History entry corruption

## Common Issues and Solutions

### Issue 1: Button Always Disabled
**Symptoms:** Redo button never becomes enabled
**Check:** 
```javascript
const store = window.useEditorStore?.getState();
console.log('History Manager exists:', !!store.historyManager);
console.log('Current index:', store.historyManager?.currentIndex);
console.log('History length:', store.historyManager?.history?.length);
```
**Solution:** History manager not initialized or no operations recorded

### Issue 2: Button Enabled But Nothing Happens
**Symptoms:** Button is clickable but no visual changes
**Check:** Look for errors in `applyHistoryEntry` method
**Solution:** Object restoration or canvas rendering issue

### Issue 3: Objects Don't Reappear
**Symptoms:** Undo works, redo doesn't restore objects
**Check:** 
```javascript
// After redo, check canvas objects
const store = window.useEditorStore?.getState();
console.log('Canvas objects:', store.canvas?.getObjects()?.length);
```
**Solution:** Object restoration logic issue

### Issue 4: History Index Problems
**Symptoms:** canRedo() returns false when it should be true
**Check:**
```javascript
const store = window.useEditorStore?.getState();
const hm = store.historyManager;
console.log('Current index:', hm.currentIndex);
console.log('History length:', hm.history.length);
console.log('Can redo calculation:', hm.currentIndex < hm.history.length - 1);
```

## Troubleshooting Commands

### Reset and Test
```javascript
// Clear history and start fresh
const store = window.useEditorStore?.getState();
store.historyManager?.clearHistory();
store.historyManager?.saveInitialState();

// Add object and test
window.testAddShape();
```

### Force Enable Redo Button
```javascript
// Temporarily enable redo for testing
const store = window.useEditorStore?.getState();
store.historyManager.currentIndex = 0; // Force index
console.log('Forced can redo:', store.canRedo());
```

### Check Object IDs
```javascript
// Check if objects have proper IDs
const store = window.useEditorStore?.getState();
const objects = store.canvas?.getObjects() || [];
objects.forEach(obj => {
  console.log('Object:', obj.type, 'ID:', obj.id);
});
```

## Manual Verification Steps

1. **Add a shape** - Should see "Object added: rectangle-xxx" in console
2. **Check history** - Should see history entry with operation: 'add'
3. **Undo** - Shape disappears, should see "Undo completed"
4. **Check redo button** - Should be enabled (not grayed out)
5. **Click redo** - Should see detailed redo logs
6. **Verify result** - Shape should reappear in exact same position

## If All Else Fails

### Nuclear Option - Restart History Manager
```javascript
const store = window.useEditorStore?.getState();
const canvas = store.canvas;

// Destroy and recreate history manager
if (store.historyManager) {
  store.historyManager.destroy();
}

// Recreate
import('./src/fabric/fabric-utils.js').then(({ CanvasHistoryManager }) => {
  const onStateChange = () => {
    const { undoRedoState } = store;
    store.setUndoRedoState?.(undoRedoState + 1);
  };
  
  const newHistoryManager = new CanvasHistoryManager(canvas, 50, onStateChange);
  store.historyManager = newHistoryManager;
  newHistoryManager.saveInitialState();
  console.log('History manager recreated');
});
```

Run these tests and share the console output to identify the exact issue!
