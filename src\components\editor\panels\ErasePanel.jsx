"use client";

import { useState, useEffect } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toggleDrawingMode, toggleEraseMode, updateDrawingBrush } from "@/fabric/fabric-utils";
import { X, Eraser } from "lucide-react";

export default function ErasePanel() {
  const { setActivePanel, canvas, markAsModified, isDrawingMode, setIsDrawingMode } = useEditorStore();
  const [isErasing, setIsErasing] = useState(false);
  const [eraserWidth, setEraserWidth] = useState(20);

  // Auto-activate eraser when panel opens
  useEffect(() => {
    if (canvas && !isErasing) {
      handleActivateEraser();
    }
    
    // Cleanup when panel closes
    return () => {
      if (canvas && isErasing) {
        handleDeactivateEraser();
      }
    };
  }, [canvas]);

  const handleClose = () => {
    // Turn off eraser mode when closing
    if (canvas && isErasing) {
      handleDeactivateEraser();
    }
    setActivePanel('select');
  };

  const handleActivateEraser = async () => {
    if (!canvas) return;

    try {
      // Enable eraser mode (this will also enable drawing mode)
      setIsErasing(true);
      setIsDrawingMode(true);
      await toggleEraseMode(canvas, true, "#000000", eraserWidth);
      markAsModified();
    } catch (error) {
      console.error('Error activating eraser:', error);
      setIsErasing(false);
      setIsDrawingMode(false);
    }
  };

  const handleDeactivateEraser = async () => {
    if (!canvas) return;

    try {
      // Disable eraser mode
      setIsErasing(false);
      await toggleEraseMode(canvas, false, "#000000", 5);

      // Disable drawing mode
      setIsDrawingMode(false);
      toggleDrawingMode(canvas, false);
      markAsModified();
    } catch (error) {
      console.error('Error deactivating eraser:', error);
    }
  };

  const handleEraserWidthChange = (width) => {
    setEraserWidth(width);
    if (canvas && isErasing && canvas.freeDrawingBrush) {
      canvas.freeDrawingBrush.width = width;
      canvas.renderAll();
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Erase</h3>
          <p className="text-xs text-gray-500">Erase parts of your drawing</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Eraser Control */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Eraser Tool</Label>
          
          <Button
            variant={isErasing ? "destructive" : "outline"}
            onClick={isErasing ? handleDeactivateEraser : handleActivateEraser}
            className="w-full h-12 flex flex-col items-center justify-center"
          >
            <Eraser className="h-5 w-5 mb-1" />
            <span className="text-xs">{isErasing ? 'Stop Erasing' : 'Start Erasing'}</span>
          </Button>
        </div>

        {/* Eraser Settings */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Eraser Settings</Label>
          
          <div className="space-y-3">
            <div className="space-y-2">
              <Label className="text-xs">Eraser Width: {eraserWidth}px</Label>
              <input
                type="range"
                min="5"
                max="100"
                value={eraserWidth}
                onChange={(e) => handleEraserWidthChange(parseInt(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Status */}
        {isErasing && (
          <div className="bg-red-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-red-700">
                Eraser mode active - Click and drag to erase
              </span>
            </div>
          </div>
        )}

        {/* Tips */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h5 className="text-sm font-medium text-gray-900 mb-1">Tips</h5>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Eraser automatically activates when you open this panel</li>
            <li>• Click and drag to erase parts of your drawing</li>
            <li>• Adjust eraser width for different erasing sizes</li>
            <li>• Eraser works on drawn paths and strokes</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
