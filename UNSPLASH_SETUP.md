# Unsplash API Setup Guide

This guide will help you set up the Unsplash API integration for the Poster Editor to enable image search functionality.

## Getting Your Unsplash API Key

1. **Create an Unsplash Developer Account**
   - Go to [Unsplash Developers](https://unsplash.com/developers)
   - Sign up for a free account or log in if you already have one

2. **Create a New Application**
   - Click "New Application" or "Your Apps" → "New Application"
   - Fill out the application form:
     - **Application name**: "Poster Editor" (or your preferred name)
     - **Description**: "A web-based poster design tool with image search"
     - **Website**: Your website URL (can be localhost for development)
   - Accept the API Guidelines and Terms

3. **Get Your Access Key**
   - Once your application is created, you'll see your **Access Key**
   - Copy this key - you'll need it for the next step

## Configuring the Application

1. **Set Up Environment Variables**
   - Copy the `.env.example` file to `.env.local`:
     ```bash
     cp .env.example .env.local
     ```

2. **Add Your API Key**
   - Open `.env.local` in your text editor
   - Replace `demo_access_key` with your actual Unsplash Access Key:
     ```
     NEXT_PUBLIC_UNSPLASH_ACCESS_KEY=your_actual_access_key_here
     ```

3. **Restart the Development Server**
   - Stop the current server (Ctrl+C)
   - Start it again:
     ```bash
     npm run dev
     ```

## Features Available with API Key

Once configured, you'll have access to:

### 🔍 **Image Search**
- Search millions of high-quality photos from Unsplash
- Real-time search results as you type
- Load more results with pagination

### 📸 **Featured Images**
- Curated collection of featured photos
- Updated regularly with trending images
- High-quality professional photography

### 🏷️ **Category Search**
- Quick access to popular categories
- Pre-defined search terms for common topics
- One-click category browsing

### 📊 **API Compliance**
- Automatic download tracking (required by Unsplash)
- Proper attribution display
- Rate limiting respect

## Demo Mode (Without API Key)

If you don't configure an API key, the application will run in demo mode with:
- 6 sample images from Unsplash
- Limited functionality
- No search capabilities
- Demo images only

## API Rate Limits

Unsplash API has the following limits:
- **Demo/Development**: 50 requests per hour
- **Production**: 5,000 requests per hour (after approval)

For production use, you may need to apply for a higher rate limit through Unsplash.

## Troubleshooting

### Common Issues

1. **"Demo Mode" message appears**
   - Check that your API key is correctly set in `.env.local`
   - Ensure the key starts with your actual access key
   - Restart the development server

2. **Search not working**
   - Verify your API key is valid
   - Check browser console for error messages
   - Ensure you haven't exceeded rate limits

3. **Images not loading**
   - Check your internet connection
   - Verify the API key has proper permissions
   - Check if Unsplash service is available

### Getting Help

- [Unsplash API Documentation](https://unsplash.com/documentation)
- [Unsplash API Guidelines](https://help.unsplash.com/en/articles/2511245-unsplash-api-guidelines)
- [Rate Limiting Information](https://unsplash.com/documentation#rate-limiting)

## Security Notes

- Never commit your actual API key to version control
- Keep your `.env.local` file in `.gitignore`
- Use environment variables for production deployment
- Consider using different keys for development and production

## Attribution Requirements

When using Unsplash images, you should:
- Provide attribution to the photographer when possible
- Follow Unsplash's attribution guidelines
- The application automatically handles basic attribution display
