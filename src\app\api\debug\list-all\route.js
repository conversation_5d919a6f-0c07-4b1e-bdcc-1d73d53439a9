import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Try to import Prisma client
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();
    
    // Get all designs
    const designs = await prisma.design.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    // Get all templates
    const templates = await prisma.template.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        category: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    return NextResponse.json({
      success: true,
      designs,
      templates,
      counts: {
        designs: designs.length,
        templates: templates.length
      }
    });
  } catch (error) {
    console.error('Database list error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
