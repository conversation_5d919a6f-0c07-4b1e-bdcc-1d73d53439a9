import {
  getAllTemplates,
  getTemplateById,
  deleteTemplateById,
  saveTemplate as saveTemplateToDb,
  updateTemplate as updateTemplateInDb
} from './simpleDbService';

// Save a new template to the database
export async function saveTemplate(templateData) {
  return await saveTemplateToDb(templateData);
}

// Get a specific template by ID
export async function getTemplate(templateId) {
  return await getTemplateById(templateId);
}

// Get user's templates - simplified version
export async function getUserTemplates(userId, page = 1, limit = 10) {
  // For now, return all templates (can be enhanced later to filter by user)
  return await getPublicTemplates(page, limit);
}

// Get public templates (from both templates and designs tables)
export async function getPublicTemplates(page = 1, limit = 10) {
  const result = await getAllTemplates();
  if (result.success) {
    return {
      success: true,
      templates: result.templates,
      pagination: {
        page,
        limit,
        total: result.templates.length,
        pages: Math.ceil(result.templates.length / limit),
      },
    };
  }
  return result;
}

// Update a template
export async function updateTemplate(templateId, templateData) {
  return await updateTemplateInDb(templateId, templateData);
}

// Delete a template (try both templates and designs tables)
export async function deleteTemplate(templateId) {
  return await deleteTemplateById(templateId);
}
