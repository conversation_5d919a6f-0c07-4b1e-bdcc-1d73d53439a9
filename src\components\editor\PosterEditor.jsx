"use client";

import { useEffect } from "react";
import { useEditorStore } from "@/store";
import Header from "./Header";
import LeftSidebar from "./LeftSidebar";
import Canvas from "./Canvas";
import RightSidebar from "./RightSidebar";

export default function PosterEditor() {
  const { resetStore } = useEditorStore();

  useEffect(() => {
    // Reset the store on mount
    resetStore();

    return () => {
      // Clean up on unmount
      resetStore();
    };
  }, [resetStore]);

  return (
    <div className="flex flex-col h-screen overflow-hidden bg-gray-900">
      {/* Header */}
      <Header />

      {/* Main Editor Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Tool Selection - Hidden on mobile, collapsible on tablet */}
        <div className="hidden md:block">
          <LeftSidebar />
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 flex flex-col overflow-hidden bg-gray-800 min-w-0">
          <Canvas />
        </div>

        {/* Right Sidebar - Layers and Properties - Hidden on mobile and tablet */}
        <div className="hidden lg:block">
          <RightSidebar />
        </div>
      </div>
    </div>
  );
}
