"use client";

import { useState } from "react";
import { useEditorStore } from "@/store";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Loader2, 
  Sparkles, 
  Scissors, 
  ArrowUp, 
  Palette, 
  AlertCircle,
  Wand2,
  Settings
} from "lucide-react";
import {
  generateImage,
  removeBackground,
  upscaleImage,
  generateImageVariations,
  isApiConfigured,
  getAvailableModels,
  getAvailableProviders,
  getDefaultModel,
  getDefaultImageToImageModel,
  getImageVariationModels,
  AI_PROVIDERS
} from "@/services/aiService";
import { addImageToCanvas } from "@/fabric/fabric-utils";

export default function AiPanel() {
  const { canvas, selectedObject } = useEditorStore();
  
  // Generate tab state
  const [prompt, setPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState("");
  const [imageSize, setImageSize] = useState("1024x1024");
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Edit tab state
  const [variationPrompt, setVariationPrompt] = useState("");
  const [selectedVariationModel, setSelectedVariationModel] = useState("");
  const [isGeneratingVariations, setIsGeneratingVariations] = useState(false);
  
  // Enhance tab state
  const [isRemovingBg, setIsRemovingBg] = useState(false);
  const [isUpscaling, setIsUpscaling] = useState(false);
  const [isApplyingFilter, setIsApplyingFilter] = useState(false);
  const [upscaleFactor, setUpscaleFactor] = useState("4");
  const [selectedProvider, setSelectedProvider] = useState(AI_PROVIDERS.TOGETHER);
  
  // General state
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState("generate");

  const isImageSelected = selectedObject && selectedObject.type === 'image';
  
  // Get available options
  const availableProviders = getAvailableProviders();
  const availableModels = getAvailableModels('text-to-image');
  const availableVariationModels = getImageVariationModels();
  const defaultModel = getDefaultModel();
  const defaultVariationModel = getDefaultImageToImageModel();

  // Initialize selected model if not set
  if (!selectedModel && defaultModel) {
    setSelectedModel(defaultModel.id);
  }

  // Initialize variation model if not set
  if (!selectedVariationModel && defaultVariationModel) {
    setSelectedVariationModel(defaultVariationModel.id);
  } else if (!selectedVariationModel && availableVariationModels.length > 0) {
    setSelectedVariationModel(availableVariationModels[0].id);
  }

  const sizeOptions = [
    { value: "512x512", label: "512 × 512" },
    { value: "768x768", label: "768 × 768" },
    { value: "1024x1024", label: "1024 × 1024" },
    { value: "1024x768", label: "1024 × 768" },
    { value: "768x1024", label: "768 × 1024" }
  ];

  const upscaleOptions = [
    { value: "2", label: "2x (Double)" },
    { value: "4", label: "4x (Quadruple)" },
    { value: "8", label: "8x (Maximum)" }
  ];

  const aiFilters = [
    { name: "Artistic", prompt: "artistic style, painterly" },
    { name: "Vintage", prompt: "vintage, retro style" },
    { name: "Cyberpunk", prompt: "cyberpunk, neon, futuristic" },
    { name: "Watercolor", prompt: "watercolor painting style" },
    { name: "Oil Paint", prompt: "oil painting, classical art" },
    { name: "Sketch", prompt: "pencil sketch, hand drawn" }
  ];

  // Utility function to add image to canvas using fabric utils
  const addImageToCanvasUtil = async (imageUrl, options = {}) => {
    if (!canvas) return;

    try {
      console.log("Adding image to canvas using fabric utils:", imageUrl);
      console.log("Image URL type:", typeof imageUrl);
      console.log("Image URL length:", imageUrl?.length);

      // Validate URL
      if (!imageUrl || typeof imageUrl !== 'string') {
        throw new Error('Invalid image URL provided');
      }

      // Handle Together AI images through proxy to avoid CORS issues
      console.log("Processing image URL:", imageUrl);
      let finalImageUrl = imageUrl;

      // Check if this is a Together AI image that needs proxying
      if (imageUrl.startsWith('https://api.together.ai/')) {
        console.log("Together AI image detected, using proxy...");
        finalImageUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
        console.log("Proxied URL:", finalImageUrl);
      } else {
        // For other providers, try direct load first, then blob fallback
        console.log("Testing direct image URL accessibility...");
        try {
          const testImg = new Image();
          testImg.crossOrigin = "anonymous";

          await new Promise((resolve, reject) => {
            testImg.onload = () => {
              console.log("Image URL is accessible, dimensions:", testImg.width, "x", testImg.height);
              resolve();
            };
            testImg.onerror = (error) => {
              console.error("Direct image URL failed, trying blob fallback:", error);
              reject(error);
            };
            testImg.src = imageUrl;
          });
        } catch (directLoadError) {
          console.log("Direct image load failed, trying blob approach...");
          try {
            const response = await fetch(imageUrl, {
              mode: 'cors',
              credentials: 'omit'
            });

            if (!response.ok) {
              throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const blob = await response.blob();
            finalImageUrl = URL.createObjectURL(blob);
            console.log("Created blob URL:", finalImageUrl);
          } catch (fetchError) {
            console.error("Blob fetch also failed:", fetchError);
            throw new Error(`Image not accessible: ${fetchError.message}`);
          }
        }
      }

      const image = await addImageToCanvas(canvas, finalImageUrl);
      console.log("Image added successfully:", image);

      // Clean up blob URL if we created one (but not proxy URLs)
      if (finalImageUrl !== imageUrl && finalImageUrl.startsWith('blob:')) {
        setTimeout(() => URL.revokeObjectURL(finalImageUrl), 1000);
      }

      return image;
    } catch (error) {
      console.error('Error adding image to canvas:', error);
      throw error;
    }
  };

  // Utility function to replace image on canvas
  const replaceImageOnCanvas = async (imageUrl, currentObj) => {
    if (!canvas || !currentObj) return;

    try {
      console.log("Replacing image on canvas:", imageUrl);

      // Store current object properties
      const objProps = {
        left: currentObj.left,
        top: currentObj.top,
        scaleX: currentObj.scaleX,
        scaleY: currentObj.scaleY,
        angle: currentObj.angle
      };

      // Remove current object
      canvas.remove(currentObj);

      // Add new image using fabric utils
      const newImage = await addImageToCanvas(canvas, imageUrl);

      // Apply stored properties to new image
      if (newImage) {
        newImage.set(objProps);
        canvas.setActiveObject(newImage);
        canvas.renderAll();
      }

      console.log("Image replaced successfully:", newImage);
      return newImage;
    } catch (error) {
      console.error('Error replacing image on canvas:', error);
      throw error;
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setError("");

    try {
      if (!isApiConfigured()) {
        throw new Error("Please configure at least one AI provider API key in .env.local");
      }

      console.log("Starting image generation with:", {
        prompt,
        selectedModel,
        imageSize,
        canvas: !!canvas
      });

      // Parse image size
      const [width, height] = imageSize.split('x').map(Number);

      const imageUrl = await generateImage(prompt, selectedModel, {
        width,
        height
      });

      console.log("Generated image URL:", imageUrl);

      if (imageUrl && canvas) {
        console.log("Adding image to canvas...");
        try {
          await addImageToCanvasUtil(imageUrl);
          console.log("Image added to canvas successfully");
          setPrompt("");
        } catch (canvasError) {
          console.error("Canvas integration failed:", canvasError);
          throw new Error(`Failed to add image to canvas: ${canvasError.message}`);
        }
      } else {
        console.error("Missing imageUrl or canvas:", { imageUrl: !!imageUrl, canvas: !!canvas });
        if (!imageUrl) {
          throw new Error("No image was generated by the AI service");
        } else {
          throw new Error("Canvas not available");
        }
      }
    } catch (err) {
      console.error("AI generation failed:", err);
      setError(err.message);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleRemoveBackground = async () => {
    if (!isImageSelected || !canvas) return;

    setIsRemovingBg(true);
    setError("");

    try {
      const imageUrl = selectedObject.getSrc();
      const resultUrl = await removeBackground(imageUrl, selectedProvider);

      if (resultUrl) {
        await replaceImageOnCanvas(resultUrl, selectedObject);
      }
    } catch (err) {
      console.error("Background removal failed:", err);
      setError(err.message);
    } finally {
      setIsRemovingBg(false);
    }
  };

  const handleUpscale = async () => {
    if (!isImageSelected || !canvas) return;

    setIsUpscaling(true);
    setError("");

    try {
      const imageUrl = selectedObject.getSrc();
      const resultUrl = await upscaleImage(imageUrl, parseInt(upscaleFactor), selectedProvider);

      if (resultUrl) {
        await replaceImageOnCanvas(resultUrl, selectedObject);
      }
    } catch (err) {
      console.error("Upscaling failed:", err);
      setError(err.message);
    } finally {
      setIsUpscaling(false);
    }
  };

  const handleGenerateVariations = async () => {
    if (!isImageSelected || !canvas) return;

    setIsGeneratingVariations(true);
    setError("");

    try {
      if (!isApiConfigured()) {
        throw new Error("Please configure at least one AI provider API key in .env.local");
      }

      const imageUrl = selectedObject.getSrc();
      const prompt = variationPrompt || 'high quality, detailed';
      const resultUrl = await generateImageVariations(imageUrl, prompt, 0.8, selectedVariationModel);

      if (resultUrl) {
        await addImageToCanvasUtil(resultUrl);
        setVariationPrompt("");
      }
    } catch (err) {
      console.error("Variation generation failed:", err);
      setError(err.message);
    } finally {
      setIsGeneratingVariations(false);
    }
  };

  const handleApplyFilter = async (filter) => {
    if (!isImageSelected || !canvas) return;

    setIsApplyingFilter(true);
    setError("");

    try {
      if (!isApiConfigured()) {
        throw new Error("Please configure at least one AI provider API key in .env.local");
      }

      const imageUrl = selectedObject.getSrc();
      const resultUrl = await generateImageVariations(imageUrl, filter.prompt, 0.6, selectedVariationModel);

      if (resultUrl) {
        await replaceImageOnCanvas(resultUrl, selectedObject);
      }
    } catch (err) {
      console.error("Filter application failed:", err);
      setError(err.message);
    } finally {
      setIsApplyingFilter(false);
    }
  };

  const apiConfigured = isApiConfigured();

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Wand2 className="w-5 h-5" />
          AI Tools
        </h3>
        <p className="text-sm text-gray-600">AI-powered image generation and editing</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 m-2">
          <TabsTrigger value="generate" className="text-xs">
            <Sparkles className="w-3 h-3 mr-1" />
            Generate
          </TabsTrigger>
          <TabsTrigger value="edit" className="text-xs">
            <Palette className="w-3 h-3 mr-1" />
            Edit
          </TabsTrigger>
          <TabsTrigger value="enhance" className="text-xs">
            <ArrowUp className="w-3 h-3 mr-1" />
            Enhance
          </TabsTrigger>
          <TabsTrigger value="settings" className="text-xs">
            <Settings className="w-3 h-3 mr-1" />
            Settings
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-y-auto">
          {/* Generate Tab */}
          <TabsContent value="generate" className="m-0 p-4 space-y-4">
            {/* Model Selection */}
            <div className="space-y-2">
              <Label htmlFor="model">AI Model</Label>
              {availableModels.length > 0 ? (
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableModels.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        {model.name} {model.default && "(Default)"} - {model.provider}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    No AI models available. Configure at least one provider in the Settings tab.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => setActiveTab("settings")}
                  >
                    Go to Settings
                  </Button>
                </div>
              )}
            </div>

            {/* Prompt Input */}
            <div className="space-y-2">
              <Label htmlFor="prompt">Prompt</Label>
              <Textarea
                id="prompt"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="A beautiful landscape with mountains and a lake, sunset, high quality, detailed"
                rows={4}
                disabled={isGenerating}
              />
            </div>

            {/* Image Size */}
            <div className="space-y-2">
              <Label htmlFor="size">Size</Label>
              <Select value={imageSize} onValueChange={setImageSize}>
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {sizeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Generate Button */}
            <Button
              onClick={!apiConfigured ? () => setActiveTab("settings") : handleGenerate}
              disabled={isGenerating || (!apiConfigured ? false : !prompt.trim() || availableModels.length === 0)}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : !apiConfigured ? (
                <>
                  <Settings className="mr-2 h-4 w-4" />
                  Configure AI Provider
                </>
              ) : availableModels.length === 0 ? (
                "No Models Available"
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate Image
                </>
              )}
            </Button>

            {/* Quick Setup Guide */}
            {!apiConfigured && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Quick Setup</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>1. Click "Configure AI Provider" above</p>
                  <p>2. Choose Together AI (recommended)</p>
                  <p>3. Get your free API key</p>
                  <p>4. Add it to .env.local and restart</p>
                </div>
              </div>
            )}
          </TabsContent>

          {/* Edit Tab */}
          <TabsContent value="edit" className="m-0 p-4 space-y-4">
            {!apiConfigured ? (
              <div className="text-center py-8">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800 text-sm mb-3">Configure an AI provider to use editing features</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab("settings")}
                  >
                    Configure Provider
                  </Button>
                </div>
              </div>
            ) : !isImageSelected ? (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">Select an image to edit with AI</p>
              </div>
            ) : (
              <>
                {/* Generate Variations */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Generate Variations</h4>
                  <p className="text-sm text-gray-600 mb-3">Create variations of the selected image</p>
                  <div className="space-y-3">
                    {/* Model Selection for Variations */}
                    {availableVariationModels.length > 0 ? (
                      <div className="space-y-2">
                        <Label htmlFor="variation-model">AI Model</Label>
                        <Select value={selectedVariationModel} onValueChange={setSelectedVariationModel}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a model" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableVariationModels.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                {model.name} - {model.provider}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    ) : (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p className="text-sm text-yellow-800">
                          No image-to-image models available. Configure Together AI for FLUX Kontext models.
                        </p>
                      </div>
                    )}

                    <Textarea
                      value={variationPrompt}
                      onChange={(e) => setVariationPrompt(e.target.value)}
                      placeholder="Describe how to modify the image..."
                      rows={3}
                      disabled={isGeneratingVariations}
                    />
                    <Button
                      onClick={handleGenerateVariations}
                      disabled={isGeneratingVariations || !apiConfigured || availableVariationModels.length === 0}
                      className="w-full"
                    >
                      {isGeneratingVariations ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : availableVariationModels.length === 0 ? (
                        "No Models Available"
                      ) : (
                        <>
                          <Palette className="mr-2 h-4 w-4" />
                          Generate Variations
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* AI Style Filters */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">AI Style Filters</h4>
                  <p className="text-sm text-gray-600 mb-3">Apply AI-powered style filters</p>
                  <div className="grid grid-cols-2 gap-2">
                    {aiFilters.map((filter) => (
                      <Button
                        key={filter.name}
                        onClick={() => handleApplyFilter(filter)}
                        disabled={isApplyingFilter || !apiConfigured}
                        variant="outline"
                        size="sm"
                      >
                        {filter.name}
                      </Button>
                    ))}
                  </div>
                </div>
              </>
            )}
          </TabsContent>

          {/* Enhance Tab */}
          <TabsContent value="enhance" className="m-0 p-4 space-y-4">
            {!apiConfigured ? (
              <div className="text-center py-8">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800 text-sm mb-3">Configure an AI provider to use enhancement features</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab("settings")}
                  >
                    Configure Provider
                  </Button>
                </div>
              </div>
            ) : !isImageSelected ? (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">Select an image to enhance with AI</p>
              </div>
            ) : (
              <>
                {/* Remove Background */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Remove Background</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    {selectedProvider === AI_PROVIDERS.TOGETHER
                      ? "Remove background using ClipDrop API (professional quality)"
                      : "Automatically remove the background from your image"}
                  </p>
                  <Button
                    onClick={handleRemoveBackground}
                    disabled={isRemovingBg}
                    className="w-full"
                    variant="destructive"
                  >
                    {isRemovingBg ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Removing...
                      </>
                    ) : (
                      <>
                        <Scissors className="mr-2 h-4 w-4" />
                        Remove Background
                      </>
                    )}
                  </Button>
                </div>

                {/* Upscale Image */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Upscale Image</h4>
                  <p className="text-sm text-gray-600 mb-3">Increase image resolution and quality</p>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="scale">Scale Factor</Label>
                      <Select value={upscaleFactor} onValueChange={setUpscaleFactor}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {upscaleOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      onClick={handleUpscale}
                      disabled={isUpscaling}
                      className="w-full"
                    >
                      {isUpscaling ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Upscaling...
                        </>
                      ) : (
                        <>
                          <ArrowUp className="mr-2 h-4 w-4" />
                          Upscale Image
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="m-0 p-4 space-y-4">
            {/* Provider Selection */}
            {availableProviders.length > 0 ? (
              <div className="space-y-2">
                <Label htmlFor="provider">AI Provider</Label>
                <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select provider" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableProviders.map((provider) => (
                      <SelectItem key={provider.id} value={provider.id}>
                        {provider.name} {provider.default && "(Default)"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">No Providers Configured</h4>
                <p className="text-sm text-yellow-800">
                  Configure at least one AI provider below to use AI features.
                </p>
              </div>
            )}

            {/* Provider Configuration */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Provider Configuration</h4>

              {/* Together AI */}
              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="font-medium text-gray-900">Together AI (Recommended)</h5>
                    <p className="text-sm text-gray-600">FLUX Schnell, Dev & Kontext models (Dev is cheaper)</p>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs font-medium ${
                    isApiConfigured(AI_PROVIDERS.TOGETHER)
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {isApiConfigured(AI_PROVIDERS.TOGETHER) ? 'Configured' : 'Not Configured'}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">1. Get API Key:</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open('https://together.ai', '_blank')}
                    >
                      Visit Together.ai
                    </Button>
                  </div>

                  <div className="space-y-1">
                    <span className="text-sm font-medium">2. Add to .env.local:</span>
                    <div className="text-xs font-mono bg-gray-100 p-2 rounded border">
                      TOGETHER_API_KEY="your-together-api-key-here"<br/>
                      NEXT_PUBLIC_TOGETHER_API_KEY="your-together-api-key-here"
                    </div>
                  </div>
                </div>
              </div>

              {/* Fal.ai */}
              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="font-medium text-gray-900">Fal.ai</h5>
                    <p className="text-sm text-gray-600">FLUX Pro, background removal, upscaling</p>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs font-medium ${
                    isApiConfigured(AI_PROVIDERS.FAL)
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {isApiConfigured(AI_PROVIDERS.FAL) ? 'Configured' : 'Not Configured'}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">1. Get API Key:</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open('https://fal.ai', '_blank')}
                    >
                      Visit Fal.ai
                    </Button>
                  </div>

                  <div className="space-y-1">
                    <span className="text-sm font-medium">2. Add to .env.local:</span>
                    <div className="text-xs font-mono bg-gray-100 p-2 rounded border">
                      FAL_KEY="your-fal-api-key-here"<br/>
                      NEXT_PUBLIC_FAL_KEY="your-fal-api-key-here"
                    </div>
                  </div>
                </div>
              </div>

              {/* Replicate */}
              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="font-medium text-gray-900">Replicate</h5>
                    <p className="text-sm text-gray-600">Legacy models, stable diffusion</p>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs font-medium ${
                    isApiConfigured(AI_PROVIDERS.REPLICATE)
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {isApiConfigured(AI_PROVIDERS.REPLICATE) ? 'Configured' : 'Not Configured'}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">1. Get API Key:</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open('https://replicate.com', '_blank')}
                    >
                      Visit Replicate.com
                    </Button>
                  </div>

                  <div className="space-y-1">
                    <span className="text-sm font-medium">2. Add to .env.local:</span>
                    <div className="text-xs font-mono bg-gray-100 p-2 rounded border">
                      REPLICATE_API_TOKEN="your-replicate-api-token-here"<br/>
                      NEXT_PUBLIC_REPLICATE_API_TOKEN="your-replicate-api-token-here"
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* General Instructions */}
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Setup Instructions</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p>1. Choose at least one AI provider above</p>
                <p>2. Sign up and get your API key</p>
                <p>3. Add the API key to your .env.local file</p>
                <p>4. Restart the development server</p>
                <p>5. Refresh this page to see available models</p>
              </div>
            </div>

            {/* Restart Notice */}
            {!apiConfigured && (
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2">⚠️ Restart Required</h4>
                <p className="text-sm text-orange-800">
                  After adding API keys to .env.local, restart your development server with <code className="bg-orange-100 px-1 rounded">npm run dev</code>
                </p>
              </div>
            )}
          </TabsContent>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mx-4 mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-red-600 mr-2 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-800">Operation Failed</p>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* API Configuration Warning */}
        {!apiConfigured && (
          <div className="mx-4 mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-800">API Configuration Required</p>
                <p className="text-sm text-yellow-700">
                  Configure at least one AI provider in the Settings tab to use AI features.
                </p>
              </div>
            </div>
          </div>
        )}
      </Tabs>
    </div>
  );
}
