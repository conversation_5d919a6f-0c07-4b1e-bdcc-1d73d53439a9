import { NextResponse } from 'next/server';
import { saveDesign, getUserDesigns, getPublicDesigns } from '@/services/designService';

// GET /api/designs - Get designs (user's designs or public designs)
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type') || 'user'; // 'user' or 'public'

    let result;
    
    if (type === 'public') {
      result = await getPublicDesigns(page, limit);
    } else if (userId) {
      result = await getUserDesigns(userId, page, limit);
    } else {
      return NextResponse.json(
        { success: false, error: 'User ID is required for user designs' },
        { status: 400 }
      );
    }

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/designs:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/designs - Create a new design
export async function POST(request) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.canvasData || !body.userId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: name, canvasData, and userId are required' 
        },
        { status: 400 }
      );
    }

    // Generate thumbnail from canvas data if not provided
    let thumbnail = body.thumbnail;
    if (!thumbnail && body.canvasData) {
      // You can implement thumbnail generation here
      // For now, we'll use a placeholder or the provided thumbnail
      thumbnail = body.thumbnail || null;
    }

    const designData = {
      name: body.name,
      description: body.description || null,
      width: body.width || 800,
      height: body.height || 600,
      canvasData: body.canvasData,
      thumbnail: thumbnail,
      isPublic: body.isPublic || false,
      userId: body.userId,
    };

    const result = await saveDesign(designData);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/designs:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
