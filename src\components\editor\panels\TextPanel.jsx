"use client";

import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { addTextToCanvas } from "@/fabric/fabric-utils";
import { X, Type, Heading1, Heading2, AlignLeft, AlignCenter, AlignRight } from "lucide-react";

export default function TextPanel() {
  const { setActivePanel, canvas, markAsModified } = useEditorStore();

  const handleClose = () => {
    setActivePanel('select');
  };

  const addText = async (text, options = {}) => {
    if (!canvas) return;
    
    try {
      await addTextToCanvas(canvas, text, options);
      markAsModified();
    } catch (error) {
      console.error("Error adding text:", error);
    }
  };

  const textPresets = [
    {
      id: 'heading',
      label: 'Add a heading',
      text: 'Add a heading',
      icon: Heading1,
      options: {
        fontSize: 48,
        fontWeight: 'bold',
        fontFamily: 'Arial',
      }
    },
    {
      id: 'subheading',
      label: 'Add a subheading',
      text: 'Add a subheading',
      icon: Heading2,
      options: {
        fontSize: 32,
        fontWeight: '600',
        fontFamily: 'Arial',
      }
    },
    {
      id: 'body',
      label: 'Add body text',
      text: 'Add a little bit of body text',
      icon: Type,
      options: {
        fontSize: 18,
        fontWeight: 'normal',
        fontFamily: 'Arial',
      }
    },
  ];

  const fontFamilies = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Georgia',
    'Verdana',
    'Courier New',
    'Impact',
    'Comic Sans MS',
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Text</h3>
          <p className="text-xs text-gray-500">Add text to your design</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Text Presets */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Add text</h4>
          
          {textPresets.map((preset) => {
            const Icon = preset.icon;
            return (
              <button
                key={preset.id}
                onClick={() => addText(preset.text, preset.options)}
                className="w-full flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
              >
                <Icon className="w-5 h-5 text-gray-600" />
                <span className="text-sm text-gray-700">{preset.label}</span>
              </button>
            );
          })}
        </div>

        {/* Font Families */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Font combinations</h4>
          
          <div className="grid grid-cols-1 gap-2">
            {fontFamilies.map((font) => (
              <button
                key={font}
                onClick={() => addText('Sample Text', { fontFamily: font, fontSize: 24 })}
                className="w-full p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
              >
                <div style={{ fontFamily: font }} className="text-sm">
                  {font}
                </div>
                <div style={{ fontFamily: font }} className="text-xs text-gray-500 mt-1">
                  The quick brown fox jumps
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Text Effects */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Text effects</h4>
          
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => addText('BOLD TEXT', { fontSize: 32, fontWeight: 'bold' })}
              className="p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <div className="text-sm font-bold">Bold</div>
            </button>
            
            <button
              onClick={() => addText('Italic Text', { fontSize: 32, fontStyle: 'italic' })}
              className="p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <div className="text-sm italic">Italic</div>
            </button>
            
            <button
              onClick={() => addText('Shadow Text', { 
                fontSize: 32, 
                shadow: 'rgba(0,0,0,0.3) 2px 2px 4px' 
              })}
              className="p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <div className="text-sm" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.3)' }}>
                Shadow
              </div>
            </button>
            
            <button
              onClick={() => addText('Outline Text', { 
                fontSize: 32, 
                stroke: '#000000',
                strokeWidth: 2,
                fill: 'transparent'
              })}
              className="p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <div className="text-sm" style={{ 
                WebkitTextStroke: '1px black',
                WebkitTextFillColor: 'transparent'
              }}>
                Outline
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
