"use client";

import { useState, useEffect, useRef } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { X, Grid, Ruler, Download, Trash2 } from "lucide-react";

export default function SettingsPanel() {
  const {
    setActivePanel,
    canvas,
    markAsModified,
    canvasWidth,
    canvasHeight,
    canvasBackgroundColor,
    setCanvasSize,
    setCanvasBackgroundColor
  } = useEditorStore();

  const [localWidth, setLocalWidth] = useState(canvasWidth);
  const [localHeight, setLocalHeight] = useState(canvasHeight);
  const [showGrid, setShowGrid] = useState(false);
  const [showRulers, setShowRulers] = useState(false);
  const updateTimeoutRef = useRef(null);

  // Handle grid toggle
  const handleGridToggle = () => {
    const newShowGrid = !showGrid;
    setShowGrid(newShowGrid);

    // Update the canvas container grid visibility
    if (canvas && canvas.wrapperEl) {
      const container = canvas.wrapperEl.parentElement;
      if (container) {
        const gridElement = container.querySelector('.workspace-grid');
        if (gridElement) {
          gridElement.style.opacity = newShowGrid ? '0.3' : '0.1';
        }
      }
    }
  };

  // Handle rulers toggle
  const handleRulersToggle = () => {
    const newShowRulers = !showRulers;
    setShowRulers(newShowRulers);

    // Update the canvas container guidelines visibility
    if (canvas && canvas.wrapperEl) {
      const container = canvas.wrapperEl.parentElement;
      if (container) {
        const guidelines = container.querySelectorAll('.workspace-guideline');
        guidelines.forEach(guideline => {
          guideline.style.opacity = newShowRulers ? '0.4' : '0.1';
        });
      }
    }
  };

  // Debounced update function
  const debouncedUpdateDesignArea = (width, height) => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    updateTimeoutRef.current = setTimeout(() => {
      try {
        // Update store values (this will automatically update the design area)
        setCanvasSize(width, height);
        markAsModified();
      } catch (error) {
        console.error('Failed to update design area:', error);
      }
    }, 300); // 300ms debounce
  };

  // Update local state when store values change
  useEffect(() => {
    setLocalWidth(canvasWidth);
    setLocalHeight(canvasHeight);
  }, [canvasWidth, canvasHeight]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const handleClose = () => {
    setActivePanel('select');
  };

  const handleCanvasResize = () => {
    // Update the store values (this will automatically update the design area)
    setCanvasSize(localWidth, localHeight);
    markAsModified();
  };

  const handleBackgroundColorChange = (color) => {
    // Update the store value (this will automatically update the design area)
    setCanvasBackgroundColor(color);
    markAsModified();
  };

  const handleClearCanvas = async () => {
    if (!canvas) return;

    if (confirm("Are you sure you want to clear all objects? This action cannot be undone.")) {
      try {
        // Import the utility function
        const { getUserObjects } = await import("@/fabric/fabric-utils");

        // Get all user objects (exclude design area elements)
        const objectsToRemove = getUserObjects(canvas);

        // Remove only user objects, keep design area
        objectsToRemove.forEach(obj => canvas.remove(obj));
        canvas.renderAll();
        markAsModified();
      } catch (error) {
        console.error('Failed to clear objects:', error);
      }
    }
  };

  const handleExportSettings = async () => {
    if (!canvas) return;

    try {
      // Import the design area export function
      const { exportDesignArea } = await import("@/fabric/fabric-utils");

      // Export only the design area
      const dataURL = exportDesignArea(canvas);

      if (dataURL) {
        const link = document.createElement('a');
        link.download = `design-export-${Date.now()}.png`;
        link.href = dataURL;
        link.click();
      } else {
        alert('Could not export design area. Please make sure you have a design area set up.');
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    }
  };

  const presetSizes = [
    { name: "Instagram Post", width: 1080, height: 1080 },
    { name: "Instagram Story", width: 1080, height: 1920 },
    { name: "Facebook Post", width: 1200, height: 630 },
    { name: "Twitter Header", width: 1500, height: 500 },
    { name: "A4 Portrait", width: 595, height: 842 },
    { name: "A4 Landscape", width: 842, height: 595 },
    { name: "Custom", width: 800, height: 600 },
  ];

  const backgroundPresets = [
    "#ffffff", "#000000", "#f3f4f6", "#1f2937",
    "#fef3c7", "#dbeafe", "#fce7f3", "#d1fae5",
    "#e0e7ff", "#fed7d7", "#f0fff4", "#fdf2f8"
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Design Area Settings</h3>
          <p className="text-xs text-gray-500">Control the white design area (export boundary)</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Design Area Size */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Design Area Size</Label>
          
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-1">
                <Label className="text-xs">Width</Label>
                <Input
                  type="number"
                  value={localWidth}
                  onChange={(e) => {
                    const newWidth = parseInt(e.target.value) || 800;
                    setLocalWidth(newWidth);
                    debouncedUpdateDesignArea(newWidth, localHeight);
                  }}
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Height</Label>
                <Input
                  type="number"
                  value={localHeight}
                  onChange={(e) => {
                    const newHeight = parseInt(e.target.value) || 600;
                    setLocalHeight(newHeight);
                    debouncedUpdateDesignArea(localWidth, newHeight);
                  }}
                  className="h-8 text-xs"
                />
              </div>
            </div>
            
            <Button onClick={handleCanvasResize} size="sm" className="w-full">
              Update Design Area
            </Button>
          </div>
        </div>

        {/* Preset Sizes */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Preset Sizes</Label>
          
          <div className="grid grid-cols-1 gap-2">
            {presetSizes.map((preset) => (
              <button
                key={preset.name}
                onClick={() => {
                  setLocalWidth(preset.width);
                  setLocalHeight(preset.height);

                  // Apply the preset size (this will automatically update the design area)
                  setCanvasSize(preset.width, preset.height);
                  markAsModified();
                }}
                className="text-left p-2 text-xs border rounded hover:bg-gray-50 transition-colors"
              >
                <div className="font-medium">{preset.name}</div>
                <div className="text-gray-500">{preset.width} × {preset.height}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Background Color */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Background Color</Label>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={canvasBackgroundColor}
                onChange={(e) => handleBackgroundColorChange(e.target.value)}
                className="w-8 h-8 rounded border"
              />
              <Input
                value={canvasBackgroundColor}
                onChange={(e) => handleBackgroundColorChange(e.target.value)}
                className="flex-1 h-8 text-xs"
              />
            </div>
            
            <div className="grid grid-cols-6 gap-2">
              {backgroundPresets.map((color) => (
                <button
                  key={color}
                  onClick={() => handleBackgroundColorChange(color)}
                  className={`w-8 h-8 rounded border-2 ${
                    canvasBackgroundColor === color ? 'border-blue-500' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
          </div>
        </div>

        {/* View Options */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">View Options</Label>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Grid className="w-4 h-4 text-gray-500" />
                <span className="text-sm">Show Grid</span>
              </div>
              <button
                onClick={handleGridToggle}
                className={`w-10 h-6 rounded-full transition-colors ${
                  showGrid ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              >
                <div
                  className={`w-4 h-4 bg-white rounded-full transition-transform ${
                    showGrid ? 'translate-x-5' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Ruler className="w-4 h-4 text-gray-500" />
                <span className="text-sm">Show Rulers</span>
              </div>
              <button
                onClick={handleRulersToggle}
                className={`w-10 h-6 rounded-full transition-colors ${
                  showRulers ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              >
                <div
                  className={`w-4 h-4 bg-white rounded-full transition-transform ${
                    showRulers ? 'translate-x-5' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Actions</Label>
          
          <div className="space-y-2">
            <Button
              onClick={handleExportSettings}
              variant="outline"
              size="sm"
              className="w-full justify-start"
            >
              <Download className="w-4 h-4 mr-2" />
              Export Design Area
            </Button>

            <Button
              onClick={handleClearCanvas}
              variant="outline"
              size="sm"
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Clear All Objects
            </Button>
          </div>
        </div>

        {/* Info */}
        <div className="bg-blue-50 rounded-lg p-3">
          <h5 className="text-sm font-medium text-blue-900 mb-1">Design Area Info</h5>
          <div className="text-xs text-blue-700 space-y-1">
            <div>Size: {localWidth} × {localHeight} pixels</div>
            <div>Background: {canvasBackgroundColor}</div>
            <div>Objects: {(() => {
              try {
                const { getUserObjects } = require("@/fabric/fabric-utils");
                return getUserObjects(canvas).length;
              } catch {
                return canvas?.getObjects()?.filter(obj =>
                  !obj.isDesignArea &&
                  !obj.isDesignAreaElement &&
                  obj.id !== 'design-area' &&
                  !obj.id?.startsWith('corner-indicator') &&
                  !obj.excludeFromExport
                ).length || 0;
              }
            })()}</div>
            <div className="mt-2 text-blue-600 font-medium">
              ✓ Controls the white design area
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
