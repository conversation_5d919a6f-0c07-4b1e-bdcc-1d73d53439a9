import { NextResponse } from 'next/server';
import { getDesign, updateDesign, deleteDesign } from '@/services/designService';

// GET /api/designs/[id] - Get a specific design
export async function GET(request, { params }) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Design ID is required' },
        { status: 400 }
      );
    }

    const result = await getDesign(id);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Design not found' ? 404 : 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/designs/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/designs/[id] - Update a specific design
export async function PUT(request, { params }) {
  try {
    const { id } = params;
    const body = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Design ID is required' },
        { status: 400 }
      );
    }

    // Validate that at least one field is being updated
    const allowedFields = ['name', 'description', 'width', 'height', 'canvasData', 'thumbnail', 'isPublic'];
    const updateData = {};
    
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    });

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    const result = await updateDesign(id, updateData);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in PUT /api/designs/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/designs/[id] - Delete a specific design
export async function DELETE(request, { params }) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Design ID is required' },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    const result = await deleteDesign(id, userId);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Design not found or access denied' ? 404 : 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/designs/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
