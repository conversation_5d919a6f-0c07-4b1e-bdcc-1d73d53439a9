/**
 * DrawingSessionManager - Manages grouping of drawing paths into sessions
 * 
 * Features:
 * - Groups multiple path strokes into logical drawing sessions
 * - Handles session timeouts and boundaries
 * - Integrates with layers panel for grouped display
 * - Maintains individual path tracking for undo/redo
 */

export class DrawingSessionManager {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.options = {
      sessionTimeout: options.sessionTimeout || 3000, // 3 seconds default
      maxSessionPaths: options.maxSessionPaths || 50, // Max paths per session
      ...options
    };
    
    // Current drawing session state
    this.currentSession = null;
    this.sessionCounter = 0;
    this.timeoutId = null;
    this.isDrawingActive = false;
    
    // Session storage
    this.sessions = new Map(); // sessionId -> session data
    this.pathToSession = new Map(); // pathId -> sessionId
    
    // Callbacks
    this.onSessionStart = options.onSessionStart || (() => {});
    this.onSessionEnd = options.onSessionEnd || (() => {});
    this.onPathAdded = options.onPathAdded || (() => {});
    
    console.log('DrawingSessionManager initialized with options:', this.options);
  }

  /**
   * Start a new drawing session
   */
  startSession() {
    // End current session if exists
    if (this.currentSession) {
      this.endSession();
    }
    
    this.sessionCounter++;
    const sessionId = `drawing-session-${this.sessionCounter}-${Date.now()}`;
    
    this.currentSession = {
      id: sessionId,
      name: `Drawing Session ${this.sessionCounter}`,
      paths: [],
      startTime: Date.now(),
      isActive: true,
      visible: true,
      locked: false
    };
    
    this.sessions.set(sessionId, this.currentSession);
    this.isDrawingActive = true;
    
    console.log(`🎨 Started drawing session: ${sessionId}`);
    this.onSessionStart(this.currentSession);
    
    return this.currentSession;
  }

  /**
   * Add a path to the current session
   */
  addPathToSession(pathObject) {
    if (!this.currentSession) {
      this.startSession();
    }
    
    const pathData = {
      id: pathObject.id,
      object: pathObject,
      addedAt: Date.now()
    };
    
    this.currentSession.paths.push(pathData);
    this.pathToSession.set(pathObject.id, this.currentSession.id);
    
    console.log(`➕ Added path ${pathObject.id} to session ${this.currentSession.id}`);
    this.onPathAdded(pathData, this.currentSession);
    
    // Reset timeout
    this.resetSessionTimeout();
    
    // Check if session should be split (too many paths)
    if (this.currentSession.paths.length >= this.options.maxSessionPaths) {
      console.log(`📊 Session ${this.currentSession.id} reached max paths, ending session`);
      this.endSession();
    }
    
    return this.currentSession;
  }

  /**
   * End the current drawing session
   */
  endSession(reason = 'manual') {
    if (!this.currentSession) return null;
    
    const session = this.currentSession;
    session.isActive = false;
    session.endTime = Date.now();
    session.endReason = reason;
    
    console.log(`🏁 Ended drawing session: ${session.id} (reason: ${reason}, paths: ${session.paths.length})`);
    
    this.clearSessionTimeout();
    this.onSessionEnd(session);
    
    this.currentSession = null;
    this.isDrawingActive = false;
    
    return session;
  }

  /**
   * Reset the session timeout
   */
  resetSessionTimeout() {
    this.clearSessionTimeout();
    
    this.timeoutId = setTimeout(() => {
      if (this.currentSession) {
        console.log(`⏰ Session timeout reached for ${this.currentSession.id}`);
        this.endSession('timeout');
      }
    }, this.options.sessionTimeout);
  }

  /**
   * Clear the session timeout
   */
  clearSessionTimeout() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * Get session for a specific path
   */
  getSessionForPath(pathId) {
    const sessionId = this.pathToSession.get(pathId);
    return sessionId ? this.sessions.get(sessionId) : null;
  }

  /**
   * Get all sessions
   */
  getAllSessions() {
    return Array.from(this.sessions.values());
  }

  /**
   * Get active sessions (sessions with paths still on canvas)
   */
  getActiveSessions() {
    const canvasObjects = this.canvas.getObjects();
    const activePathIds = new Set(
      canvasObjects
        .filter(obj => obj.type === 'path' && obj.id)
        .map(obj => obj.id)
    );
    
    return this.getAllSessions().filter(session => 
      session.paths.some(path => activePathIds.has(path.id))
    );
  }

  /**
   * Toggle session visibility
   */
  toggleSessionVisibility(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) return false;
    
    session.visible = !session.visible;
    
    // Apply to all paths in session
    session.paths.forEach(pathData => {
      const obj = this.canvas.getObjects().find(o => o.id === pathData.id);
      if (obj) {
        obj.set('visible', session.visible);
      }
    });
    
    this.canvas.renderAll();
    console.log(`👁️ Toggled session ${sessionId} visibility: ${session.visible}`);
    return session.visible;
  }

  /**
   * Toggle session lock state
   */
  toggleSessionLock(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) return false;
    
    session.locked = !session.locked;
    
    // Apply to all paths in session
    session.paths.forEach(pathData => {
      const obj = this.canvas.getObjects().find(o => o.id === pathData.id);
      if (obj) {
        obj.set('selectable', !session.locked);
        obj.set('evented', !session.locked);
      }
    });
    
    this.canvas.renderAll();
    console.log(`🔒 Toggled session ${sessionId} lock: ${session.locked}`);
    return session.locked;
  }

  /**
   * Delete entire session
   */
  deleteSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) return false;
    
    // Remove all paths from canvas
    const pathsToRemove = [];
    session.paths.forEach(pathData => {
      const obj = this.canvas.getObjects().find(o => o.id === pathData.id);
      if (obj) {
        pathsToRemove.push(obj);
      }
      this.pathToSession.delete(pathData.id);
    });
    
    pathsToRemove.forEach(obj => this.canvas.remove(obj));
    this.sessions.delete(sessionId);
    
    this.canvas.renderAll();
    console.log(`🗑️ Deleted session ${sessionId} with ${pathsToRemove.length} paths`);
    return true;
  }

  /**
   * Handle tool change - end current session
   */
  onToolChange(newTool) {
    if (newTool !== 'draw' && this.currentSession) {
      console.log(`🔧 Tool changed to ${newTool}, ending drawing session`);
      this.endSession('tool-change');
    }
  }

  /**
   * Handle drawing mode change
   */
  onDrawingModeChange(isDrawing) {
    if (!isDrawing && this.currentSession) {
      console.log('🎨 Drawing mode disabled, ending session');
      this.endSession('drawing-disabled');
    }
  }

  /**
   * Clean up resources
   */
  destroy() {
    this.clearSessionTimeout();
    this.endSession('destroy');
    this.sessions.clear();
    this.pathToSession.clear();
    console.log('DrawingSessionManager destroyed');
  }

  /**
   * Get session statistics
   */
  getSessionStats() {
    const sessions = this.getAllSessions();
    const activeSessions = this.getActiveSessions();
    
    return {
      totalSessions: sessions.length,
      activeSessions: activeSessions.length,
      totalPaths: sessions.reduce((sum, session) => sum + session.paths.length, 0),
      currentSession: this.currentSession?.id || null,
      isDrawingActive: this.isDrawingActive
    };
  }
}
