"use client";

import { useState, useEffect } from "react";
import { useEditorStore } from "@/store";
import { useTemplateStore } from "@/store/templateStore";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Save, Loader2 } from "lucide-react";

export default function SaveDialog({ isOpen, onClose, onSave }) {
  const { name, description: storeDescription, saveToDatabase, saveStatus, currentDesignId, canvas, isTemplateMode } = useEditorStore();
  const { currentTemplate, updateTemplate } = useTemplateStore();

  // Determine if we're editing a template
  const isEditingTemplate = isTemplateMode && currentTemplate;

  // Initialize form with template name if editing template, otherwise use design name
  const [designName, setDesignName] = useState(
    isEditingTemplate ? currentTemplate.name : (name || "Untitled Design")
  );
  const [description, setDescription] = useState(
    isEditingTemplate ? (currentTemplate.metadata?.description || "") : (storeDescription || "")
  );
  const [isPublic, setIsPublic] = useState(
    isEditingTemplate ? (currentTemplate.metadata?.isPublic || false) : false
  );
  const [isSaving, setIsSaving] = useState(false);

  // For demo purposes, using a mock user ID
  // In a real app, you'd get this from authentication
  const mockUserId = "demo_user";

  // Update form values when dialog opens or template changes
  useEffect(() => {
    if (isOpen) {
      if (isEditingTemplate && currentTemplate) {
        setDesignName(currentTemplate.name);
        setDescription(currentTemplate.metadata?.description || "");
        setIsPublic(currentTemplate.metadata?.isPublic || false);
      } else {
        setDesignName(name || "Untitled Design");
        setDescription("");
        setIsPublic(false);
      }
    }
  }, [isOpen, isEditingTemplate, currentTemplate, name]);

  const handleSave = async () => {
    if (!designName.trim()) {
      alert(isEditingTemplate ? "Please enter a template name" : "Please enter a design name");
      return;
    }

    setIsSaving(true);

    try {
      if (isEditingTemplate) {
        // Save as template update to database
        if (!canvas) {
          alert('Canvas not available for template save');
          return;
        }

        // Get canvas data and filter out design area elements
        const canvasData = canvas.toJSON();
        const templateObjects = canvasData.objects.filter(obj =>
          !obj.isDesignArea &&
          !obj.isDesignAreaElement &&
          obj.id !== 'design-area' &&
          !obj.id?.startsWith('corner-indicator')
        );

        const templateData = {
          name: designName.trim(),
          description: description.trim(),
          category: currentTemplate.category || 'general',
          width: canvas.width || 800,
          height: canvas.height || 600,
          canvasData: {
            width: canvas.width || 800,
            height: canvas.height || 600,
            backgroundColor: canvasData.backgroundColor || '#ffffff',
            objects: templateObjects
          },
          isPublic: isPublic,
          isPremium: false,
          tags: currentTemplate.metadata?.tags || [],
          editableZones: currentTemplate.editableZones || null
        };

        try {
          const response = await fetch(`/api/templates/${currentTemplate.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(templateData),
          });

          const result = await response.json();

          if (result.success) {
            // Also update the local template store
            const localTemplateData = {
              name: designName.trim(),
              canvas: {
                width: canvas.width || 800,
                height: canvas.height || 600,
                backgroundColor: canvasData.backgroundColor || '#ffffff',
                objects: templateObjects
              },
              metadata: {
                description: description.trim(),
                isPublic: isPublic,
                tags: currentTemplate.metadata?.tags || []
              }
            };
            updateTemplate(currentTemplate.id, localTemplateData);

            onSave?.(result.template);
            onClose();
            alert('Template updated successfully!');
          } else {
            alert(`Failed to update template: ${result.error}`);
          }
        } catch (error) {
          console.error('Error updating template:', error);
          alert('An error occurred while updating the template');
        }
      } else {
        // Save as design to database
        const result = await saveToDatabase(mockUserId, designName.trim(), isPublic, description.trim());

        if (result.success) {
          onSave?.(result.design);
          onClose();
          // Reset form
          setDescription("");
          setIsPublic(false);

          // Show success message
          alert('Design saved to database successfully!');
        } else {
          alert(`Failed to save design: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('Error saving:', error);
      alert('An error occurred while saving');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    if (!isSaving) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            {isEditingTemplate
              ? 'Update Template'
              : (currentDesignId ? 'Update Design' : 'Save Design')
            }
          </DialogTitle>
          <DialogDescription>
            {isEditingTemplate
              ? 'Update your template with the latest changes.'
              : (currentDesignId
                ? 'Update your design with the latest changes.'
                : 'Save your design to the database so you can access it later.'
              )
            }
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="design-name">
              {isEditingTemplate ? 'Template Name *' : 'Design Name *'}
            </Label>
            <Input
              id="design-name"
              value={designName}
              onChange={(e) => setDesignName(e.target.value)}
              placeholder={isEditingTemplate ? "Enter template name" : "Enter design name"}
              disabled={isSaving}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={isEditingTemplate
                ? "Optional description for your template"
                : "Optional description for your design"
              }
              rows={3}
              disabled={isSaving}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="public"
              checked={isPublic}
              onCheckedChange={setIsPublic}
              disabled={isSaving}
            />
            <Label htmlFor="public" className="text-sm">
              {isEditingTemplate ? 'Make this template public' : 'Make this design public'}
            </Label>
          </div>

          {isPublic && (
            <p className="text-xs text-gray-500">
              {isEditingTemplate
                ? 'Public templates can be viewed and used by other users.'
                : 'Public designs can be viewed and used by other users as templates.'
              }
            </p>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={isSaving || !designName.trim()}
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditingTemplate ? 'Updating...' : 'Saving...'}
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {isEditingTemplate
                  ? 'Update Template'
                  : (currentDesignId ? 'Update' : 'Save') + ' Design'
                }
              </>
            )}
          </Button>
        </DialogFooter>

        {/* Save Status Indicator */}
        {saveStatus && saveStatus !== "Saved" && (
          <div className="mt-2 text-center">
            <p className="text-sm text-gray-600">
              Status: {saveStatus}
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
