"use client";

import { useState, useEffect } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import HeaderObjectControlsSimple from "./HeaderObjectControlsSimple";
import SaveDialog from "./SaveDialog";
import {
  Download,
  Save,
  Undo,
  Redo,
  Eye,
  Edit3,
  Home,
  Database
} from "lucide-react";

export default function Header() {
  const {
    name,
    setName,
    saveStatus,
    isEditing,
    setIsEditing,
    canvas,
    saveToLocalStorage,
    undo,
    redo,
    canUndo,
    canRedo,
    undoRedoState, // Include this to trigger re-renders when undo/redo state changes
  } = useEditorStore();

  // Suppress unused variable warning - undoRedoState is used to trigger re-renders
  void undoRedoState;

  const [showSaveDialog, setShowSaveDialog] = useState(false);

  // Debug: Log when name changes
  useEffect(() => {
    console.log('Header: Name changed to:', name);
  }, [name]);

  const handleSave = () => {
    saveToLocalStorage();
  };

  const handleSaveToDatabase = () => {
    setShowSaveDialog(true);
  };

  const handleSaveDialogClose = () => {
    setShowSaveDialog(false);
  };

  const handleSaveSuccess = (design) => {
    console.log('Design saved successfully:', design);
    // You can add additional success handling here
  };

  const handleExport = async () => {
    if (!canvas) return;

    try {
      // Import the export function
      const { exportDesignArea } = await import("@/fabric/fabric-utils");

      // Export only the design area
      const dataURL = exportDesignArea(canvas);

      if (dataURL) {
        const link = document.createElement('a');
        link.download = `${name}.png`;
        link.href = dataURL;
        link.click();
      } else {
        // Fallback to full canvas export if design area export fails
        const fallbackDataURL = canvas.toDataURL({
          format: 'png',
          quality: 1,
          multiplier: 2,
        });

        const link = document.createElement('a');
        link.download = `${name}.png`;
        link.href = fallbackDataURL;
        link.click();
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleUndo = () => {
    undo();
  };

  const handleRedo = () => {
    redo();
  };



  return (
    <div className="flex flex-col">
      <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Logo/Home */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Home className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-semibold text-gray-900">Poster Editor</span>
          </div>

          {/* Design Name */}
          <div className="flex items-center space-x-2">
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-64 h-8 text-sm"
              placeholder="Design name"
            />
            <span className="text-xs text-gray-500">{saveStatus}</span>
          </div>
        </div>

      {/* Center Section - Tools */}
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleUndo}
          disabled={!canUndo()}
          className="h-8 w-8 p-0"
          title="Undo"
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRedo}
          disabled={!canRedo()}
          className="h-8 w-8 p-0"
          title="Redo"
        >
          <Redo className="h-4 w-4" />
        </Button>
        
        <div className="w-px h-6 bg-gray-300 mx-2" />
        
        <Button
          variant={isEditing ? "default" : "ghost"}
          size="sm"
          onClick={() => setIsEditing(!isEditing)}
          className="h-8"
        >
          {isEditing ? (
            <>
              <Edit3 className="h-4 w-4 mr-1" />
              Editing
            </>
          ) : (
            <>
              <Eye className="h-4 w-4 mr-1" />
              Viewing
            </>
          )}
        </Button>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSave}
          className="h-8"
          title="Save to browser storage"
        >
          <Save className="h-4 w-4 mr-1" />
          Save Local
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleSaveToDatabase}
          className="h-8"
          title="Save to database"
        >
          <Database className="h-4 w-4 mr-1" />
          Save to DB
        </Button>
        <Button
          variant="default"
          size="sm"
          onClick={handleExport}
          className="h-8"
        >
          <Download className="h-4 w-4 mr-1" />
          Export
        </Button>
      </div>
    </header>

      {/* Object Controls - Show when object is selected */}
      <HeaderObjectControlsSimple />

      {/* Save Dialog */}
      <SaveDialog
        isOpen={showSaveDialog}
        onClose={handleSaveDialogClose}
        onSave={handleSaveSuccess}
      />
    </div>
  );
}
