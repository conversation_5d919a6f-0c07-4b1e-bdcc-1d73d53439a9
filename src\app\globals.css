@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Editor specific styles */
.sidebar {
  @apply w-[72px] bg-white border-r flex flex-col items-center py-4 h-full;
}

.sidebar-item {
  @apply w-full flex flex-col items-center justify-center py-3 cursor-pointer hover:bg-gray-100 transition-colors;
}

.sidebar-item.active {
  @apply bg-blue-50 text-blue-600;
}

.sidebar-item-icon {
  @apply mb-1;
}

.sidebar-item-label {
  @apply text-xs font-medium;
}

.secondary-panel {
  @apply w-[320px] bg-white border-r h-full flex flex-col transition-all duration-300;
}

.panel-header {
  @apply flex items-center gap-2 p-4 border-b;
}

.back-button {
  @apply p-1 rounded-full hover:bg-gray-100;
}

.panel-title {
  @apply text-sm font-medium;
}

.panel-content {
  @apply p-4 flex-1 overflow-y-auto;
}

.collapse-button {
  @apply absolute top-1/2 -right-3 transform -translate-y-1/2 bg-white border rounded-full p-1 shadow-md;
}

.header-gradient {
  background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
}

.header-button {
  @apply px-3 py-1.5 rounded-md hover:bg-white/10 transition-colors;
}

/* Properties panel styles */
.color-swatch {
  @apply w-6 h-6 rounded-full border cursor-pointer;
}

.color-swatch.active {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Layers panel styles */
.layer-item {
  @apply flex items-center justify-between p-2 border-b hover:bg-gray-50 cursor-pointer;
}

.layer-item.active {
  @apply bg-blue-50;
}

.layer-item-name {
  @apply text-sm truncate flex-1;
}

.layer-item-actions {
  @apply flex items-center gap-1;
}

.layer-visibility-toggle {
  @apply p-1 rounded-full hover:bg-gray-200;
}

.layer-lock-toggle {
  @apply p-1 rounded-full hover:bg-gray-200;
}
