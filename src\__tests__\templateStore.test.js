/**
 * @jest-environment jsdom
 */

import { renderHook, act } from '@testing-library/react';
import { useTemplateStore } from '../store/templateStore';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('Template Store', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    
    // Reset store state
    const { result } = renderHook(() => useTemplateStore());
    act(() => {
      result.current.resetTemplateStore();
    });
  });

  describe('Template Management', () => {
    test('should create a new template', () => {
      const { result } = renderHook(() => useTemplateStore());

      const templateData = {
        name: 'Test Template',
        category: 'business',
        description: 'A test template',
        canvas: {
          width: 800,
          height: 600,
          backgroundColor: '#ffffff',
          objects: []
        },
        editableZones: []
      };

      act(() => {
        const newTemplate = result.current.createTemplate(templateData);
        expect(newTemplate).toBeDefined();
        expect(newTemplate.id).toBeDefined();
        expect(newTemplate.name).toBe('Test Template');
        expect(newTemplate.category).toBe('business');
        expect(newTemplate.metadata.createdAt).toBeDefined();
        expect(newTemplate.metadata.version).toBe(1);
      });

      expect(result.current.templates).toHaveLength(1);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'poster-templates',
        expect.any(String)
      );
    });

    test('should update an existing template', () => {
      const { result } = renderHook(() => useTemplateStore());

      // Create a template first
      let templateId;
      act(() => {
        const template = result.current.createTemplate({
          name: 'Original Template',
          category: 'business'
        });
        templateId = template.id;
      });

      // Update the template
      act(() => {
        const updatedTemplate = result.current.updateTemplate(templateId, {
          name: 'Updated Template',
          description: 'Updated description'
        });
        
        expect(updatedTemplate.name).toBe('Updated Template');
        expect(updatedTemplate.metadata.description).toBe('Updated description');
        expect(updatedTemplate.metadata.version).toBe(2);
        expect(updatedTemplate.metadata.updatedAt).toBeDefined();
      });
    });

    test('should delete a template', () => {
      const { result } = renderHook(() => useTemplateStore());

      // Create a template first
      let templateId;
      act(() => {
        const template = result.current.createTemplate({
          name: 'Template to Delete',
          category: 'business'
        });
        templateId = template.id;
      });

      expect(result.current.templates).toHaveLength(1);

      // Delete the template
      act(() => {
        result.current.deleteTemplate(templateId);
      });

      expect(result.current.templates).toHaveLength(0);
    });

    test('should duplicate a template', () => {
      const { result } = renderHook(() => useTemplateStore());

      // Create a template first
      let templateId;
      act(() => {
        const template = result.current.createTemplate({
          name: 'Original Template',
          category: 'business',
          description: 'Original description'
        });
        templateId = template.id;
      });

      // Duplicate the template
      act(() => {
        const duplicatedTemplate = result.current.duplicateTemplate(templateId);
        
        expect(duplicatedTemplate).toBeDefined();
        expect(duplicatedTemplate.id).not.toBe(templateId);
        expect(duplicatedTemplate.name).toBe('Original Template (Copy)');
        expect(duplicatedTemplate.metadata.description).toBe('Original description');
        expect(duplicatedTemplate.metadata.version).toBe(1);
      });

      expect(result.current.templates).toHaveLength(2);
    });
  });

  describe('Template Filtering and Search', () => {
    beforeEach(() => {
      const { result } = renderHook(() => useTemplateStore());
      
      // Create test templates
      act(() => {
        result.current.createTemplate({
          name: 'Business Card',
          category: 'business',
          description: 'Professional business card',
          metadata: { tags: ['business', 'professional'], isPublic: true }
        });
        
        result.current.createTemplate({
          name: 'Event Poster',
          category: 'events',
          description: 'Event announcement poster',
          metadata: { tags: ['event', 'poster'], isPublic: true }
        });
        
        result.current.createTemplate({
          name: 'Private Template',
          category: 'business',
          description: 'Private template',
          metadata: { tags: ['private'], isPublic: false }
        });
      });
    });

    test('should filter templates by category', () => {
      const { result } = renderHook(() => useTemplateStore());

      const businessTemplates = result.current.getTemplatesByCategory('business');
      expect(businessTemplates).toHaveLength(2);
      expect(businessTemplates.every(t => t.category === 'business')).toBe(true);

      const eventTemplates = result.current.getTemplatesByCategory('events');
      expect(eventTemplates).toHaveLength(1);
      expect(eventTemplates[0].name).toBe('Event Poster');
    });

    test('should get only public templates', () => {
      const { result } = renderHook(() => useTemplateStore());

      const publicTemplates = result.current.getPublicTemplates();
      expect(publicTemplates).toHaveLength(2);
      expect(publicTemplates.every(t => t.metadata.isPublic)).toBe(true);
    });

    test('should search templates by name and description', () => {
      const { result } = renderHook(() => useTemplateStore());

      const searchResults = result.current.searchTemplates('business');
      expect(searchResults).toHaveLength(2); // Matches name and description

      const eventResults = result.current.searchTemplates('event');
      expect(eventResults).toHaveLength(1);
      expect(eventResults[0].name).toBe('Event Poster');
    });

    test('should search templates by tags', () => {
      const { result } = renderHook(() => useTemplateStore());

      const professionalResults = result.current.searchTemplates('professional');
      expect(professionalResults).toHaveLength(1);
      expect(professionalResults[0].name).toBe('Business Card');
    });
  });

  describe('User Role Management', () => {
    test('should set and get user role', () => {
      const { result } = renderHook(() => useTemplateStore());

      expect(result.current.userRole).toBeNull();

      act(() => {
        result.current.setUserRole('admin');
      });

      expect(result.current.userRole).toBe('admin');

      act(() => {
        result.current.setUserRole('user');
      });

      expect(result.current.userRole).toBe('user');
    });
  });

  describe('Template Mode Management', () => {
    test('should manage template mode state', () => {
      const { result } = renderHook(() => useTemplateStore());

      expect(result.current.isTemplateMode).toBe(false);

      act(() => {
        result.current.setIsTemplateMode(true);
      });

      expect(result.current.isTemplateMode).toBe(true);
    });

    test('should manage current and selected templates', () => {
      const { result } = renderHook(() => useTemplateStore());

      const template = {
        id: 'test-template',
        name: 'Test Template',
        category: 'business'
      };

      act(() => {
        result.current.setCurrentTemplate(template);
      });

      expect(result.current.currentTemplate).toEqual(template);

      act(() => {
        result.current.setSelectedTemplate(template);
      });

      expect(result.current.selectedTemplate).toEqual(template);
    });
  });

  describe('Local Storage Integration', () => {
    test('should save templates to localStorage', () => {
      const { result } = renderHook(() => useTemplateStore());

      act(() => {
        result.current.createTemplate({
          name: 'Test Template',
          category: 'business'
        });
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'poster-templates',
        expect.any(String)
      );
    });

    test('should load templates from localStorage', () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Stored Template',
          category: 'business',
          metadata: { isPublic: true }
        }
      ];

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockTemplates));

      const { result } = renderHook(() => useTemplateStore());

      act(() => {
        result.current.loadTemplates();
      });

      expect(result.current.templates).toHaveLength(1);
      expect(result.current.templates[0].name).toBe('Stored Template');
    });

    test('should initialize sample templates when localStorage is empty', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useTemplateStore());

      act(() => {
        result.current.loadTemplates();
      });

      // Should have sample templates
      expect(result.current.templates.length).toBeGreaterThan(0);
      expect(result.current.templates.some(t => t.name.includes('Business Card'))).toBe(true);
    });
  });
});
