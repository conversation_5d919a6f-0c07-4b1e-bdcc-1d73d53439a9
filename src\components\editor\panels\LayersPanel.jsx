"use client";

import { useState, useEffect } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
// Layer utility functions
const toggleLayerVisibility = (canvas, layerId) => {
  if (!canvas) return;

  // Check if this is a drawing session
  const historyManager = canvas.historyManager;
  const drawingSessionManager = historyManager?.getDrawingSessionManager?.();

  if (drawingSessionManager) {
    const session = drawingSessionManager.sessions.get(layerId);
    if (session) {
      drawingSessionManager.toggleSessionVisibility(layerId);
      return;
    }
  }

  // Handle individual objects
  const obj = canvas.getObjects().find(o => o.id === layerId);
  if (obj) {
    obj.set('visible', !obj.visible);
    canvas.renderAll();
  }
};

const toggleLayerLock = (canvas, layerId) => {
  if (!canvas) return;

  // Check if this is a drawing session
  const historyManager = canvas.historyManager;
  const drawingSessionManager = historyManager?.getDrawingSessionManager?.();

  if (drawingSessionManager) {
    const session = drawingSessionManager.sessions.get(layerId);
    if (session) {
      drawingSessionManager.toggleSessionLock(layerId);
      return;
    }
  }

  // Handle individual objects
  const obj = canvas.getObjects().find(o => o.id === layerId);
  if (obj) {
    obj.set('selectable', !obj.selectable);
    obj.set('evented', !obj.evented);
    canvas.renderAll();
  }
};

const selectLayer = (canvas, layerId) => {
  if (!canvas) return;
  const obj = canvas.getObjects().find(o => o.id === layerId);
  if (obj && obj.selectable) {
    canvas.setActiveObject(obj);
    canvas.renderAll();
  }
};

const deleteLayer = (canvas, layerId) => {
  if (!canvas) return;

  // Check if this is a drawing session
  const historyManager = canvas.historyManager;
  const drawingSessionManager = historyManager?.getDrawingSessionManager?.();

  if (drawingSessionManager) {
    const session = drawingSessionManager.sessions.get(layerId);
    if (session) {
      drawingSessionManager.deleteSession(layerId);
      return;
    }
  }

  // Handle individual objects
  const obj = canvas.getObjects().find(o => o.id === layerId);
  if (obj) {
    // Prevent deletion of design area elements
    if (obj.id === 'design-area' ||
        obj.isDesignArea ||
        obj.isDesignAreaElement ||
        obj.isCornerIndicator) {
      console.warn("Cannot delete design area elements");
      return;
    }

    canvas.remove(obj);
    canvas.renderAll();
  }
};

const moveLayerUp = (canvas, layerId) => {
  if (!canvas) return;
  const obj = canvas.getObjects().find(o => o.id === layerId);
  if (obj && !obj.isDesignArea && !obj.isDesignAreaElement) {
    console.log(`Moving layer ${layerId} up...`);
    console.log(`Current canvas objects:`, canvas.getObjects().map(o => o.id || o.type));
    console.log(`Object to move:`, obj.id, obj.type);

    const beforeIndex = canvas.getObjects().indexOf(obj);
    console.log(`Before index: ${beforeIndex}`);

    // Use Fabric.js built-in method
    const result = canvas.bringObjectForward(obj);
    console.log(`bringObjectForward result:`, result);

    const afterIndex = canvas.getObjects().indexOf(obj);
    console.log(`After index: ${afterIndex}`);

    canvas.renderAll();
    console.log(`✅ Moved layer ${layerId} up from ${beforeIndex} to ${afterIndex}`);
  }
};

const moveLayerDown = (canvas, layerId) => {
  if (!canvas) return;
  const obj = canvas.getObjects().find(o => o.id === layerId);
  if (obj && !obj.isDesignArea && !obj.isDesignAreaElement) {
    console.log(`Moving layer ${layerId} down...`);
    console.log(`Current canvas objects:`, canvas.getObjects().map(o => o.id || o.type));
    console.log(`Object to move:`, obj.id, obj.type);

    const beforeIndex = canvas.getObjects().indexOf(obj);
    console.log(`Before index: ${beforeIndex}`);

    // Use Fabric.js built-in method
    const result = canvas.sendObjectBackwards(obj);
    console.log(`sendObjectBackwards result:`, result);

    const afterIndex = canvas.getObjects().indexOf(obj);
    console.log(`After index: ${afterIndex}`);

    canvas.renderAll();
    console.log(`✅ Moved layer ${layerId} down from ${beforeIndex} to ${afterIndex}`);
  }
};


import {
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Trash2,
  ChevronUp,
  ChevronDown,
  Type,
  Square,
  Circle,
  Image as ImageIcon,
  Pen,
  Shapes,
  Layers3
} from "lucide-react";

export default function LayersPanel() {
  const {
    canvas,
    selectedObject,
    markAsModified
  } = useEditorStore();

  const [layers, setLayers] = useState([]);

  const updateLayers = () => {
    if (canvas) {
      // Get drawing session manager from history manager
      const historyManager = canvas.historyManager;
      const drawingSessionManager = historyManager?.getDrawingSessionManager?.();

      // Filter out design area elements from layers
      const userObjects = canvas.getObjects().filter(obj =>
        !obj.isDesignArea &&
        !obj.isDesignAreaElement &&
        obj.id !== 'design-area' &&
        !obj.id?.startsWith('corner-indicator') &&
        !obj.excludeFromExport
      );

      // Get active drawing sessions
      const activeSessions = drawingSessionManager?.getActiveSessions() || [];
      const pathsInSessions = new Set();

      // Track which paths are in sessions
      activeSessions.forEach(session => {
        session.paths.forEach(pathData => {
          pathsInSessions.add(pathData.id);
        });
      });

      // Create layers for non-path objects and ungrouped paths
      const individualLayers = userObjects
        .filter(obj => obj.type !== 'path' || !pathsInSessions.has(obj.id))
        .map((obj, canvasIndex) => {
          let layerName = '';

          // Generate better layer names based on object type
          switch (obj.type) {
            case 'text':
            case 'textbox':
            case 'i-text':
              layerName = obj.text?.substring(0, 20) || 'Text';
              break;
            case 'rect':
            case 'rectangle':
              layerName = 'Rectangle';
              break;
            case 'circle':
            case 'ellipse':
              layerName = 'Circle';
              break;
            case 'path':
              layerName = 'Drawing';
              break;
            case 'image':
              layerName = 'Image';
              break;
            case 'polygon':
              layerName = 'Polygon';
              break;
            case 'triangle':
              layerName = 'Triangle';
              break;
            default:
              layerName = obj.type.charAt(0).toUpperCase() + obj.type.slice(1);
          }

          return {
            id: obj.id || `object-${canvasIndex}`,
            name: layerName,
            type: obj.type,
            visible: obj.visible !== false,
            locked: !obj.selectable,
            object: obj,
            canvasIndex: userObjects.indexOf(obj),
            zIndex: userObjects.indexOf(obj),
            isSession: false
          };
        });

      // Create layers for drawing sessions
      const sessionLayers = activeSessions.map(session => {
        // Find the highest z-index of paths in this session
        const sessionPaths = session.paths
          .map(pathData => userObjects.find(obj => obj.id === pathData.id))
          .filter(Boolean);

        const maxZIndex = Math.max(...sessionPaths.map(obj => userObjects.indexOf(obj)));

        return {
          id: session.id,
          name: session.name,
          type: 'drawing-session',
          visible: session.visible,
          locked: session.locked,
          object: null, // Session doesn't have a single object
          session: session,
          pathCount: session.paths.length,
          canvasIndex: maxZIndex,
          zIndex: maxZIndex,
          isSession: true
        };
      });

      // Combine and sort all layers
      const allLayers = [...individualLayers, ...sessionLayers];
      allLayers.sort((a, b) => b.zIndex - a.zIndex);

      setLayers(allLayers);
    }
  };

  // Update layers when canvas changes
  useEffect(() => {
    if (!canvas) return;

    const handleCanvasChange = () => {
      updateLayers();
    };

    canvas.on('object:added', handleCanvasChange);
    canvas.on('object:removed', handleCanvasChange);
    canvas.on('object:modified', handleCanvasChange);

    // Initial update
    updateLayers();

    return () => {
      canvas.off('object:added', handleCanvasChange);
      canvas.off('object:removed', handleCanvasChange);
      canvas.off('object:modified', handleCanvasChange);
    };
  }, [canvas]);

  const handleToggleVisibility = (layerId) => {
    toggleLayerVisibility(canvas, layerId);
    markAsModified();
    updateLayers();
  };

  const handleToggleLock = (layerId) => {
    toggleLayerLock(canvas, layerId);
    markAsModified();
    updateLayers();
  };

  const handleSelectLayer = (layerId) => {
    selectLayer(canvas, layerId);
    updateLayers();
  };

  const handleDeleteLayer = (layerId) => {
    deleteLayer(canvas, layerId);
    markAsModified();
    updateLayers();
  };

  const handleMoveUp = (layerId) => {
    moveLayerUp(canvas, layerId);
    markAsModified();
    updateLayers();
  };

  const handleMoveDown = (layerId) => {
    moveLayerDown(canvas, layerId);
    markAsModified();
    updateLayers();
  };

  const getLayerIcon = (type) => {
    switch (type) {
      case 'text':
      case 'textbox':
      case 'i-text':
        return Type;
      case 'rect':
      case 'rectangle':
        return Square;
      case 'circle':
      case 'ellipse':
        return Circle;
      case 'image':
        return ImageIcon;
      case 'path':
        return Pen;
      case 'polygon':
      case 'triangle':
        return Shapes;
      case 'drawing-session':
        return Layers3;
      default:
        return Square;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Layers</h3>
          <span className="text-xs text-gray-500">
            {layers.length} {layers.length === 1 ? 'layer' : 'layers'}
          </span>
        </div>
      </div>

      {/* Layers List */}
      <div className="flex-1 overflow-y-auto">
        {layers.length === 0 ? (
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center text-gray-500">
              <Square className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">No layers yet</p>
              <p className="text-xs mt-1">Add shapes, text, or images to get started</p>
            </div>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {/* Layers are already sorted with top layer first */}
            {layers.map((layer, index) => {
              const Icon = getLayerIcon(layer.type);
              const isSelected = selectedObject?.id === layer.id;
              const isTopLayer = index === 0;
              const isBottomLayer = index === layers.length - 1;
              
              return (
                <div
                  key={layer.id}
                  className={`
                    flex items-center p-2 rounded-md cursor-pointer transition-colors
                    ${isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'}
                  `}
                  onClick={() => handleSelectLayer(layer.id)}
                >
                  {/* Layer Icon */}
                  <Icon className="w-4 h-4 text-gray-500 mr-2 flex-shrink-0" />
                  
                  {/* Layer Name */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className={`text-sm truncate ${
                        !layer.visible ? 'text-gray-400 italic' :
                        layer.locked ? 'text-orange-600' :
                        'text-gray-700'
                      }`}>
                        {layer.name}
                      </span>
                      {layer.isSession && (
                        <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">
                          {layer.pathCount} paths
                        </span>
                      )}
                    </div>
                    {(!layer.visible || layer.locked || layer.isSession) && (
                      <div className="flex items-center gap-1 mt-0.5">
                        {!layer.visible && (
                          <span className="text-xs text-gray-400">Hidden</span>
                        )}
                        {layer.locked && (
                          <span className="text-xs text-orange-500">Locked</span>
                        )}
                        {layer.isSession && (
                          <span className="text-xs text-blue-600">Drawing Session</span>
                        )}
                      </div>
                    )}
                  </div>
                  
                  {/* Layer Controls */}
                  <div className="flex items-center space-x-1 ml-2">
                    {/* Move Up/Down */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMoveUp(layer.id);
                      }}
                      disabled={isTopLayer}
                      className="h-6 w-6 p-0"
                      title="Move layer up"
                    >
                      <ChevronUp className="h-3 w-3" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMoveDown(layer.id);
                      }}
                      disabled={isBottomLayer}
                      className="h-6 w-6 p-0"
                      title="Move layer down"
                    >
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                    
                    {/* Visibility Toggle */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleVisibility(layer.id);
                      }}
                      className="h-6 w-6 p-0"
                      title={layer.visible ? "Hide layer" : "Show layer"}
                    >
                      {layer.visible ? (
                        <Eye className="h-3 w-3 text-blue-600" />
                      ) : (
                        <EyeOff className="h-3 w-3 text-gray-400" />
                      )}
                    </Button>

                    {/* Lock Toggle */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleLock(layer.id);
                      }}
                      className="h-6 w-6 p-0"
                      title={layer.locked ? "Unlock layer" : "Lock layer"}
                    >
                      {layer.locked ? (
                        <Lock className="h-3 w-3 text-orange-500" />
                      ) : (
                        <Unlock className="h-3 w-3 text-gray-600" />
                      )}
                    </Button>

                    {/* Delete */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteLayer(layer.id);
                      }}
                      className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      title="Delete layer"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Tips */}
      <div className="p-4 border-t bg-gray-50">
        <div className="text-xs text-gray-600 space-y-1">
          <p>• Click a layer to select it</p>
          <p>• Use eye icon to show/hide layers</p>
          <p>• Use lock icon to prevent editing</p>
        </div>
      </div>
    </div>
  );
}
