import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { templateId } = await request.json();
    
    if (!templateId) {
      return NextResponse.json({
        success: false,
        error: 'Template ID is required'
      }, { status: 400 });
    }

    // Try to import Prisma client
    const { PrismaClient } = await import('../../../../generated/prisma');
    const prisma = new PrismaClient();
    
    // First check if template exists
    const existingTemplate = await prisma.template.findUnique({
      where: { id: templateId }
    });

    if (!existingTemplate) {
      return NextResponse.json({
        success: false,
        error: 'Template not found',
        templateId
      });
    }

    // Try to delete the template
    await prisma.template.delete({
      where: { id: templateId }
    });

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully',
      deletedTemplate: existingTemplate
    });
  } catch (error) {
    console.error('Database delete test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
