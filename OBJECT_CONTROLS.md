# Object Controls

This document explains the header-based object controls feature that appears when objects are selected on the canvas.

## Overview

When you select any object on the canvas (text, shapes, images, etc.), a contextual toolbar appears in the header below the main navigation with various controls for manipulating the selected object. This design prevents the controls from covering the object and provides more space for contextual tools.

## Features

### Visual Controls

The header toolbar includes the following controls:

#### Text-Specific Controls (shown only for text objects):
1. **Font Family Dropdown** - Choose from Arial, Helvetica, Times New Roman, Georgia, Verdana, Courier New
2. **Font Size Input** - Adjust text size (8-200px)
3. **Bold Toggle** (B) - Make text bold/normal
4. **Italic Toggle** (I) - Make text italic/normal
5. **Underline Toggle** (U) - Add/remove underline
6. **Text Alignment** - Left, center, right alignment
7. **Text Color Picker** - Change text color

#### Shape-Specific Controls (shown only for shapes):
1. **Fill Color Picker** - Change shape fill color
2. **Stroke Color Picker** - Change shape border color
3. **Stroke Width Slider** - Adjust border thickness (0-20px)
4. **Border Radius Slider** - Round corners for rectangles (0-50px)

#### Image-Specific Controls (shown only for images):
1. **Image Filters Panel** - Comprehensive filter controls:
   - **Brightness** - Adjust image brightness (-100% to +100%)
   - **Contrast** - Adjust image contrast (-100% to +100%)
   - **Saturation** - Adjust color saturation (-100% to +100%)
   - **Blur** - Apply blur effect (0-20px)

#### Transform Controls (shown for all objects):
1. **Rotation Slider** - Rotate objects (-360° to +360°)
2. **Flip Horizontal** - Mirror object horizontally
3. **Flip Vertical** - Mirror object vertically
4. **Scale Controls** - Resize objects:
   - **Width Scale** - Adjust horizontal scale (10% to 300%)
   - **Height Scale** - Adjust vertical scale (10% to 300%)
   - **Reset Scale** - Return to original size

#### Common Object Controls (shown for all objects):
1. **Opacity Control** (🔘) - Adjust the transparency of the selected object
2. **Layer Controls** (⬆️⬇️) - Manage object layering (bring forward/send backward)
3. **Visibility Toggle** (👁️) - Show/hide the selected object
4. **Lock Toggle** (🔒) - Lock/unlock the object to prevent accidental modifications
5. **Duplicate** (📋) - Create a copy of the selected object
6. **Delete** (🗑️) - Remove the selected object from the canvas

### Keyboard Shortcuts

The following keyboard shortcuts are available when an object is selected:

- **Delete/Backspace** - Delete the selected object
- **Ctrl+D** - Duplicate the selected object
- **Ctrl+B** - Toggle bold (text objects only)
- **Ctrl+I** - Toggle italic (text objects only)
- **Ctrl+U** - Toggle underline (text objects only)

## Implementation Details

### Components

- **HeaderObjectControls.jsx** - Main component that renders the contextual toolbar in the header
- **Header.jsx** - Updated to include the HeaderObjectControls component
- **UI Components** - Uses Radix UI components for popover, slider, and select functionality
- **ObjectControls.jsx** - Legacy floating toolbar (now replaced by header controls)

### Key Features

1. **Header Integration** - Controls are integrated into the header, preventing overlap with canvas objects
2. **Contextual Interface** - Different controls appear based on the selected object type
3. **Real-time Updates** - Controls update automatically when object properties change
4. **Keyboard Shortcuts** - Full keyboard support for common operations
5. **Professional Layout** - Clean, organized interface that doesn't obstruct the canvas

### Technical Implementation

The ObjectControls component:

1. Listens to canvas selection events through the Zustand store
2. Calculates the optimal position based on the selected object's bounding box
3. Updates position in real-time as objects are manipulated
4. Provides both mouse and keyboard interaction methods
5. Handles edge cases like off-screen positioning

## Usage

1. **Select an Object** - Click on any object on the canvas
2. **Use Controls** - The floating toolbar will appear above the object
3. **Adjust Properties** - Click on any control to modify the object
4. **Keyboard Shortcuts** - Use keyboard shortcuts for quick actions

## Styling

The controls use a clean, modern design with:
- White background with subtle shadow
- Rounded corners for a friendly appearance
- Hover effects for better user feedback
- Consistent icon sizing and spacing
- Smooth animations for appearing/disappearing

## Browser Compatibility

The object controls work in all modern browsers that support:
- CSS transforms and positioning
- ES6+ JavaScript features
- Radix UI components
- Fabric.js canvas manipulation

## Future Enhancements

Potential improvements could include:
- More granular layer controls (bring to front, send to back)
- Color picker integration
- Transform controls (rotation, skew)
- Alignment tools
- Grouping/ungrouping controls
