"use client";

import { use } from "react";
import dynamic from "next/dynamic";
import ClientWrapper from "@/components/ClientWrapper";
import ErrorBoundary from "@/components/ErrorBoundary";

const TemplateCustomizer = dynamic(() => import("@/components/user/TemplateCustomizer"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Template Customizer...</h2>
        <p className="text-gray-600">Preparing customization interface...</p>
      </div>
    </div>
  ),
});

export default function CustomizeTemplatePage({ params }) {
  const { id } = use(params);

  return (
    <ErrorBoundary>
      <ClientWrapper>
        <TemplateCustomizer templateId={id} />
      </ClientWrapper>
    </ErrorBoundary>
  );
}
