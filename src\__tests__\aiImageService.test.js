/**
 * @jest-environment jsdom
 */

import { aiImageService } from '../services/aiImageService';

// Mock canvas and image elements
global.HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
  drawImage: jest.fn(),
  getImageData: jest.fn(() => ({
    data: new Uint8ClampedArray(400), // 10x10 image with RGBA
    width: 10,
    height: 10
  })),
  putImageData: jest.fn(),
  imageSmoothingEnabled: true,
  imageSmoothingQuality: 'high'
}));

global.HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/png;base64,mockImageData');

// Mock Image constructor
global.Image = class {
  constructor() {
    setTimeout(() => {
      this.onload && this.onload();
    }, 100);
  }
  set src(value) {
    this._src = value;
  }
  get src() {
    return this._src;
  }
};

// Mock document.createElement for canvas
const originalCreateElement = document.createElement;
document.createElement = jest.fn((tagName) => {
  if (tagName === 'canvas') {
    const canvas = originalCreateElement.call(document, 'canvas');
    canvas.width = 100;
    canvas.height = 100;
    return canvas;
  }
  return originalCreateElement.call(document, tagName);
});

describe('AI Image Service', () => {
  const mockImageDataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGArEkAAAAAElFTkSuQmCC';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Availability', () => {
    test('should be available', () => {
      expect(aiImageService.isAvailable()).toBe(true);
    });

    test('should return supported operations', () => {
      const operations = aiImageService.getSupportedOperations();
      expect(operations).toContain('removeBackground');
      expect(operations).toContain('upscale');
      expect(operations).toContain('enhance');
      expect(operations).toContain('colorize');
      expect(operations).toContain('denoise');
    });
  });

  describe('Background Removal', () => {
    test('should remove background from image', async () => {
      const result = await aiImageService.removeBackground(mockImageDataUrl);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.startsWith('data:image/')).toBe(true);
    });

    test('should handle invalid image data', async () => {
      await expect(aiImageService.removeBackground('invalid-data'))
        .rejects.toThrow('Failed to remove background');
    });
  });

  describe('Image Upscaling', () => {
    test('should upscale image with default scale', async () => {
      const result = await aiImageService.upscaleImage(mockImageDataUrl);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.startsWith('data:image/')).toBe(true);
    });

    test('should upscale image with custom scale', async () => {
      const result = await aiImageService.upscaleImage(mockImageDataUrl, 4);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.startsWith('data:image/')).toBe(true);
    });

    test('should handle invalid image data for upscaling', async () => {
      await expect(aiImageService.upscaleImage('invalid-data'))
        .rejects.toThrow('Failed to upscale image');
    });
  });

  describe('Image Enhancement', () => {
    test('should enhance image quality', async () => {
      const result = await aiImageService.enhanceImage(mockImageDataUrl);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.startsWith('data:image/')).toBe(true);
    });

    test('should handle invalid image data for enhancement', async () => {
      await expect(aiImageService.enhanceImage('invalid-data'))
        .rejects.toThrow('Failed to enhance image');
    });
  });

  describe('Image Colorization', () => {
    test('should colorize black and white image', async () => {
      const result = await aiImageService.colorizeImage(mockImageDataUrl);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.startsWith('data:image/')).toBe(true);
    });

    test('should handle invalid image data for colorization', async () => {
      await expect(aiImageService.colorizeImage('invalid-data'))
        .rejects.toThrow('Failed to colorize image');
    });
  });

  describe('Image Denoising', () => {
    test('should denoise image', async () => {
      const result = await aiImageService.denoiseImage(mockImageDataUrl);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.startsWith('data:image/')).toBe(true);
    });

    test('should handle invalid image data for denoising', async () => {
      await expect(aiImageService.denoiseImage('invalid-data'))
        .rejects.toThrow('Failed to denoise image');
    });
  });

  describe('Fabric.js Integration', () => {
    test('should extract image data from fabric object', () => {
      const mockFabricObject = {
        type: 'image',
        width: 100,
        height: 100,
        getElement: jest.fn(() => ({
          width: 100,
          height: 100
        }))
      };

      const result = aiImageService.getImageDataFromFabricObject(mockFabricObject);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(mockFabricObject.getElement).toHaveBeenCalled();
    });

    test('should throw error for invalid fabric object', () => {
      const invalidObject = {
        type: 'text',
        width: 100,
        height: 100
      };

      expect(() => {
        aiImageService.getImageDataFromFabricObject(invalidObject);
      }).toThrow('Invalid image object');
    });

    test('should throw error for null fabric object', () => {
      expect(() => {
        aiImageService.getImageDataFromFabricObject(null);
      }).toThrow('Invalid image object');
    });
  });

  describe('Processing Simulation', () => {
    test('should simulate processing time', async () => {
      const startTime = Date.now();
      await aiImageService.removeBackground(mockImageDataUrl);
      const endTime = Date.now();
      
      // Should take at least 2 seconds (simulated processing time)
      expect(endTime - startTime).toBeGreaterThanOrEqual(2000);
    });

    test('should apply different effects based on operation', async () => {
      // Test that different operations return different results
      // (In a real implementation, these would be different)
      const backgroundRemoved = await aiImageService.removeBackground(mockImageDataUrl);
      const enhanced = await aiImageService.enhanceImage(mockImageDataUrl);
      const colorized = await aiImageService.colorizeImage(mockImageDataUrl);
      
      expect(backgroundRemoved).toBeDefined();
      expect(enhanced).toBeDefined();
      expect(colorized).toBeDefined();
      
      // All should be valid data URLs
      expect(backgroundRemoved.startsWith('data:image/')).toBe(true);
      expect(enhanced.startsWith('data:image/')).toBe(true);
      expect(colorized.startsWith('data:image/')).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle canvas creation errors', async () => {
      // Mock document.createElement to throw an error
      const originalCreateElement = document.createElement;
      document.createElement = jest.fn(() => {
        throw new Error('Canvas creation failed');
      });

      await expect(aiImageService.removeBackground(mockImageDataUrl))
        .rejects.toThrow('Failed to remove background');

      // Restore original function
      document.createElement = originalCreateElement;
    });

    test('should handle image loading errors', async () => {
      // Mock Image to trigger onerror
      global.Image = class {
        constructor() {
          setTimeout(() => {
            this.onerror && this.onerror();
          }, 100);
        }
        set src(value) {
          this._src = value;
        }
        get src() {
          return this._src;
        }
      };

      await expect(aiImageService.removeBackground(mockImageDataUrl))
        .rejects.toThrow('Failed to remove background');
    });
  });
});
