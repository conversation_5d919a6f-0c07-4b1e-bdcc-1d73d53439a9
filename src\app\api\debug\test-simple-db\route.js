import { NextResponse } from 'next/server';
import { getAllTemplates, deleteTemplateById } from '../../../../services/simpleDbService';

export async function GET() {
  try {
    const result = await getAllTemplates();
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const { action, templateId } = await request.json();
    
    if (action === 'delete' && templateId) {
      const result = await deleteTemplateById(templateId);
      return NextResponse.json(result);
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action or missing templateId'
    }, { status: 400 });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
