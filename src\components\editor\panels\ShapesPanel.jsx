"use client";

import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { addShapeToCanvas } from "@/fabric/fabric-utils";
import { shapeTypes, shapeDefinitions } from "@/fabric/shapes/shape-definitions";
import { X } from "lucide-react";

export default function ShapesPanel() {
  const { setActivePanel, canvas, markAsModified } = useEditorStore();

  const handleClose = () => {
    setActivePanel('select');
  };

  const addShape = async (shapeType, customProps = {}) => {
    if (!canvas) return;

    try {
      const shape = await addShapeToCanvas(canvas, shapeType, customProps);
      if (shape) {
        markAsModified();
      }
    } catch (error) {
      console.error("Error adding shape:", error);
    }
  };

  const getShapeIcon = (shapeType) => {
    switch (shapeType) {
      case 'rectangle':
        return '▭';
      case 'square':
        return '⬜';
      case 'circle':
        return '⭕';
      case 'triangle':
        return '🔺';
      case 'ellipse':
        return '⭕';
      case 'line':
        return '➖';
      case 'star':
        return '⭐';
      case 'arrow':
        return '➡️';
      case 'pentagon':
        return '⬟';
      case 'hexagon':
        return '⬡';
      case 'octagon':
        return '⬢';
      case 'heart':
        return '❤️';
      default:
        return '⬜';
    }
  };

  const shapeCategories = [
    {
      name: 'Basic Shapes',
      shapes: ['rectangle', 'square', 'circle', 'triangle', 'ellipse', 'line']
    },
    {
      name: 'Polygons',
      shapes: ['pentagon', 'hexagon', 'octagon']
    },
    {
      name: 'Special',
      shapes: ['star', 'arrow', 'heart']
    }
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Shapes</h3>
          <p className="text-xs text-gray-500">Add shapes to your canvas</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {shapeCategories.map((category) => (
          <div key={category.name} className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700">{category.name}</h4>
            
            <div className="grid grid-cols-3 gap-3">
              {category.shapes.map((shapeType) => {
                const definition = shapeDefinitions[shapeType];
                if (!definition) return null;
                
                return (
                  <button
                    key={shapeType}
                    onClick={() => addShape(shapeType)}
                    className="aspect-square border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 flex items-center justify-center transition-colors group"
                    title={definition.label}
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-1">
                        {getShapeIcon(shapeType)}
                      </div>
                      <div className="text-xs text-gray-600 group-hover:text-blue-600">
                        {definition.label}
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        ))}

        {/* Color Presets */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Quick Colors</h4>
          
          <div className="grid grid-cols-6 gap-2">
            {[
              '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
              '#8b5cf6', '#06b6d4', '#84cc16', '#f97316',
              '#ec4899', '#6b7280', '#000000', '#ffffff'
            ].map((color) => (
              <button
                key={color}
                onClick={() => {
                  // Add a rectangle with the selected color
                  addShape('rectangle', { fill: color });
                }}
                className="aspect-square rounded-lg border-2 border-gray-200 hover:border-gray-400 transition-colors"
                style={{ backgroundColor: color }}
                title={`Add rectangle with ${color}`}
              />
            ))}
          </div>
        </div>

        {/* Shape Tips */}
        <div className="bg-blue-50 rounded-lg p-3">
          <h5 className="text-sm font-medium text-blue-900 mb-1">Tips</h5>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Click any shape to add it to your canvas</li>
            <li>• Use the properties panel to customize colors</li>
            <li>• Hold Shift while resizing to maintain proportions</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
