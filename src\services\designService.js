import prisma from '@/lib/prisma';

/**
 * Design Service - Handles all database operations for designs
 */

// Save a new design
export async function saveDesign(designData) {
  try {
    // Ensure user exists before creating design
    await ensureUserExists(designData.userId);

    const design = await prisma.design.create({
      data: {
        name: designData.name,
        description: designData.description || null,
        width: designData.width || 800,
        height: designData.height || 600,
        canvasData: designData.canvasData,
        thumbnail: designData.thumbnail || null,
        isPublic: designData.isPublic || false,
        userId: designData.userId,
      },
    });

    return { success: true, design };
  } catch (error) {
    console.error('Error saving design:', error);
    return { success: false, error: error.message };
  }
}

// Helper function to ensure user exists
async function ensureUserExists(userId) {
  try {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      // Create demo user if it doesn't exist
      await prisma.user.create({
        data: {
          id: userId,
          email: `${userId}@demo.com`,
          name: userId === 'demo_user' ? 'Demo User' : userId,
          image: null,
        }
      });
      console.log(`Created demo user: ${userId}`);
    }
  } catch (error) {
    console.error('Error ensuring user exists:', error);
    throw error;
  }
}

// Update an existing design
export async function updateDesign(designId, designData) {
  try {
    const design = await prisma.design.update({
      where: { id: designId },
      data: {
        name: designData.name,
        description: designData.description,
        width: designData.width,
        height: designData.height,
        canvasData: designData.canvasData,
        thumbnail: designData.thumbnail,
        isPublic: designData.isPublic,
        updatedAt: new Date(),
      },
    });

    return { success: true, design };
  } catch (error) {
    console.error('Error updating design:', error);
    return { success: false, error: error.message };
  }
}

// Get a design by ID
export async function getDesign(designId) {
  try {
    const design = await prisma.design.findUnique({
      where: { id: designId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!design) {
      return { success: false, error: 'Design not found' };
    }

    return { success: true, design };
  } catch (error) {
    console.error('Error getting design:', error);
    return { success: false, error: error.message };
  }
}

// Get all designs for a user
export async function getUserDesigns(userId, page = 1, limit = 10) {
  try {
    const skip = (page - 1) * limit;
    
    const [designs, total] = await Promise.all([
      prisma.design.findMany({
        where: { userId },
        orderBy: { updatedAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          description: true,
          width: true,
          height: true,
          thumbnail: true,
          isPublic: true,
          createdAt: true,
          updatedAt: true,
        },
      }),
      prisma.design.count({
        where: { userId },
      }),
    ]);

    return {
      success: true,
      designs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error('Error getting user designs:', error);
    return { success: false, error: error.message };
  }
}

// Delete a design
export async function deleteDesign(designId, userId) {
  try {
    // First check if the design belongs to the user
    const design = await prisma.design.findFirst({
      where: {
        id: designId,
        userId: userId,
      },
    });

    if (!design) {
      return { success: false, error: 'Design not found or access denied' };
    }

    await prisma.design.delete({
      where: { id: designId },
    });

    return { success: true, message: 'Design deleted successfully' };
  } catch (error) {
    console.error('Error deleting design:', error);
    return { success: false, error: error.message };
  }
}

// Get public designs (for gallery/browse)
export async function getPublicDesigns(page = 1, limit = 12) {
  try {
    const skip = (page - 1) * limit;
    
    const [designs, total] = await Promise.all([
      prisma.design.findMany({
        where: { isPublic: true },
        orderBy: { updatedAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          description: true,
          width: true,
          height: true,
          thumbnail: true,
          createdAt: true,
          user: {
            select: {
              name: true,
            },
          },
        },
      }),
      prisma.design.count({
        where: { isPublic: true },
      }),
    ]);

    return {
      success: true,
      designs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error('Error getting public designs:', error);
    return { success: false, error: error.message };
  }
}

// Save design history (for version control)
export async function saveDesignHistory(designId, canvasData, version, comment = null) {
  try {
    const history = await prisma.designHistory.create({
      data: {
        designId,
        canvasData,
        version,
        comment,
      },
    });

    return { success: true, history };
  } catch (error) {
    console.error('Error saving design history:', error);
    return { success: false, error: error.message };
  }
}

// Get design history
export async function getDesignHistory(designId) {
  try {
    const history = await prisma.designHistory.findMany({
      where: { designId },
      orderBy: { version: 'desc' },
    });

    return { success: true, history };
  } catch (error) {
    console.error('Error getting design history:', error);
    return { success: false, error: error.message };
  }
}
