"use client";

import { centerCanvas } from "@/fabric/fabric-utils";
import { create } from "zustand";

export const useEditorStore = create((set, get) => ({
  // Canvas state
  canvas: null,
  canvasWidth: 800,
  canvasHeight: 600,
  canvasBackgroundColor: '#ffffff',
  setCanvas: (canvas) => {
    const { initializeHistoryManager, historyManager } = get();

    // Clean up existing history manager if canvas is being replaced
    if (historyManager && !canvas) {
      historyManager.destroy();
      set({ historyManager: null });
    }

    set({ canvas });
    if (canvas) {
      centerCanvas(canvas);
      // Initialize the enhanced history manager
      initializeHistoryManager(canvas);
    }
  },
  setCanvasSize: (width, height) => {
    const { canvas, canvasBackgroundColor } = get();
    set({ canvasWidth: width, canvasHeight: height });
    if (canvas) {
      // Update design area with new dimensions
      import("@/fabric/fabric-utils").then(({ updateDesignArea }) => {
        updateDesignArea(canvas, width, height, canvasBackgroundColor);
      });
    }
  },
  setCanvasBackgroundColor: (color) => {
    const { canvas, canvasWidth, canvasHeight } = get();
    set({ canvasBackgroundColor: color });
    if (canvas) {
      // Update design area with new background color
      import("@/fabric/fabric-utils").then(({ updateDesignArea }) => {
        updateDesignArea(canvas, canvasWidth, canvasHeight, color);
      });
    }
  },

  // Design state
  designId: "local-design",
  setDesignId: (id) => set({ designId: id }),

  isEditing: true,
  setIsEditing: (flag) => set({ isEditing: flag }),

  name: "Untitled Design",
  setName: (value) => set({ name: value }),

  description: "",
  setDescription: (value) => set({ description: value }),

  // UI state
  showProperties: false,
  setShowProperties: (flag) => set({ showProperties: flag }),

  showLayers: true,
  setShowLayers: (flag) => set({ showLayers: flag }),

  // Active tool state (from Vue poster app)
  activeTool: 'select',
  setActiveTool: (tool) => set({ activeTool: tool }),

  // Drawing mode state
  isDrawingMode: false,
  setIsDrawingMode: (isDrawing) => set({ isDrawingMode: isDrawing }),

  // Pan mode state
  isPanMode: false,
  setIsPanMode: (isPanning) => set({ isPanMode: isPanning }),

  // Template mode state
  isTemplateMode: false,
  setIsTemplateMode: (isTemplate) => set({ isTemplateMode: isTemplate }),

  // Drawing session state
  currentDrawingSession: null,
  drawingSessionTimeout: 3000, // 3 seconds default
  setCurrentDrawingSession: (session) => set({ currentDrawingSession: session }),
  setDrawingSessionTimeout: (timeout) => set({ drawingSessionTimeout: timeout }),

  // Layers state
  layers: [],
  setLayers: (layers) => set({ layers }),
  selectedLayer: null,
  setSelectedLayer: (layer) => set({ selectedLayer: layer }),

  // Save state
  saveStatus: "saved",
  setSaveStatus: (status) => set({ saveStatus: status }),
  lastModified: Date.now(),
  isModified: false,
  currentDesignId: null,
  setCurrentDesignId: (id) => set({ currentDesignId: id }),

  markAsModified: () => {
    set({
      lastModified: Date.now(),
      saveStatus: "Modified",
      isModified: true,
    });

    // Auto-save to localStorage after 1 second
    setTimeout(() => {
      get().saveToLocalStorage();
    }, 1000);
  },

  saveToLocalStorage: () => {
    if (typeof window === 'undefined') return null;

    const canvas = get().canvas;
    const name = get().name;

    if (!canvas) {
      console.log("No canvas instance available");
      return null;
    }

    try {
      const canvasData = canvas.toJSON(["id", "filters"]);
      
      const designData = {
        name: name,
        canvasData: JSON.stringify(canvasData),
        width: canvas.width,
        height: canvas.height,
        lastModified: Date.now(),
      };

      localStorage.setItem("poster-editor-design", JSON.stringify(designData));
      
      set({
        saveStatus: "Saved",
        isModified: false,
      });

      return designData;
    } catch (e) {
      set({ saveStatus: "Error" });
      console.error("Error saving to localStorage:", e);
      return null;
    }
  },

  loadFromLocalStorage: () => {
    if (typeof window === 'undefined') return null;

    try {
      const savedData = localStorage.getItem("poster-editor-design");
      if (savedData) {
        const designData = JSON.parse(savedData);
        set({ name: designData.name });
        return designData;
      }
    } catch (e) {
      console.error("Error loading from localStorage:", e);
    }
    return null;
  },

  // Save design to database
  saveToDatabase: async (userId, designName, isPublic = false, description = null) => {
    const { canvas, canvasWidth, canvasHeight, currentDesignId } = get();

    if (!canvas || !userId) {
      console.error('Canvas or userId not available for database save');
      return { success: false, error: 'Canvas or userId not available' };
    }

    set({ saveStatus: "Saving..." });

    try {
      const canvasData = canvas.toJSON(["id", "filters"]);

      // Generate thumbnail (you can implement this later)
      const thumbnail = null; // TODO: Generate thumbnail from canvas

      const designData = {
        name: designName || get().name,
        description: description || `Design created on ${new Date().toLocaleDateString()}`,
        width: canvasWidth,
        height: canvasHeight,
        canvasData,
        thumbnail,
        isPublic,
        userId,
      };

      let response;

      if (currentDesignId) {
        // Update existing design
        response = await fetch(`/api/designs/${currentDesignId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(designData),
        });
      } else {
        // Create new design
        response = await fetch('/api/designs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(designData),
        });
      }

      const result = await response.json();

      if (result.success) {
        set({
          saveStatus: "Saved",
          isModified: false,
          currentDesignId: result.design.id,
          name: result.design.name,
        });
        console.log('Design saved to database:', result.design);
        return { success: true, design: result.design };
      } else {
        set({ saveStatus: "Error" });
        console.error('Error saving to database:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      set({ saveStatus: "Error" });
      console.error('Error saving to database:', error);
      return { success: false, error: error.message };
    }
  },

  // Load design from database
  loadFromDatabase: async (designId) => {
    const { canvas } = get();

    if (!canvas) {
      console.error('Canvas not available for loading design');
      return { success: false, error: 'Canvas not available' };
    }

    set({ saveStatus: "Loading..." });

    try {
      const response = await fetch(`/api/designs/${designId}`);
      const result = await response.json();

      if (result.success) {
        const design = result.design;

        // Clear current canvas
        canvas.clear();

        // Load design data
        canvas.loadFromJSON(design.canvasData, () => {
          canvas.renderAll();
          set({
            currentDesignId: design.id,
            name: design.name,
            description: design.description || "",
            canvasWidth: design.width,
            canvasHeight: design.height,
            saveStatus: "Saved",
            isModified: false,
          });
          console.log('Design loaded from database:', design);
        });

        return { success: true, design };
      } else {
        set({ saveStatus: "Error" });
        console.error('Error loading from database:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      set({ saveStatus: "Error" });
      console.error('Error loading from database:', error);
      return { success: false, error: error.message };
    }
  },

  // Get saved designs list from database
  getSavedDesigns: async (userId) => {
    try {
      const response = await fetch(`/api/designs?userId=${userId}&type=user`);
      const result = await response.json();

      if (result.success) {
        return { success: true, designs: result.designs };
      } else {
        console.error('Error fetching designs:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error getting saved designs:', error);
      return { success: false, error: error.message };
    }
  },

  // Template state (from Vue poster app)
  templates: [],
  setTemplates: (templates) => set({ templates }),

  // Selected object state
  selectedObject: null,
  setSelectedObject: (obj) => set({ selectedObject: obj }),

  // Enhanced Undo/Redo state with history manager
  historyManager: null,
  undoRedoState: 0, // Counter to trigger re-renders when undo/redo state changes

  // Initialize history manager when canvas is set
  initializeHistoryManager: (canvas) => {
    const { historyManager } = get();

    // Clean up existing history manager
    if (historyManager) {
      historyManager.destroy();
    }

    // Create new history manager
    import("@/fabric/fabric-utils").then(({ CanvasHistoryManager }) => {
      const onStateChange = () => {
        // Trigger re-render when history state changes
        const { undoRedoState } = get();
        set({ undoRedoState: undoRedoState + 1 });
      };

      const { drawingSessionTimeout } = get();
      const drawingSessionOptions = {
        sessionTimeout: drawingSessionTimeout,
        maxSessionPaths: 50
      };

      const newHistoryManager = new CanvasHistoryManager(canvas, 50, onStateChange, drawingSessionOptions);

      // Set up drawing session callbacks
      const drawingSessionManager = newHistoryManager.getDrawingSessionManager();
      if (drawingSessionManager) {
        drawingSessionManager.onSessionStart = (session) => {
          set({ currentDrawingSession: session });
          console.log('Drawing session started in store:', session.id);
        };

        drawingSessionManager.onSessionEnd = (session) => {
          set({ currentDrawingSession: null });
          console.log('Drawing session ended in store:', session.id);
        };

        drawingSessionManager.onPathAdded = (_, session) => {
          // Update the current session in store to trigger re-render
          set({ currentDrawingSession: { ...session } });
        };
      }

      // Disable tracking initially to prevent interference with canvas setup
      newHistoryManager.disableTracking();

      set({ historyManager: newHistoryManager });

      // Enable tracking and save initial state after canvas is fully ready
      setTimeout(() => {
        newHistoryManager.enableTracking();
        newHistoryManager.saveInitialState();
        console.log('History manager initialized and tracking enabled');
      }, 500); // Longer delay to ensure canvas is fully ready
    });
  },

  // Legacy method for backward compatibility - now delegates to history manager
  addToHistory: () => {
    // This method is kept for backward compatibility but the new system
    // automatically tracks changes through event listeners
    console.log('addToHistory called - new system handles this automatically');
  },

  // Enhanced undo functionality using history manager
  undo: async () => {
    const { historyManager, undoRedoState } = get();

    if (historyManager) {
      try {
        const success = await historyManager.undo();
        if (success) {
          // Trigger re-render by updating state
          set({ undoRedoState: undoRedoState + 1 });
        }
      } catch (error) {
        console.error('Undo failed:', error);
      }
    }
  },

  // Enhanced redo functionality using history manager
  redo: async () => {
    const { historyManager, undoRedoState } = get();

    console.log('🔄 Store redo called');
    console.log('Has history manager:', !!historyManager);

    if (historyManager) {
      console.log('Can redo from history manager:', historyManager.canRedo());
      try {
        const success = await historyManager.redo();
        console.log('Redo success:', success);
        if (success) {
          // Trigger re-render by updating state
          set({ undoRedoState: undoRedoState + 1 });
          console.log('State updated, undoRedoState:', undoRedoState + 1);
        }
      } catch (error) {
        console.error('Redo failed:', error);
      }
    } else {
      console.error('No history manager available for redo');
    }
  },

  // Check if undo is available using history manager
  canUndo: () => {
    const { historyManager } = get();
    return historyManager ? historyManager.canUndo() : false;
  },

  // Check if redo is available using history manager
  canRedo: () => {
    const { historyManager } = get();
    const result = historyManager ? historyManager.canRedo() : false;
    // Uncomment for debugging: console.log('canRedo called:', result, 'historyManager:', !!historyManager);
    return result;
  },

  // Clear history using history manager
  clearHistory: () => {
    const { historyManager } = get();
    if (historyManager) {
      historyManager.clearHistory();
    }
  },

  // Debug method to inspect history state
  debugHistory: () => {
    const { historyManager } = get();
    if (historyManager) {
      historyManager.debugHistory();
    } else {
      console.log('No history manager available');
    }
  },

  // Tool panels state
  showTemplatesPanel: false,
  setShowTemplatesPanel: (flag) => set({ showTemplatesPanel: flag }),

  showTextPanel: false,
  setShowTextPanel: (flag) => set({ showTextPanel: flag }),

  showShapesPanel: false,
  setShowShapesPanel: (flag) => set({ showShapesPanel: flag }),

  showDrawingPanel: false,
  setShowDrawingPanel: (flag) => set({ showDrawingPanel: flag }),

  showErasePanel: false,
  setShowErasePanel: (flag) => set({ showErasePanel: flag }),

  showUploadPanel: false,
  setShowUploadPanel: (flag) => set({ showUploadPanel: flag }),

  showSettingsPanel: false,
  setShowSettingsPanel: (flag) => set({ showSettingsPanel: flag }),

  showAiPanel: false,
  setShowAiPanel: (flag) => set({ showAiPanel: flag }),

  // Reset all panels
  resetPanels: () => set({
    showTemplatesPanel: false,
    showTextPanel: false,
    showShapesPanel: false,
    showDrawingPanel: false,
    showErasePanel: false,
    showUploadPanel: false,
    showSettingsPanel: false,
    showAiPanel: false,
  }),

  // Set active panel (close others)
  setActivePanel: (panel) => {
    const { canvas, activeTool, historyManager } = get();

    // Clean up drawing mode when switching away from draw tool
    if (activeTool === 'draw' && panel !== 'draw' && canvas) {
      // Disable drawing mode
      import("@/fabric/fabric-utils").then(({ toggleDrawingMode }) => {
        toggleDrawingMode(canvas, false);
        set({ isDrawingMode: false });
        console.log('Drawing mode disabled when switching tools');

        // Notify history manager about drawing mode change
        if (historyManager) {
          historyManager.onDrawingModeChange(false);
        }
      });
    }

    // Clean up pan mode when switching away from pan tool
    if (activeTool === 'pan' && panel !== 'pan' && canvas) {
      // Disable pan mode
      import("@/fabric/fabric-utils").then(({ togglePanMode }) => {
        togglePanMode(canvas, false);
        set({ isPanMode: false });
        console.log('Pan mode disabled when switching tools');
      });
    }

    // Notify history manager about tool change
    if (historyManager) {
      historyManager.onToolChange(panel);
    }

    get().resetPanels();
    switch (panel) {
      case 'templates':
        set({ showTemplatesPanel: true, activeTool: 'templates' });
        break;
      case 'text':
        set({ showTextPanel: true, activeTool: 'text' });
        break;
      case 'shapes':
        set({ showShapesPanel: true, activeTool: 'shapes' });
        break;
      case 'draw':
        set({ showDrawingPanel: true, activeTool: 'draw' });
        break;
      case 'erase':
        set({ showErasePanel: true, activeTool: 'erase' });
        break;
      case 'uploads':
        set({ showUploadPanel: true, activeTool: 'uploads' });
        break;
      case 'settings':
        set({ showSettingsPanel: true, activeTool: 'settings' });
        break;
      case 'ai':
        set({ showAiPanel: true, activeTool: 'ai' });
        break;
      case 'pan':
        // Enable pan mode
        if (canvas) {
          import("@/fabric/fabric-utils").then(({ togglePanMode }) => {
            togglePanMode(canvas, true);
            set({ isPanMode: true });
            console.log('Pan mode enabled');
          });
        }
        set({ activeTool: 'pan' });
        break;
      default:
        set({ activeTool: 'select' });
    }
  },

  resetStore: () => {
    set({
      canvas: null,
      designId: "local-design",
      isEditing: true,
      name: "Untitled Design",
      showProperties: false,
      showLayers: true,
      activeTool: 'select',
      isDrawingMode: false,
      isPanMode: false,
      isTemplateMode: false,
      layers: [],
      selectedLayer: null,
      saveStatus: "Saved",
      isModified: false,
      lastModified: Date.now(),
      selectedObject: null,
      showTemplatesPanel: false,
      showTextPanel: false,
      showShapesPanel: false,
      showDrawingPanel: false,
      showErasePanel: false,
      showUploadPanel: false,
      showSettingsPanel: false,
      showAiPanel: false,
    });
  },
}));
