"use client";

import { useState, useEffect } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toggleDrawingMode, updateDrawingBrush } from "@/fabric/fabric-utils";
import { X, Pencil } from "lucide-react";

export default function DrawingPanel() {
  const { setActivePanel, canvas, markAsModified, isDrawingMode, setIsDrawingMode } = useEditorStore();
  const [brushColor, setBrushColor] = useState("#000000");
  const [brushWidth, setBrushWidth] = useState(5);

  // Auto-activate drawing when panel opens
  useEffect(() => {
    if (canvas && !isDrawingMode) {
      handleActivateDrawing();
    }

    // Cleanup when panel closes
    return () => {
      if (canvas && isDrawingMode) {
        handleDeactivateDrawing();
      }
    };
  }, [canvas]);

  const handleClose = () => {
    // Turn off drawing mode when closing
    if (canvas && isDrawingMode) {
      handleDeactivateDrawing();
    }
    setActivePanel('select');
  };

  const handleActivateDrawing = () => {
    if (!canvas) return;

    setIsDrawingMode(true);
    toggleDrawingMode(canvas, true, brushColor, brushWidth);
    markAsModified();
  };

  const handleDeactivateDrawing = () => {
    if (!canvas) return;

    setIsDrawingMode(false);
    toggleDrawingMode(canvas, false);
    markAsModified();
  };

  const handleColorChange = (color) => {
    setBrushColor(color);
    if (canvas && isDrawingMode) {
      updateDrawingBrush(canvas, { color });
    }
  };

  const handleBrushWidthChange = (width) => {
    setBrushWidth(width);
    if (canvas && isDrawingMode) {
      updateDrawingBrush(canvas, { width });
    }
  };

  const presetColors = [
    "#000000", "#ffffff", "#ff0000", "#00ff00", "#0000ff",
    "#ffff00", "#ff00ff", "#00ffff", "#ffa500", "#800080",
    "#ffc0cb", "#a52a2a", "#808080", "#000080", "#008000"
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Draw</h3>
          <p className="text-xs text-gray-500">Draw on canvas with brush</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Drawing Tool */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Drawing Tool</Label>

          <Button
            variant={isDrawingMode ? "default" : "outline"}
            onClick={isDrawingMode ? handleDeactivateDrawing : handleActivateDrawing}
            className="w-full h-12 flex flex-col items-center justify-center"
          >
            <Pencil className="h-5 w-5 mb-1" />
            <span className="text-xs">{isDrawingMode ? 'Stop Drawing' : 'Start Drawing'}</span>
          </Button>
        </div>

        {/* Brush Settings */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Brush Settings</Label>

          <div className="space-y-3">
            <div className="space-y-2">
              <Label className="text-xs">Brush Width: {brushWidth}px</Label>
              <input
                type="range"
                min="1"
                max="50"
                value={brushWidth}
                onChange={(e) => handleBrushWidthChange(parseInt(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Color Picker */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Brush Color</Label>
          
          <div className="flex items-center space-x-2 mb-3">
            <input
              type="color"
              value={brushColor}
              onChange={(e) => handleColorChange(e.target.value)}
              className="w-8 h-8 rounded border"
            />
            <Input
              value={brushColor}
              onChange={(e) => handleColorChange(e.target.value)}
              className="flex-1 h-8 text-xs"
            />
          </div>
          
          <div className="grid grid-cols-5 gap-2">
            {presetColors.map((color) => (
              <button
                key={color}
                onClick={() => handleColorChange(color)}
                className={`w-8 h-8 rounded border-2 ${
                  brushColor === color ? 'border-blue-500' : 'border-gray-300'
                }`}
                style={{ backgroundColor: color }}
                title={color}
              />
            ))}
          </div>
        </div>

        {/* Status */}
        {isDrawingMode && (
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-700">
                Drawing mode active - Click and drag to draw
              </span>
            </div>
          </div>
        )}

        {/* Tips */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h5 className="text-sm font-medium text-gray-900 mb-1">Tips</h5>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Drawing automatically activates when you open this panel</li>
            <li>• Click and drag to draw on the canvas</li>
            <li>• Adjust brush width for different line thickness</li>
            <li>• Choose colors from the palette or use custom colors</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
