"use client";

import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { X, Plus } from "lucide-react";

export default function TemplatesPanel() {
  const { setActivePanel, canvas } = useEditorStore();

  const handleClose = () => {
    setActivePanel('select');
  };

  const createBlankTemplate = () => {
    if (!canvas) return;
    
    // Clear the canvas
    canvas.clear();
    canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
    
    console.log("Created blank template");
  };

  const templates = [
    {
      id: 1,
      name: "Social Media Post",
      thumbnail: "/api/placeholder/150/150",
      category: "social"
    },
    {
      id: 2,
      name: "Business Card",
      thumbnail: "/api/placeholder/150/100",
      category: "business"
    },
    {
      id: 3,
      name: "Flyer",
      thumbnail: "/api/placeholder/150/200",
      category: "marketing"
    },
    {
      id: 4,
      name: "Poster",
      thumbnail: "/api/placeholder/150/200",
      category: "marketing"
    },
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Templates</h3>
          <p className="text-xs text-gray-500">Choose a template to start with</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* Blank Template */}
        <div className="mb-6">
          <button
            onClick={createBlankTemplate}
            className="w-full h-32 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 flex items-center justify-center transition-colors"
          >
            <div className="text-center">
              <Plus className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <div className="text-sm text-gray-600">Blank Template</div>
            </div>
          </button>
        </div>

        {/* Template Categories */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-700">Popular Templates</h4>
          
          <div className="grid grid-cols-2 gap-3">
            {templates.map((template) => (
              <button
                key={template.id}
                className="group relative bg-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                onClick={() => {
                  console.log("Selected template:", template.name);
                  // TODO: Load template
                }}
              >
                <div className="aspect-[3/4] bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                  <span className="text-xs text-gray-600">{template.name}</span>
                </div>
                <div className="p-2">
                  <p className="text-xs font-medium text-gray-700 truncate">
                    {template.name}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Template Categories */}
        <div className="mt-6 space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Categories</h4>
          
          {['Social Media', 'Business', 'Marketing', 'Personal'].map((category) => (
            <button
              key={category}
              className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
              onClick={() => {
                console.log("Selected category:", category);
                // TODO: Filter templates by category
              }}
            >
              {category}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
