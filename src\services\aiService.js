import Replicate from 'replicate'
import { fal } from '@fal-ai/client'
import Together from 'together-ai'

// Initialize clients
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN || process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN,
})

// Configure fal.ai
if (process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY) {
  fal.config({
    credentials: process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY,
  })
}

// Initialize Together AI client
const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY,
})

// AI Providers
export const AI_PROVIDERS = {
  TOGETHER: 'together',
  FAL: 'fal',
  REPLICATE: 'replicate'
}

// AI Models configuration
export const AI_MODELS = {
  // Together AI Models (Default Provider)
  FLUX_SCHNELL_TOGETHER: {
    id: 'black-forest-labs/FLUX.1-schnell',
    name: '<PERSON>U<PERSON>ell (Together)',
    description: 'Fast FLUX model via Together AI',
    category: 'text-to-image',
    provider: AI_PROVIDERS.TOGETHER,
    default: true
  },
  FLUX_DEV_TOGETHER: {
    id: 'black-forest-labs/FLUX.1-dev',
    name: 'FLUX Dev (Together)',
    description: 'High-quality FLUX model via Together AI',
    category: 'text-to-image',
    provider: AI_PROVIDERS.TOGETHER
  },
  FLUX_KONTEXT_DEV: {
    id: 'black-forest-labs/FLUX.1-kontext-dev',
    name: 'FLUX Kontext Dev',
    description: 'Image-to-image development model (cheaper)',
    category: 'image-to-image',
    provider: AI_PROVIDERS.TOGETHER,
    requiresImage: true,
    default: true
  },
  FLUX_KONTEXT_PRO: {
    id: 'black-forest-labs/FLUX.1-kontext-pro',
    name: 'FLUX Kontext Pro',
    description: 'Professional image-to-image with context understanding',
    category: 'image-to-image',
    provider: AI_PROVIDERS.TOGETHER,
    requiresImage: true
  },
  FLUX_KONTEXT_MAX: {
    id: 'black-forest-labs/FLUX.1-kontext-max',
    name: 'FLUX Kontext Max',
    description: 'Maximum quality image-to-image with context understanding',
    category: 'image-to-image',
    provider: AI_PROVIDERS.TOGETHER,
    requiresImage: true
  },

  // Fal.ai Models
  FLUX_PRO_FAL: {
    id: 'fal-ai/flux-pro/v1.1',
    name: 'FLUX Pro 1.1',
    description: 'High-quality FLUX Pro model',
    category: 'text-to-image',
    provider: AI_PROVIDERS.FAL
  },
  FLUX_DEV_FAL: {
    id: 'fal-ai/flux/dev',
    name: 'FLUX Dev (Fal)',
    description: 'FLUX development model via Fal.ai',
    category: 'text-to-image',
    provider: AI_PROVIDERS.FAL
  },
  REMOVE_BG_FAL: {
    id: 'fal-ai/birefnet',
    name: 'BiRefNet Background Removal',
    description: 'Advanced background removal',
    category: 'image-processing',
    provider: AI_PROVIDERS.FAL
  },
  UPSCALE_FAL: {
    id: 'fal-ai/clarity-upscaler',
    name: 'Clarity Upscaler',
    description: 'High-quality image upscaling',
    category: 'image-processing',
    provider: AI_PROVIDERS.FAL
  },

  // Replicate Models (Legacy)
  FLUX_SCHNELL: {
    id: 'black-forest-labs/flux-schnell',
    name: 'FLUX Schnell (Replicate)',
    description: 'Fast text-to-image generation',
    category: 'text-to-image',
    provider: AI_PROVIDERS.REPLICATE
  },
  FLUX_DEV: {
    id: 'black-forest-labs/flux-dev',
    name: 'FLUX Dev (Replicate)',
    description: 'High-quality text-to-image generation',
    category: 'text-to-image',
    provider: AI_PROVIDERS.REPLICATE
  },
  STABLE_DIFFUSION: {
    id: 'stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478',
    name: 'Stable Diffusion',
    description: 'Classic stable diffusion model',
    category: 'text-to-image',
    provider: AI_PROVIDERS.REPLICATE
  },
  REMOVE_BG: {
    id: 'cjwbw/rembg:fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003',
    name: 'Remove Background (Replicate)',
    description: 'Remove background from images',
    category: 'image-processing',
    provider: AI_PROVIDERS.REPLICATE
  },
  UPSCALE: {
    id: 'nightmareai/real-esrgan:42fed1c4974146d4d2414e2be2c5277c7fcf05fcc3a73abf41610695738c1d7b',
    name: 'Real-ESRGAN',
    description: 'Upscale and enhance images',
    category: 'image-processing',
    provider: AI_PROVIDERS.REPLICATE
  }
}

// Get default model for text-to-image
export function getDefaultModel() {
  return Object.values(AI_MODELS).find(model => model.default && model.category === 'text-to-image') || AI_MODELS.FLUX_SCHNELL_TOGETHER
}

// Get default model for image-to-image
export function getDefaultImageToImageModel() {
  return Object.values(AI_MODELS).find(model => model.default && model.category === 'image-to-image') || AI_MODELS.FLUX_KONTEXT_DEV
}

// Text-to-Image generation with multiple providers
export async function generateImage(prompt, modelId = null, options = {}) {
  try {
    // Use default model if none specified
    const model = modelId ? Object.values(AI_MODELS).find(m => m.id === modelId) : getDefaultModel()
    if (!model) {
      throw new Error(`Model not found: ${modelId}`)
    }

    const defaultOptions = {
      prompt,
      width: 1024,
      height: 1024,
      ...options
    }

    console.log(`Generating image with ${model.provider}:`, { model: model.id, options: defaultOptions })

    switch (model.provider) {
      case AI_PROVIDERS.TOGETHER:
        return await generateImageTogether(model.id, defaultOptions)

      case AI_PROVIDERS.FAL:
        return await generateImageFal(model.id, defaultOptions)

      case AI_PROVIDERS.REPLICATE:
        return await generateImageReplicate(model.id, defaultOptions)

      default:
        throw new Error(`Unsupported provider: ${model.provider}`)
    }
  } catch (error) {
    console.error('Error generating image:', error)
    throw new Error(`Failed to generate image: ${error.message}`)
  }
}

// Together AI image generation
async function generateImageTogether(modelId, options) {
  console.log('generateImageTogether called with:', { modelId, options })

  // Check API key
  const apiKey = process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY
  console.log('Together API key configured:', !!apiKey, 'length:', apiKey?.length || 0)

  if (!apiKey) {
    throw new Error('Together AI API key not configured')
  }

  // Check if this is a Kontext model that requires an image
  const isKontextModel = modelId.includes('kontext')

  if (isKontextModel && !options.image_url) {
    throw new Error('FLUX Kontext models require an input image. Use FLUX Schnell or FLUX Dev for text-to-image generation.')
  }

  const requestData = {
    model: modelId,
    prompt: options.prompt,
    width: options.width,
    height: options.height,
    steps: options.num_inference_steps || 28,
    n: 1,
    response_format: "url"
  }

  // Add image_url for Kontext models
  if (isKontextModel && options.image_url) {
    requestData.image_url = options.image_url
  }

  console.log('Together AI request data:', requestData)

  const response = await together.images.create(requestData)

  console.log('Together AI response:', response)
  console.log('Response structure:', {
    hasData: !!response.data,
    dataLength: response.data?.length || 0,
    dataType: typeof response.data,
    firstItem: response.data?.[0] || null
  })

  if (response.data && response.data.length > 0) {
    const imageUrl = response.data[0].url
    console.log('Generated image URL:', imageUrl)
    console.log('Image URL type:', typeof imageUrl)
    console.log('Image URL valid:', !!imageUrl && imageUrl.startsWith('http'))
    return imageUrl
  }

  throw new Error('No image generated from Together AI')
}

// Fal.ai image generation
async function generateImageFal(modelId, options) {
  const result = await fal.subscribe(modelId, {
    input: {
      prompt: options.prompt,
      image_size: `${options.width}x${options.height}`,
      num_inference_steps: options.num_inference_steps || 20,
      guidance_scale: options.guidance_scale || 7.5
    }
  })

  if (result.images && result.images.length > 0) {
    return result.images[0].url
  }

  throw new Error('No image generated from Fal.ai')
}

// Replicate image generation (legacy)
async function generateImageReplicate(modelId, options) {
  const replicateOptions = {
    prompt: options.prompt,
    width: options.width,
    height: options.height,
    num_inference_steps: options.num_inference_steps || 4,
    guidance_scale: options.guidance_scale || 0
  }

  const output = await replicate.run(modelId, {
    input: replicateOptions
  })

  // Handle different output formats
  if (Array.isArray(output)) {
    return output[0] // Return first image if array
  }

  return output // Return direct URL if string
}

// Background removal using ClipDrop API (professional quality)
async function removeBackgroundWithTogether(imageUrl) {
  try {
    console.log('Starting ClipDrop background removal with dimension preservation')

    // First, get the original image dimensions
    const originalDimensions = await getImageDimensions(imageUrl)
    console.log('Original image dimensions:', originalDimensions)

    // Use ClipDrop API for professional background removal
    const resultUrl = await removeBackgroundWithClipDrop(imageUrl)

    console.log('ClipDrop background removal completed with preserved dimensions')
    return resultUrl
  } catch (error) {
    console.error('ClipDrop background removal failed:', error)
    throw new Error(`Failed to remove background with ClipDrop: ${error.message}`)
  }
}

// ClipDrop background removal implementation
async function removeBackgroundWithClipDrop(imageUrl) {
  try {
    console.log('Using ClipDrop API for background removal:', imageUrl)

    // Convert image URL to blob for upload
    const imageBlob = await fetchImageAsBlob(imageUrl)

    // Create FormData for ClipDrop API
    const formData = new FormData()
    formData.append('image_file', imageBlob, 'image.jpg')

    // Call ClipDrop API
    const response = await fetch('https://clipdrop-api.co/remove-background/v1', {
      method: 'POST',
      headers: {
        'x-api-key': 'b179570265f20f905b88d91dbdf318196e9064df616a3832b7bd09d48fccdf30644c60b88d12668342f2159a53f1e6c8'
      },
      body: formData
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('ClipDrop API error:', response.status, errorText)
      throw new Error(`ClipDrop API error: ${response.status} - ${errorText}`)
    }

    // Get the result as blob
    const resultBlob = await response.blob()

    // Create object URL for the result
    const resultUrl = URL.createObjectURL(resultBlob)

    console.log('ClipDrop background removal successful')
    return resultUrl
  } catch (error) {
    console.error('ClipDrop API call failed:', error)
    throw error
  }
}

// Helper function to fetch image as blob
async function fetchImageAsBlob(imageUrl) {
  try {
    console.log('Fetching image as blob:', imageUrl)

    // Use proxy for Together AI URLs to avoid CORS issues
    let fetchUrl = imageUrl
    if (imageUrl.includes('api.together.ai')) {
      console.log('Together AI image detected, using proxy...')
      fetchUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`
    }

    const response = await fetch(fetchUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`)
    }

    const blob = await response.blob()
    console.log('Image fetched as blob, size:', blob.size, 'bytes')
    return blob
  } catch (error) {
    console.error('Failed to fetch image as blob:', error)
    throw error
  }
}

// Helper function to get image dimensions
function getImageDimensions(imageUrl) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }

    img.onerror = () => {
      reject(new Error('Failed to load image for dimension detection'))
    }

    img.src = imageUrl
  })
}

// Helper function to adjust dimensions to be multiples of 8 (required by FLUX models)
function adjustDimensionsForFlux(width, height) {
  // Round to nearest multiple of 8
  const adjustedWidth = Math.round(width / 8) * 8
  const adjustedHeight = Math.round(height / 8) * 8

  // Ensure minimum dimensions
  const finalWidth = Math.max(adjustedWidth, 512)
  const finalHeight = Math.max(adjustedHeight, 512)

  console.log(`Adjusted dimensions from ${width}x${height} to ${finalWidth}x${finalHeight}`)

  return {
    width: finalWidth,
    height: finalHeight
  }
}

// Helper function to resize image to exact dimensions using canvas
function resizeImageToExactDimensions(imageUrl, targetDimensions) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      try {
        // Create canvas with target dimensions
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        canvas.width = targetDimensions.width
        canvas.height = targetDimensions.height

        // Draw image scaled to exact target dimensions
        ctx.drawImage(img, 0, 0, targetDimensions.width, targetDimensions.height)

        // Convert to blob and create URL
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob)
            console.log(`Image resized to exact dimensions: ${targetDimensions.width}x${targetDimensions.height}`)
            resolve(url)
          } else {
            reject(new Error('Failed to create blob from resized image'))
          }
        }, 'image/png')
      } catch (error) {
        reject(error)
      }
    }

    img.onerror = () => {
      reject(new Error('Failed to load image for resizing'))
    }

    img.src = imageUrl
  })
}

// Remove background from image with multiple providers
export async function removeBackground(imageUrl, provider = AI_PROVIDERS.FAL) {
  try {
    console.log(`Removing background with ${provider}:`, imageUrl)

    // For Together AI, use ClipDrop API for professional background removal
    if (provider === AI_PROVIDERS.TOGETHER) {
      console.log('Using ClipDrop API for professional background removal')
      return await removeBackgroundWithTogether(imageUrl)
    }

    // Check if API is configured for the actual provider
    if (!isApiConfigured(provider)) {
      console.log(`${provider} API not configured, attempting client-side background removal`)

      // Try client-side background removal as fallback
      try {
        const { removeBackgroundClientSide, isClientSideBackgroundRemovalAvailable } = await import('./clientAiService.js')

        if (isClientSideBackgroundRemovalAvailable()) {
          return await removeBackgroundClientSide(imageUrl)
        } else {
          throw new Error('Client-side background removal not available')
        }
      } catch (clientError) {
        console.error('Client-side background removal failed:', clientError)
        throw new Error(`Please configure ${provider} API key in .env.local for background removal, or ensure your browser supports WebGL for client-side processing`)
      }
    }

    switch (provider) {
      case AI_PROVIDERS.FAL:
        const result = await fal.subscribe(AI_MODELS.REMOVE_BG_FAL.id, {
          input: {
            image_url: imageUrl
          }
        })
        return result.image?.url || result.image

      case AI_PROVIDERS.REPLICATE:
        const output = await replicate.run(AI_MODELS.REMOVE_BG.id, {
          input: {
            image: imageUrl
          }
        })
        return output

      default:
        throw new Error(`Background removal not supported for provider: ${provider}`)
    }
  } catch (error) {
    console.error('Error removing background:', error)
    throw new Error(`Failed to remove background: ${error.message}`)
  }
}

// Upscale image with multiple providers
export async function upscaleImage(imageUrl, scale = 4, provider = AI_PROVIDERS.FAL) {
  try {
    console.log(`Upscaling image with ${provider}:`, imageUrl)

    // If provider doesn't support upscaling, fallback to FAL
    let actualProvider = provider
    if (provider === AI_PROVIDERS.TOGETHER) {
      console.log('Together AI does not support image upscaling, falling back to FAL')
      actualProvider = AI_PROVIDERS.FAL
    }

    // Check if API is configured for the actual provider
    if (!isApiConfigured(actualProvider)) {
      console.log(`${actualProvider} API not configured, attempting client-side upscaling`)

      // Try client-side upscaling as fallback
      try {
        const { upscaleImageClientSide } = await import('./clientAiService.js')
        return await upscaleImageClientSide(imageUrl, scale)
      } catch (clientError) {
        console.error('Client-side upscaling failed:', clientError)
        throw new Error(`Please configure ${actualProvider} API key in .env.local for AI-powered upscaling. Basic upscaling attempted but failed.`)
      }
    }

    switch (actualProvider) {
      case AI_PROVIDERS.FAL:
        const result = await fal.subscribe(AI_MODELS.UPSCALE_FAL.id, {
          input: {
            image_url: imageUrl,
            scale_factor: scale
          }
        })
        return result.image?.url || result.image

      case AI_PROVIDERS.REPLICATE:
        const output = await replicate.run(AI_MODELS.UPSCALE.id, {
          input: {
            image: imageUrl,
            scale: scale
          }
        })
        return output

      default:
        throw new Error(`Image upscaling not supported for provider: ${actualProvider}`)
    }
  } catch (error) {
    console.error('Error upscaling image:', error)
    throw new Error(`Failed to upscale image: ${error.message}`)
  }
}

// Generate variations of an image
export async function generateImageVariations(imageUrl, prompt = '', strength = 0.8, modelId = null) {
  try {
    // Use provided model or default to FLUX Kontext Dev
    const model = modelId ? Object.values(AI_MODELS).find(m => m.id === modelId) : getDefaultImageToImageModel()
    if (!model) {
      throw new Error(`Model not found: ${modelId}`)
    }

    console.log(`Generating image variations with ${model.provider} using ${model.name}:`, imageUrl)

    switch (model.provider) {
      case AI_PROVIDERS.TOGETHER:
        // Use FLUX Kontext for image-to-image
        return await generateImageTogether(model.id, {
          prompt: prompt || 'high quality, detailed',
          image_url: imageUrl,
          width: 1024,
          height: 768,
          num_inference_steps: 28
        })

      case AI_PROVIDERS.FAL:
        const result = await fal.subscribe(AI_MODELS.FLUX_DEV_FAL.id, {
          input: {
            prompt: prompt || 'high quality, detailed',
            image_url: imageUrl,
            strength: strength,
            num_inference_steps: 20,
            guidance_scale: 7.5
          }
        })
        return result.images?.[0]?.url || result.image?.url

      case AI_PROVIDERS.REPLICATE:
        const output = await replicate.run(AI_MODELS.STABLE_DIFFUSION.id, {
          input: {
            image: imageUrl,
            prompt: prompt || 'high quality, detailed',
            strength: strength,
            num_inference_steps: 20,
            guidance_scale: 7.5
          }
        })
        return output

      default:
        throw new Error(`Image variations not supported for provider: ${model.provider}`)
    }
  } catch (error) {
    console.error('Error generating variations:', error)
    throw new Error(`Failed to generate variations: ${error.message}`)
  }
}

// Get prediction status (for long-running tasks)
export async function getPredictionStatus(predictionId) {
  try {
    const prediction = await replicate.predictions.get(predictionId)
    return prediction
  } catch (error) {
    console.error('Error getting prediction status:', error)
    throw new Error(`Failed to get prediction status: ${error.message}`)
  }
}

// Utility function to check if API tokens are configured
export function isApiConfigured(provider = null) {
  if (provider) {
    switch (provider) {
      case AI_PROVIDERS.TOGETHER:
        const togetherToken = process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY;
        return !!togetherToken && togetherToken !== 'your-together-api-key-here'

      case AI_PROVIDERS.FAL:
        const falToken = process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY;
        return !!falToken && falToken !== 'your-fal-api-key-here'

      case AI_PROVIDERS.REPLICATE:
        const replicateToken = process.env.REPLICATE_API_TOKEN || process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN;
        return !!replicateToken &&
               replicateToken !== 'your-replicate-api-token-here' &&
               replicateToken !== 'your-replicate-api-token'

      default:
        return false
    }
  }

  // Check if any provider is configured (prioritize Together AI as default)
  return isApiConfigured(AI_PROVIDERS.TOGETHER) ||
         isApiConfigured(AI_PROVIDERS.FAL) ||
         isApiConfigured(AI_PROVIDERS.REPLICATE)
}

// Get available providers
export function getAvailableProviders() {
  const providers = []

  if (isApiConfigured(AI_PROVIDERS.TOGETHER)) {
    providers.push({ id: AI_PROVIDERS.TOGETHER, name: 'Together AI', default: true })
  }

  if (isApiConfigured(AI_PROVIDERS.FAL)) {
    providers.push({ id: AI_PROVIDERS.FAL, name: 'Fal.ai' })
  }

  if (isApiConfigured(AI_PROVIDERS.REPLICATE)) {
    providers.push({ id: AI_PROVIDERS.REPLICATE, name: 'Replicate' })
  }

  return providers
}

// Get available models (filtered by configured providers)
export function getAvailableModels(category = null, provider = null) {
  let models = Object.values(AI_MODELS)

  // Filter by provider if specified
  if (provider) {
    models = models.filter(model => model.provider === provider)
  } else {
    // Only show models from configured providers
    const availableProviders = getAvailableProviders().map(p => p.id)
    models = models.filter(model => availableProviders.includes(model.provider))
  }

  // Filter by category if specified
  if (category) {
    models = models.filter(model => model.category === category)
  }

  // Sort by provider priority (Together AI first, then Fal.ai, then Replicate)
  const providerPriority = [AI_PROVIDERS.TOGETHER, AI_PROVIDERS.FAL, AI_PROVIDERS.REPLICATE]
  models.sort((a, b) => {
    const aPriority = providerPriority.indexOf(a.provider)
    const bPriority = providerPriority.indexOf(b.provider)
    return aPriority - bPriority
  })

  return models
}

// Get models suitable for image variations
export function getImageVariationModels() {
  return getAvailableModels('image-to-image')
}
