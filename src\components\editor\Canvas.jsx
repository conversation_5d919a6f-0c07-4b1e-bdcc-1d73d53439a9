"use client";

import { useEffect, useRef, useState } from "react";
import { useEditorStore } from "@/store";
import { initializeFabric, customizeBoundingBox, setupLayerOrderMaintenance } from "@/fabric/fabric-utils";
import { ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import AIImageToolbar from "./AIImageToolbar";

export default function Canvas() {
  const canvasRef = useRef(null);
  const canvasContainerRef = useRef(null);
  const fabricCanvasRef = useRef(null);
  const initAttemptedRef = useRef(false);

  const { setCanvas, markAsModified, canvasWidth, canvasHeight, canvasBackgroundColor, isPanMode, setSelectedObject, currentDrawingSession, selectedObject } = useEditorStore();

  // Zoom state
  const [zoomLevel, setZoomLevel] = useState(1);
  const minZoom = 0.1;
  const maxZoom = 5;

  // AI toolbar state
  const [showAIToolbar, setShowAIToolbar] = useState(false);
  const [aiToolbarPosition, setAIToolbarPosition] = useState({ x: 0, y: 0 });

  // Update AI toolbar position when selected object changes
  useEffect(() => {
    if (selectedObject && selectedObject.type === 'image' && showAIToolbar) {
      const updatePosition = () => {
        const objectBounds = selectedObject.getBoundingRect();
        setAIToolbarPosition({
          x: objectBounds.left + objectBounds.width / 2,
          y: objectBounds.top
        });
      };

      // Update position immediately
      updatePosition();

      // Listen for object movement
      if (fabricCanvasRef.current) {
        const handleObjectMoving = () => updatePosition();
        const handleObjectScaling = () => updatePosition();
        const handleObjectRotating = () => updatePosition();

        selectedObject.on('moving', handleObjectMoving);
        selectedObject.on('scaling', handleObjectScaling);
        selectedObject.on('rotating', handleObjectRotating);

        return () => {
          selectedObject.off('moving', handleObjectMoving);
          selectedObject.off('scaling', handleObjectScaling);
          selectedObject.off('rotating', handleObjectRotating);
        };
      }
    }
  }, [selectedObject, showAIToolbar]);

  // Zoom functions
  const handleZoomIn = () => {
    if (fabricCanvasRef.current && zoomLevel < maxZoom) {
      const canvas = fabricCanvasRef.current;
      const newZoom = Math.min(zoomLevel * 1.2, maxZoom);

      // Get canvas center point
      const center = {
        x: canvas.getWidth() / 2,
        y: canvas.getHeight() / 2
      };

      // Zoom to center point
      canvas.zoomToPoint(center, newZoom);
      setZoomLevel(newZoom);
      canvas.renderAll();
    }
  };

  const handleZoomOut = () => {
    if (fabricCanvasRef.current && zoomLevel > minZoom) {
      const canvas = fabricCanvasRef.current;
      const newZoom = Math.max(zoomLevel / 1.2, minZoom);

      // Get canvas center point
      const center = {
        x: canvas.getWidth() / 2,
        y: canvas.getHeight() / 2
      };

      // Zoom to center point
      canvas.zoomToPoint(center, newZoom);
      setZoomLevel(newZoom);
      canvas.renderAll();
    }
  };

  const handleResetZoom = () => {
    if (fabricCanvasRef.current) {
      const canvas = fabricCanvasRef.current;

      // Get canvas center point
      const center = {
        x: canvas.getWidth() / 2,
        y: canvas.getHeight() / 2
      };

      // Reset zoom to center point
      canvas.zoomToPoint(center, 1);
      setZoomLevel(1);
      canvas.renderAll();
    }
  };

  // Update canvas to fill container on initial load only
  useEffect(() => {
    if (fabricCanvasRef.current && canvasContainerRef.current) {
      const container = canvasContainerRef.current;
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;

      // Set canvas to fill entire container
      fabricCanvasRef.current.setDimensions({
        width: containerWidth,
        height: containerHeight
      });

      // Re-center canvas
      const { centerCanvas } = require("@/fabric/fabric-utils");
      centerCanvas(fabricCanvasRef.current);

      fabricCanvasRef.current.renderAll();
    }
  }, []); // Only run on mount



  // Handle window resize to keep canvas full-screen and maintain object positions
  useEffect(() => {
    const handleResize = () => {
      if (fabricCanvasRef.current && canvasContainerRef.current) {
        const { handleResponsiveResize } = require("@/fabric/fabric-utils");
        handleResponsiveResize(
          fabricCanvasRef.current,
          canvasContainerRef.current,
          canvasWidth,
          canvasHeight,
          canvasBackgroundColor
        );
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Function to add the design area rectangle
  const addDesignArea = async (canvas) => {
    if (!canvas) return;

    try {
      const { Rect } = await import("fabric");

      // Remove existing design area and corner indicators if they exist
      const existingDesignArea = canvas.getObjects().find(obj => obj.id === 'design-area');
      if (existingDesignArea) {
        canvas.remove(existingDesignArea);
      }

      // Remove existing corner indicators
      const existingCorners = canvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));
      existingCorners.forEach(corner => canvas.remove(corner));

      // Calculate center position for design area
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;

      // Create design area rectangle with more visible styling
      const designArea = new Rect({
        id: 'design-area',
        left: centerX - canvasWidth / 2,
        top: centerY - canvasHeight / 2,
        width: canvasWidth,
        height: canvasHeight,
        fill: canvasBackgroundColor,
        stroke: '#3b82f6',
        strokeWidth: 3,
        strokeDashArray: [10, 5],
        selectable: false,
        evented: false,
        excludeFromExport: true,
        hoverCursor: 'default',
        moveCursor: 'default',
        isDesignArea: true, // Custom flag to identify design area
        visible: true, // Keep visible for visual reference
        opacity: 1, // Ensure full opacity
        shadow: {
          color: 'rgba(59, 130, 246, 0.3)',
          blur: 10,
          offsetX: 0,
          offsetY: 4
        }
      });

      // Add design area to canvas
      canvas.add(designArea);

      console.log("Design area added to canvas:", {
        id: designArea.id,
        left: designArea.left,
        top: designArea.top,
        width: designArea.width,
        height: designArea.height,
        visible: designArea.visible
      });

      // Ensure design area stays at the very back
      try {
        canvas.sendToBack(designArea);
        // Force it to be at index 0 (bottom layer)
        canvas.moveTo && canvas.moveTo(designArea, 0);
      } catch (e) {
        console.warn("Could not move design area to back:", e);
      }

      // Add corner indicators
      await addCornerIndicators(canvas, centerX - canvasWidth / 2, centerY - canvasHeight / 2, canvasWidth, canvasHeight);

      // Ensure corner indicators stay at the bottom
      const cornerIndicators = canvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));
      cornerIndicators.forEach((indicator) => {
        try {
          canvas.sendToBack(indicator);
        } catch (e) {
          console.warn("Could not move corner indicator to back:", e);
        }
      });

      // Setup automatic layer order maintenance
      setupLayerOrderMaintenance(canvas);

      canvas.renderAll();

    } catch (e) {
      console.error("Failed to add design area", e);
    }
  };

  // Function to add corner indicators
  const addCornerIndicators = async (canvas, left, top, width, height) => {
    if (!canvas) return;

    try {
      const { Rect } = await import("fabric");

      // Remove existing corner indicators
      const existingCorners = canvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));
      existingCorners.forEach(corner => canvas.remove(corner));

      const cornerSize = 12;
      const cornerThickness = 3;
      const cornerColor = '#3b82f6';

      // Common properties for all corner indicators
      const cornerProps = {
        selectable: false,
        evented: false,
        excludeFromExport: true,
        hoverCursor: 'default',
        moveCursor: 'default',
        isDesignAreaElement: true,
        fill: cornerColor
      };

      // Top-left corner
      const topLeftH = new Rect({
        id: 'corner-indicator-tl-h',
        left: left - cornerThickness,
        top: top - cornerThickness,
        width: cornerSize,
        height: cornerThickness,
        ...cornerProps
      });

      const topLeftV = new Rect({
        id: 'corner-indicator-tl-v',
        left: left - cornerThickness,
        top: top - cornerThickness,
        width: cornerThickness,
        height: cornerSize,
        ...cornerProps
      });

      // Top-right corner
      const topRightH = new Rect({
        id: 'corner-indicator-tr-h',
        left: left + width - cornerSize + cornerThickness,
        top: top - cornerThickness,
        width: cornerSize,
        height: cornerThickness,
        ...cornerProps
      });

      const topRightV = new Rect({
        id: 'corner-indicator-tr-v',
        left: left + width,
        top: top - cornerThickness,
        width: cornerThickness,
        height: cornerSize,
        ...cornerProps
      });

      // Bottom-left corner
      const bottomLeftH = new Rect({
        id: 'corner-indicator-bl-h',
        left: left - cornerThickness,
        top: top + height,
        width: cornerSize,
        height: cornerThickness,
        ...cornerProps
      });

      const bottomLeftV = new Rect({
        id: 'corner-indicator-bl-v',
        left: left - cornerThickness,
        top: top + height - cornerSize + cornerThickness,
        width: cornerThickness,
        height: cornerSize,
        ...cornerProps
      });

      // Bottom-right corner
      const bottomRightH = new Rect({
        id: 'corner-indicator-br-h',
        left: left + width - cornerSize + cornerThickness,
        top: top + height,
        width: cornerSize,
        height: cornerThickness,
        ...cornerProps
      });

      const bottomRightV = new Rect({
        id: 'corner-indicator-br-v',
        left: left + width,
        top: top + height - cornerSize + cornerThickness,
        width: cornerThickness,
        height: cornerSize,
        ...cornerProps
      });

      // Add all corner indicators
      canvas.add(topLeftH, topLeftV, topRightH, topRightV, bottomLeftH, bottomLeftV, bottomRightH, bottomRightV);

    } catch (e) {
      console.error("Failed to add corner indicators", e);
    }
  };

  useEffect(() => {
    const cleanUpCanvas = () => {
      if (fabricCanvasRef.current) {
        try {
          fabricCanvasRef.current.off("object:added");
          fabricCanvasRef.current.off("object:modified");
          fabricCanvasRef.current.off("object:removed");
          fabricCanvasRef.current.off("path:created");
        } catch (e) {
          console.error("Error removing event listeners", e);
        }

        try {
          fabricCanvasRef.current.dispose();
        } catch (e) {
          console.error("Error disposing canvas", e);
        }

        fabricCanvasRef.current = null;
        setCanvas(null);
      }
    };

    cleanUpCanvas();

    //reset init flag
    initAttemptedRef.current = false;

    //init our canvas
    const initCanvas = async () => {
      if (
        typeof window === "undefined" ||
        !canvasRef.current ||
        initAttemptedRef.current
      ) {
        return;
      }

      initAttemptedRef.current = true;

      try {
        const fabricCanvas = await initializeFabric(
          canvasRef.current,
          canvasContainerRef.current
        );

        if (!fabricCanvas) {
          console.error("Failed to initialize Fabric.js canvas");
          return;
        }

        // Set canvas to fill entire container
        const containerWidth = canvasContainerRef.current.clientWidth;
        const containerHeight = canvasContainerRef.current.clientHeight;

        console.log("Canvas container dimensions:", { containerWidth, containerHeight });

        // Ensure we have valid dimensions
        if (containerWidth === 0 || containerHeight === 0) {
          console.warn("Canvas container has zero dimensions, using fallback");
          fabricCanvas.setDimensions({
            width: 800,
            height: 600
          });
        } else {
          fabricCanvas.setDimensions({
            width: containerWidth,
            height: containerHeight
          });
        }

        fabricCanvasRef.current = fabricCanvas;
        //set the canvas in store
        setCanvas(fabricCanvas);

        console.log("Canvas init is done and set in store");

        //apply custom style for the controls
        customizeBoundingBox(fabricCanvas);

        // Design area removed - canvas is now clear for user content

        // Add mouse wheel zoom support
        fabricCanvas.on('mouse:wheel', (opt) => {
          const delta = opt.e.deltaY;
          let zoom = fabricCanvas.getZoom();
          zoom *= 0.999 ** delta;

          if (zoom > maxZoom) zoom = maxZoom;
          if (zoom < minZoom) zoom = minZoom;

          fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
          setZoomLevel(zoom);
          opt.e.preventDefault();
          opt.e.stopPropagation();
        });

        // Add pan functionality
        let isPanning = false;
        let lastPosX = 0;
        let lastPosY = 0;

        fabricCanvas.on('mouse:down', (opt) => {
          const { isPanMode } = useEditorStore.getState();
          if (isPanMode) {
            isPanning = true;
            fabricCanvas.selection = false;
            lastPosX = opt.e.clientX;
            lastPosY = opt.e.clientY;
            fabricCanvas.defaultCursor = 'grabbing';
          }
        });

        fabricCanvas.on('mouse:move', (opt) => {
          const { isPanMode } = useEditorStore.getState();
          if (isPanMode && isPanning) {
            const vpt = fabricCanvas.viewportTransform;
            vpt[4] += opt.e.clientX - lastPosX;
            vpt[5] += opt.e.clientY - lastPosY;
            fabricCanvas.requestRenderAll();
            lastPosX = opt.e.clientX;
            lastPosY = opt.e.clientY;
          }
        });

        fabricCanvas.on('mouse:up', () => {
          const { isPanMode } = useEditorStore.getState();
          if (isPanMode) {
            isPanning = false;
            fabricCanvas.defaultCursor = 'grab';
          }
        });

        // Prevent selection of design area elements
        fabricCanvas.on('selection:created', async (e) => {
          const selectedObjects = e.selected || [];
          const userObjects = selectedObjects.filter(obj =>
            !obj.isDesignArea &&
            !obj.isDesignAreaElement &&
            obj.id !== 'design-area' &&
            !obj.id?.startsWith('corner-indicator')
          );

          if (userObjects.length !== selectedObjects.length) {
            fabricCanvas.discardActiveObject();
            if (userObjects.length > 0) {
              if (userObjects.length === 1) {
                fabricCanvas.setActiveObject(userObjects[0]);
              } else {
                try {
                  const fabric = await import("fabric");
                  const selection = new fabric.ActiveSelection(userObjects, {
                    canvas: fabricCanvas
                  });
                  fabricCanvas.setActiveObject(selection);
                } catch (e) {
                  // Fallback: just select the first object
                  fabricCanvas.setActiveObject(userObjects[0]);
                }
              }
            }
            fabricCanvas.renderAll();
          }

          // Update selected object in store
          const activeObject = fabricCanvas.getActiveObject();
          if (activeObject && !activeObject.isDesignArea && !activeObject.isDesignAreaElement) {
            setSelectedObject(activeObject);

            // Update AI toolbar position for images but don't auto-show
            if (activeObject.type === 'image') {
              const objectBounds = activeObject.getBoundingRect();
              setAIToolbarPosition({
                x: objectBounds.left + objectBounds.width / 2,
                y: objectBounds.top
              });
              // Don't automatically show AI toolbar - let user open it manually
            } else {
              setShowAIToolbar(false);
            }
          } else {
            setShowAIToolbar(false);
          }
        });

        fabricCanvas.on('selection:updated', async (e) => {
          const selectedObjects = e.selected || [];
          const userObjects = selectedObjects.filter(obj =>
            !obj.isDesignArea &&
            !obj.isDesignAreaElement &&
            obj.id !== 'design-area' &&
            !obj.id?.startsWith('corner-indicator')
          );

          if (userObjects.length !== selectedObjects.length) {
            fabricCanvas.discardActiveObject();
            if (userObjects.length > 0) {
              if (userObjects.length === 1) {
                fabricCanvas.setActiveObject(userObjects[0]);
              } else {
                try {
                  const fabric = await import("fabric");
                  const selection = new fabric.ActiveSelection(userObjects, {
                    canvas: fabricCanvas
                  });
                  fabricCanvas.setActiveObject(selection);
                } catch (e) {
                  // Fallback: just select the first object
                  fabricCanvas.setActiveObject(userObjects[0]);
                }
              }
            }
            fabricCanvas.renderAll();
          }

          // Update selected object in store
          const activeObject = fabricCanvas.getActiveObject();
          if (activeObject && !activeObject.isDesignArea && !activeObject.isDesignAreaElement) {
            setSelectedObject(activeObject);

            // Show AI toolbar for images
            if (activeObject.type === 'image') {
              const objectBounds = activeObject.getBoundingRect();
              setAIToolbarPosition({
                x: objectBounds.left + objectBounds.width / 2,
                y: objectBounds.top
              });
              setShowAIToolbar(true);
            } else {
              setShowAIToolbar(false);
            }
          } else {
            setShowAIToolbar(false);
          }
        });

        // Handle selection cleared
        fabricCanvas.on('selection:cleared', () => {
          setSelectedObject(null);
          setShowAIToolbar(false);
        });

        // Ensure design area elements always stay at the bottom
        fabricCanvas.on('object:added', () => {
          const designArea = fabricCanvas.getObjects().find(obj => obj.id === 'design-area');
          const cornerIndicators = fabricCanvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));

          if (designArea) {
            try {
              if (typeof fabricCanvas.sendObjectToBack === 'function') {
                fabricCanvas.sendObjectToBack(designArea);
              } else {
                fabricCanvas.moveTo(designArea, 0);
              }
            } catch (e) {
              console.warn("Could not send design area to back:", e);
            }
          }

          cornerIndicators.forEach(indicator => {
            try {
              if (typeof fabricCanvas.sendToBack === 'function') {
                fabricCanvas.sendToBack(indicator);
              } else if (typeof fabricCanvas.sendObjectToBack === 'function') {
                fabricCanvas.sendObjectToBack(indicator);
              } else {
                fabricCanvas.moveTo(indicator, 0);
              }
            } catch (e) {
              console.warn("Could not send corner indicator to back:", e);
            }
          });
        });



        // Set up basic event listeners for modification tracking
        const handleCanvasChange = () => {
          markAsModified();
        };

        // Only track basic modification events for save status
        // The new CanvasHistoryManager handles detailed undo/redo tracking automatically
        fabricCanvas.on("object:added", handleCanvasChange);
        fabricCanvas.on("object:modified", handleCanvasChange);
        fabricCanvas.on("object:removed", handleCanvasChange);
        fabricCanvas.on("path:created", handleCanvasChange);

      } catch (e) {
        console.error("Failed to init canvas", e);
      }
    };

    const timer = setTimeout(() => {
      initCanvas();
    }, 50);

    return () => {
      clearTimeout(timer);
      cleanUpCanvas();
    };
  }, []);

  return (
    <div
      className="relative w-full h-full overflow-hidden bg-gray-900"
      ref={canvasContainerRef}
      style={{ minHeight: '600px' }}
    >
      {/* Workspace Grid Background */}
      <div
        className="workspace-grid absolute inset-0 opacity-10 transition-opacity duration-300"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.2) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.2) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
      />

      {/* Workspace Center Guidelines */}
      <div
        className="workspace-guideline absolute opacity-10 transition-opacity duration-300"
        style={{
          top: '50%',
          left: '0',
          right: '0',
          height: '1px',
          backgroundColor: '#60a5fa',
          zIndex: 0
        }}
      />
      <div
        className="workspace-guideline absolute opacity-10 transition-opacity duration-300"
        style={{
          left: '50%',
          top: '0',
          bottom: '0',
          width: '1px',
          backgroundColor: '#60a5fa',
          zIndex: 0
        }}
      />

      {/* Full-screen Fabric.js Canvas */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0"
        style={{
          width: '100%',
          height: '100%',
          zIndex: 1
        }}
      />

      {/* Design Area Info Panel - Responsive */}
      <div
        className="absolute top-4 left-4 text-xs text-gray-400 bg-gray-800 px-3 py-2 rounded border border-gray-600 flex items-center gap-2 hidden sm:flex"
        style={{ zIndex: 10 }}
      >
        <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
        <div>
          <div>Export Area: {canvasWidth} × {canvasHeight} px</div>
          <div className="text-gray-500">Background: {canvasBackgroundColor}</div>
        </div>
      </div>

      {/* Export Instructions - Hidden on mobile */}
      <div
        className="absolute top-4 right-4 text-xs text-gray-400 bg-gray-800 px-3 py-2 rounded border border-gray-600 max-w-xs hidden lg:block"
        style={{ zIndex: 10 }}
      >
        <div className="flex items-center gap-2 mb-1">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          <span className="font-medium">Professional Workspace</span>
        </div>
        <div className="text-gray-500">
          • Move objects freely across entire canvas<br/>
          • Only blue-bordered area will be exported<br/>
          • Click Export to save design area as image
        </div>
      </div>

      {/* Pan Mode Indicator */}
      {isPanMode && (
        <div
          className="absolute top-20 left-4 text-xs text-yellow-400 bg-yellow-900 bg-opacity-80 px-3 py-2 rounded border border-yellow-600 flex items-center gap-2"
          style={{ zIndex: 10 }}
        >
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
          <div>
            <div className="font-medium">Pan Mode Active</div>
            <div className="text-yellow-300">Click and drag to pan the canvas</div>
          </div>
        </div>
      )}

      {/* Drawing Session Indicator */}
      {currentDrawingSession && (
        <div
          className="absolute top-20 right-4 text-xs text-blue-400 bg-blue-900 bg-opacity-80 px-3 py-2 rounded border border-blue-600 flex items-center gap-2"
          style={{ zIndex: 10 }}
        >
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          <div>
            <div className="font-medium">{currentDrawingSession.name}</div>
            <div className="text-blue-300">{currentDrawingSession.paths?.length || 0} paths drawn</div>
          </div>
        </div>
      )}

      {/* Zoom Controls - Responsive */}
      <div
        className="absolute bottom-4 left-4 flex flex-col gap-2 sm:bottom-6 sm:left-6"
        style={{ zIndex: 10 }}
      >
        <button
          onClick={handleZoomIn}
          className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded flex items-center justify-center text-gray-300 hover:text-white transition-colors"
          title="Zoom In"
        >
          <ZoomIn className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
        <button
          onClick={handleZoomOut}
          className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded flex items-center justify-center text-gray-300 hover:text-white transition-colors"
          title="Zoom Out"
        >
          <ZoomOut className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
        <button
          onClick={handleResetZoom}
          className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded flex items-center justify-center text-gray-300 hover:text-white transition-colors"
          title="Reset Zoom"
        >
          <RotateCcw className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
      </div>

      {/* Canvas Info Panel - Responsive */}
      <div
        className="absolute bottom-4 right-4 sm:bottom-6 sm:right-6 text-xs text-gray-400 bg-gray-800 px-3 py-2 rounded border border-gray-600"
        style={{ zIndex: 10 }}
      >
        <div className="hidden sm:block">
          Objects: {fabricCanvasRef.current?.getObjects()?.filter(obj =>
            !obj.isDesignArea &&
            !obj.isDesignAreaElement &&
            obj.id !== 'design-area' &&
            !obj.id?.startsWith('corner-indicator') &&
            !obj.excludeFromExport
          ).length || 0} | Zoom: {Math.round(zoomLevel * 100)}%
        </div>
        <div className="sm:hidden">
          {Math.round(zoomLevel * 100)}%
        </div>
      </div>

      {/* AI Image Toolbar - Contextual */}
      {showAIToolbar && selectedObject && selectedObject.type === 'image' && (
        <div
          className="absolute z-50"
          style={{
            left: aiToolbarPosition.x - 200, // Center the toolbar
            top: aiToolbarPosition.y - 10,
            transform: 'translateY(-100%)'
          }}
        >
          <AIImageToolbar
            selectedObject={selectedObject}
            onClose={() => setShowAIToolbar(false)}
          />
        </div>
      )}

    </div>
  );
}
