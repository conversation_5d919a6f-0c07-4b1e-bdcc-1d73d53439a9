"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { debounce } from "lodash";

import { useTemplateStore } from "@/store/templateStore";
import { useEditorStore } from "@/store";
import Header from "@/components/editor/Header";
import LeftSidebar from "@/components/editor/LeftSidebar";
import Canvas from "@/components/editor/Canvas";
import LayersPanel from "@/components/editor/panels/LayersPanel";
import PropertiesPanel from "@/components/editor/panels/PropertiesPanel";
import {
  Save,
  Crown,
  X,
  Settings,
  Layers
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function TemplateCreator({ templateId = null }) {
  const router = useRouter();
  const {
    templates,
    categories,
    loadTemplates
  } = useTemplateStore();

  const {
    canvas,
    resetStore,
    showLayers,
    showProperties,
    selectedObject,
    setShowLayers,
    setShowProperties,
    setName,
    setSaveStatus
  } = useEditorStore();

  const [templateData, setTemplateData] = useState({
    name: "",
    category: "business",
    description: "",
    isPublic: true
  });

  // Debug: Log templateData changes
  useEffect(() => {
    console.log('TemplateData changed:', templateData);
  }, [templateData]);

  const [editableZones, setEditableZones] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [showTemplateSettings, setShowTemplateSettings] = useState(true);

  // For demo purposes, using a mock user ID
  // In a real app, you'd get this from authentication
  const mockUserId = "demo_user";

  // Auto-save state (following client/server pattern)
  const [isModified, setIsModified] = useState(false);

  // Auto-save functions (following client/server pattern)
  const saveTemplateToServer = useCallback(async () => {
    if (!canvas || !isEditing || !templateId) {
      console.log("No canvas, not editing, or no template ID available");
      return null;
    }

    try {
      // Use the same canvas serialization approach as client/server
      const canvasData = canvas.toJSON(["id", "filters"]);

      // Filter out design area elements
      const templateObjects = canvasData.objects.filter(obj =>
        !obj.isDesignArea &&
        !obj.isDesignAreaElement &&
        obj.id !== 'design-area' &&
        !obj.id?.startsWith('corner-indicator')
      );

      // Create clean canvas data structure
      const cleanCanvasData = {
        ...canvasData,
        objects: templateObjects
      };

      const dbTemplateData = {
        name: templateData.name.trim() || 'Untitled Template',
        description: templateData.description?.trim() || '',
        category: templateData.category || 'general',
        width: canvas.width || 800,
        height: canvas.height || 600,
        canvasData: JSON.stringify(cleanCanvasData),
        thumbnail: null,
        isPublic: templateData.isPublic !== false,
        isPremium: false,
        tags: templateData.tags || [],
        editableZones: editableZones,
        createdById: mockUserId
      };

      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dbTemplateData),
      });

      const result = await response.json();

      if (result.success) {
        setSaveStatus("Saved");
        setIsModified(false);
        return result;
      } else {
        setSaveStatus("Error");
        console.error('Failed to auto-save template:', result.error);
        return null;
      }
    } catch (error) {
      setSaveStatus("Error");
      console.error('Error auto-saving template:', error);
      return null;
    }
  }, [canvas, isEditing, templateId, templateData, editableZones, mockUserId, setSaveStatus]);

  // Debounced auto-save (following client/server pattern)
  const debouncedSaveToServer = useCallback(
    debounce(() => {
      saveTemplateToServer();
    }, 500),
    [saveTemplateToServer]
  );

  // Mark as modified and trigger auto-save (following client/server pattern)
  const markAsModified = useCallback(() => {
    if (isEditing && templateId) {
      setSaveStatus("Saving...");
      setIsModified(true);
      debouncedSaveToServer();
    }
  }, [isEditing, templateId, debouncedSaveToServer, setSaveStatus]);

  useEffect(() => {
    // Load templates from database and reset the store on mount
    const initializeTemplates = async () => {
      try {
        await loadTemplates();
      } catch (error) {
        console.error('Failed to load templates:', error);
      }
    };

    initializeTemplates();
    resetStore();

    // Ensure right sidebar is visible for template creator
    setShowLayers(true);

    return () => {
      // Clean up on unmount
      resetStore();
    };
  }, [loadTemplates, resetStore, setShowLayers]);

  useEffect(() => {
    if (templateId) {
      loadTemplateFromDatabase(templateId);
    }
  }, [templateId, setName]);

  // Function to load template from database using proper API endpoint
  const loadTemplateFromDatabase = async (templateId) => {
    try {


      // Use the proper template API endpoint
      const response = await fetch(`/api/templates/${templateId}`);
      const result = await response.json();

      if (result.success && result.template) {
        const template = result.template;

        const newTemplateData = {
          name: template.name || 'Untitled Template',
          category: template.category || 'general',
          description: template.description || "",
          isPublic: template.isPublic !== false
        };
        setTemplateData(newTemplateData);
        setEditableZones(template.editableZones || []);
        setIsEditing(true);

        // Update editor store name to reflect template name
        setName(template.name || 'Untitled Template');

        // Load canvas data if available
        if (template.canvasData && canvas) {
          const templateForCanvas = {
            id: template.id,
            name: template.name,
            category: template.category || 'general',
            canvas: template.canvasData, // Use canvasData from database
            editableZones: template.editableZones || [],
            metadata: {
              description: template.description || "",
              isPublic: template.isPublic !== false,
              tags: template.tags || [],
              createdAt: template.createdAt,
              updatedAt: template.updatedAt,
              version: 1
            }
          };

          loadTemplateToCanvas(templateForCanvas);
        }
        return;
      } else {
        console.error('Template not found or API error:', result.error);
        alert('Template not found. It may have been deleted.');
      }
    } catch (error) {
      console.error('Error loading template from database:', error);
      alert('Error loading template. Please try again.');
    }
  };

  // Load template canvas data when canvas is ready
  useEffect(() => {
    if (canvas && templateId && isEditing) {
      const template = templates.find(t => t.id === templateId);
      if (template && template.canvasData) {
        // Convert database format to canvas format
        const templateForCanvas = {
          id: template.id,
          name: template.name,
          category: template.category || 'general',
          canvas: template.canvasData,
          editableZones: template.editableZones || [],
          metadata: {
            description: template.description || "",
            isPublic: template.isPublic !== false,
            tags: template.tags || [],
            createdAt: template.createdAt,
            updatedAt: template.updatedAt,
            version: 1
          }
        };
        loadTemplateToCanvas(templateForCanvas);
      }
    }
  }, [canvas, templateId, isEditing, templates]);

  // Add canvas event listeners for auto-save (following client/server pattern)
  useEffect(() => {
    if (!canvas || !isEditing || !templateId) return;

    const handleCanvasModified = () => {
      markAsModified();
    };

    // Add event listeners for canvas modifications
    canvas.on('object:added', handleCanvasModified);
    canvas.on('object:removed', handleCanvasModified);
    canvas.on('object:modified', handleCanvasModified);
    canvas.on('path:created', handleCanvasModified);

    return () => {
      // Clean up event listeners
      canvas.off('object:added', handleCanvasModified);
      canvas.off('object:removed', handleCanvasModified);
      canvas.off('object:modified', handleCanvasModified);
      canvas.off('path:created', handleCanvasModified);
    };
  }, [canvas, isEditing, templateId, markAsModified]);

  // Function to load template to canvas
  const loadTemplateToCanvas = async (template) => {
    if (!canvas || !template) return;

    try {
      // Clear all objects from canvas
      canvas.clear();

      // Load template objects if they exist
      if (template.canvas && template.canvas.objects && template.canvas.objects.length > 0) {
        const fabric = await import("fabric");

        // Load all template objects (no need to filter design area since we removed it)
        const templateObjects = template.canvas.objects;

        if (templateObjects.length > 0) {
          fabric.util.enlivenObjects(templateObjects, (objects) => {
            objects.forEach(obj => {
              canvas.add(obj);
            });

            canvas.renderAll();
          });
        } else {
          canvas.renderAll();
        }
      } else {
        canvas.renderAll();
      }

      // Design area removed - canvas is now clear for user content

    } catch (error) {
      console.error('Error loading template to canvas:', error);
    }
  };

  // Function to create design area
  const createDesignArea = async () => {
    if (!canvas) return;

    try {
      const { Rect } = await import("fabric");
      const { canvasWidth, canvasHeight, canvasBackgroundColor } = useEditorStore.getState();

      // Calculate center position for design area
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;

      // Create design area rectangle
      const designArea = new Rect({
        id: 'design-area',
        left: centerX - canvasWidth / 2,
        top: centerY - canvasHeight / 2,
        width: canvasWidth,
        height: canvasHeight,
        fill: canvasBackgroundColor,
        stroke: '#3b82f6',
        strokeWidth: 3,
        strokeDashArray: [10, 5],
        selectable: false,
        evented: false,
        excludeFromExport: true,
        hoverCursor: 'default',
        moveCursor: 'default',
        isDesignArea: true,
        visible: true,
        opacity: 1,
        shadow: {
          color: 'rgba(59, 130, 246, 0.3)',
          blur: 10,
          offsetX: 0,
          offsetY: 4
        }
      });

      // Add design area to canvas
      canvas.add(designArea);

      // Ensure design area stays at the very back
      try {
        canvas.sendToBack(designArea);
        // Force it to be at index 0 (bottom layer)
        canvas.moveTo && canvas.moveTo(designArea, 0);
      } catch (e) {
        console.warn("Could not move design area to back:", e);
      }

      // Add corner indicators
      await addCornerIndicators(canvas, centerX - canvasWidth / 2, centerY - canvasHeight / 2, canvasWidth, canvasHeight);

      canvas.renderAll();
    } catch (error) {
      console.error('Failed to create design area:', error);
    }
  };

  // Function to add corner indicators (copied from Canvas.jsx)
  const addCornerIndicators = async (canvas, left, top, width, height) => {
    if (!canvas) return;

    try {
      const { Circle } = await import("fabric");

      const cornerSize = 8;
      const cornerColor = '#3b82f6';

      const corners = [
        { x: left, y: top, id: 'corner-indicator-tl' },
        { x: left + width, y: top, id: 'corner-indicator-tr' },
        { x: left, y: top + height, id: 'corner-indicator-bl' },
        { x: left + width, y: top + height, id: 'corner-indicator-br' }
      ];

      corners.forEach(corner => {
        const indicator = new Circle({
          id: corner.id,
          left: corner.x - cornerSize / 2,
          top: corner.y - cornerSize / 2,
          radius: cornerSize / 2,
          fill: cornerColor,
          stroke: '#ffffff',
          strokeWidth: 2,
          selectable: false,
          evented: false,
          excludeFromExport: true,
          isDesignAreaElement: true,
          visible: true,
          opacity: 1,
          shadow: {
            color: 'rgba(59, 130, 246, 0.3)',
            blur: 4,
            offsetX: 0,
            offsetY: 2
          }
        });

        canvas.add(indicator);
      });
    } catch (error) {
      console.error('Failed to add corner indicators:', error);
    }
  };

  const handleSaveTemplate = async () => {
    if (!canvas) {
      alert("Canvas not ready. Please wait and try again.");
      return;
    }

    if (!templateData.name.trim()) {
      alert("Please enter a template name.");
      return;
    }



    try {
      // Use the same canvas serialization approach as client/server
      const canvasData = canvas.toJSON(["id", "filters"]);

      // Filter out design area elements from template (same as client approach)
      const templateObjects = canvasData.objects.filter(obj =>
        !obj.isDesignArea &&
        !obj.isDesignAreaElement &&
        obj.id !== 'design-area' &&
        !obj.id?.startsWith('corner-indicator')
      );

      console.log('Canvas objects before filtering:', canvasData.objects.length);
      console.log('Template objects after filtering:', templateObjects.length);
      console.log('Template objects:', templateObjects);

      // Create clean canvas data structure (matching client/server pattern)
      const cleanCanvasData = {
        ...canvasData,
        objects: templateObjects
      };

      // Prepare data for database save (following client/server pattern)
      const dbTemplateData = {
        name: templateData.name.trim(),
        description: templateData.description?.trim() || '',
        category: templateData.category || 'general',
        width: canvas.width || 800,
        height: canvas.height || 600,
        canvasData: JSON.stringify(cleanCanvasData), // Store as string like client/server
        thumbnail: null, // TODO: Generate thumbnail
        isPublic: templateData.isPublic !== false,
        isPremium: false,
        tags: templateData.tags || [],
        editableZones: editableZones,
        createdById: mockUserId // For demo purposes
      };

      if (isEditing && templateId) {
        // Update existing template in database
        try {
          const response = await fetch(`/api/templates/${templateId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(dbTemplateData),
          });

          const result = await response.json();

          if (result.success) {
            alert('Template updated successfully!');
            router.push('/admin');
          } else {
            alert(`Failed to update template: ${result.error}`);
          }
        } catch (error) {
          console.error('Error updating template:', error);
          alert('An error occurred while updating the template');
        }
      } else {
        // Create new template in database
        try {
          const response = await fetch('/api/templates', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(dbTemplateData),
          });

          const result = await response.json();

          if (result.success) {
            alert('Template created successfully!');
            router.push('/admin');
          } else {
            alert(`Failed to create template: ${result.error}`);
          }
        } catch (error) {
          console.error('Error creating template:', error);
          alert('An error occurred while creating the template');
        }
      }
    } catch (error) {
      console.error('Error saving template:', error);
      alert('Failed to save template. Please try again.');
    }
  };



  // Show template settings sidebar if either layers/properties should be shown OR template settings is enabled
  const shouldShowRightSidebar = showLayers || showProperties || showTemplateSettings;

  return (
    <div className="flex flex-col h-screen overflow-hidden bg-gray-900">
      {/* Header - Same as PosterEditor */}
      <Header />

      {/* Main Editor Layout - Same as PosterEditor */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Tool Selection - Same as PosterEditor */}
        <div className="hidden md:block">
          <LeftSidebar />
        </div>

        {/* Main Canvas Area - Same as PosterEditor */}
        <div className="flex-1 flex flex-col overflow-hidden bg-gray-800 min-w-0">
          <Canvas />
        </div>

        {/* Right Sidebar - Enhanced with Template Settings */}
        {shouldShowRightSidebar && (
          <div className="w-[320px] bg-white border-l h-full flex flex-col">
            <Tabs defaultValue={showTemplateSettings ? "template" : "layers"} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-3 m-2">
                <TabsTrigger value="layers" className="flex items-center gap-2">
                  <Layers className="w-4 h-4" />
                  Layers
                </TabsTrigger>
                <TabsTrigger
                  value="properties"
                  className="flex items-center gap-2"
                  disabled={!selectedObject}
                >
                  <Settings className="w-4 h-4" />
                  Properties
                </TabsTrigger>
                <TabsTrigger value="template" className="flex items-center gap-2">
                  <Crown className="w-4 h-4" />
                  Template
                </TabsTrigger>
              </TabsList>

              <TabsContent value="layers" className="flex-1 m-0">
                <LayersPanel />
              </TabsContent>

              <TabsContent value="properties" className="flex-1 m-0">
                {selectedObject ? (
                  <PropertiesPanel />
                ) : (
                  <div className="flex-1 flex items-center justify-center p-8">
                    <div className="text-center text-gray-500">
                      <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-sm">Select an object to view properties</p>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="template" className="flex-1 m-0">
                {/* Template Settings Panel */}
                <div className="flex flex-col h-full">
                  {/* Header */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Crown className="w-5 h-5 text-purple-600" />
                        <h2 className="text-lg font-semibold text-gray-900">
                          {isEditing ? 'Edit Template' : 'Create Template'}
                        </h2>
                      </div>
                      <button
                        onClick={() => router.push('/admin')}
                        className="p-1 text-gray-400 hover:text-gray-600 rounded"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    <p className="text-sm text-gray-500">
                      Configure template settings and save your design
                    </p>
                  </div>

                  {/* Template Settings Form */}
                  <div className="flex-1 p-4 overflow-y-auto">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Template Name
                        </label>
                        <input
                          type="text"
                          value={templateData.name}
                          onChange={(e) => setTemplateData({...templateData, name: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                          placeholder="Enter template name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Category
                        </label>
                        <select
                          value={templateData.category}
                          onChange={(e) => setTemplateData({...templateData, category: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                        >
                          {categories.map(category => (
                            <option key={category.id} value={category.id}>
                              {category.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          value={templateData.description}
                          onChange={(e) => setTemplateData({...templateData, description: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                          rows={4}
                          placeholder="Describe your template"
                        />
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="isPublic"
                          checked={templateData.isPublic}
                          onChange={(e) => setTemplateData({...templateData, isPublic: e.target.checked})}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label htmlFor="isPublic" className="ml-2 block text-sm text-gray-700">
                          Make template public
                        </label>
                      </div>

                      {/* Template Info */}
                      <div className="pt-4 border-t border-gray-200">
                        <h3 className="text-sm font-medium text-gray-700 mb-2">Template Info</h3>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex justify-between">
                            <span>Editable Zones:</span>
                            <span>{editableZones.length}</span>
                          </div>
                          {isEditing && (
                            <>
                              <div className="flex justify-between">
                                <span>Version:</span>
                                <span>v{templates.find(t => t.id === templateId)?.metadata?.version || 1}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Last Updated:</span>
                                <span>{new Date(templates.find(t => t.id === templateId)?.metadata?.updatedAt || Date.now()).toLocaleDateString()}</span>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Save Button */}
                  <div className="p-4 border-t border-gray-200">
                    <button
                      onClick={handleSaveTemplate}
                      className="w-full inline-flex items-center justify-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-sm font-medium"
                    >
                      <Save className="w-4 h-4" />
                      {isEditing ? 'Update Template' : 'Save Template'}
                    </button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
}
