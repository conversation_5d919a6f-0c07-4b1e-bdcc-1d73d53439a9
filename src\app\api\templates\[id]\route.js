import { NextResponse } from 'next/server';
import { getTemplateById, updateTemplate, deleteTemplateById } from '../../../../services/simpleDbService';

// GET /api/templates/[id] - Get a specific template
export async function GET(request, { params }) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const result = await getTemplateById(id);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Template not found' ? 404 : 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/templates/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/templates/[id] - Update a specific template
export async function PUT(request, { params }) {
  try {
    const { id } = await params;
    const body = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Validate required fields for update
    if (!body.name || !body.canvasData) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: name and canvasData are required' 
        },
        { status: 400 }
      );
    }

    const templateData = {
      name: body.name,
      description: body.description || null,
      category: body.category || 'general',
      width: body.width || 800,
      height: body.height || 600,
      canvasData: body.canvasData,
      thumbnail: body.thumbnail || null,
      isPublic: body.isPublic !== undefined ? body.isPublic : true,
      isPremium: body.isPremium !== undefined ? body.isPremium : false,
      tags: body.tags || [],
      editableZones: body.editableZones || null,
    };

    const result = await updateTemplate(id, templateData);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Template not found' ? 404 : 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in PUT /api/templates/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/templates/[id] - Delete a specific template
export async function DELETE(request, { params }) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const result = await deleteTemplateById(id);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Template not found' ? 404 : 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in DELETE /api/templates/[id]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
