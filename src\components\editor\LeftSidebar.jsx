"use client";

import { useEditorStore } from "@/store";
import {
  MousePointer2,
  FileText,
  Type,
  Shapes,
  Upload,
  Pencil,
  Eraser,
  Settings,
  Move,
  Wand2
} from "lucide-react";
import TemplatesPanel from "./panels/TemplatesPanel";
import TextPanel from "./panels/TextPanel";
import ShapesPanel from "./panels/ShapesPanel";
import DrawingPanel from "./panels/DrawingPanel";
import ErasePanel from "./panels/ErasePanel";
import EnhancedImagesPanel from "./panels/EnhancedImagesPanel";
import SettingsPanel from "./panels/SettingsPanel";
import AiPanel from "./panels/AiPanel";

const tools = [
  {
    id: 'select',
    icon: MousePointer2,
    label: 'Select',
    panel: null,
  },
  {
    id: 'pan',
    icon: Move,
    label: 'Pan',
    panel: null,
  },
  {
    id: 'templates',
    icon: FileText,
    label: 'Design',
    panel: TemplatesPanel,
  },
  {
    id: 'shapes',
    icon: Shapes,
    label: 'Elements',
    panel: ShapesPanel,
  },
  {
    id: 'text',
    icon: Type,
    label: 'Text',
    panel: TextPanel,
  },
  {
    id: 'uploads',
    icon: Upload,
    label: 'Images',
    panel: EnhancedImagesPanel,
  },
  {
    id: 'draw',
    icon: Pencil,
    label: 'Draw',
    panel: DrawingPanel,
  },
  {
    id: 'erase',
    icon: Eraser,
    label: 'Erase',
    panel: ErasePanel,
  },
  {
    id: 'ai',
    icon: Wand2,
    label: 'AI Tools',
    panel: AiPanel,
  },
  {
    id: 'settings',
    icon: Settings,
    label: 'Settings',
    panel: SettingsPanel,
  },
];

export default function LeftSidebar() {
  const {
    activeTool,
    setActivePanel,
    showTemplatesPanel,
    showTextPanel,
    showShapesPanel,
    showDrawingPanel,
    showErasePanel,
    showUploadPanel,
    showSettingsPanel,
    showAiPanel,
  } = useEditorStore();

  const handleToolClick = (toolId) => {
    if (toolId === 'select') {
      setActivePanel('select');
    } else if (toolId === 'pan') {
      setActivePanel('pan');
    } else {
      setActivePanel(toolId);
    }
  };

  const getActivePanel = () => {
    if (showTemplatesPanel) return TemplatesPanel;
    if (showTextPanel) return TextPanel;
    if (showShapesPanel) return ShapesPanel;
    if (showDrawingPanel) return DrawingPanel;
    if (showErasePanel) return ErasePanel;
    if (showUploadPanel) return EnhancedImagesPanel;
    if (showAiPanel) return AiPanel;
    if (showSettingsPanel) return SettingsPanel;
    return null;
  };

  const ActivePanelComponent = getActivePanel();

  return (
    <div className="flex h-full">
      {/* Tool Selection Sidebar */}
      <aside className="w-[100px] bg-white border-r flex flex-col items-center py-4">
        {tools.map((tool) => {
          const Icon = tool.icon;
          const isActive = activeTool === tool.id;
          
          return (
            <button
              key={tool.id}
              onClick={() => handleToolClick(tool.id)}
              className={`
                w-full flex flex-col items-center justify-center py-4 px-3 cursor-pointer 
                hover:bg-gray-100 transition-colors rounded-none
                ${isActive ? 'bg-gray-100 text-blue-600' : 'text-gray-700'}
              `}
            >
              <Icon className="w-6 h-6 mb-2" />
              <span className="text-xs font-medium">{tool.label}</span>
            </button>
          );
        })}
      </aside>

      {/* Tool Panel */}
      {ActivePanelComponent && (
        <div className="w-[360px] bg-white border-r h-full flex flex-col">
          <ActivePanelComponent />
        </div>
      )}
    </div>
  );
}
