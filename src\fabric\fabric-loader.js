/**
 * Robust Fabric.js loader with retry mechanism for chunk loading issues
 */

let fabricModule = null;
let fabricLoadPromise = null;

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const loadFabricWithRetry = async (retryCount = 0) => {
  try {
    // Try to load fabric module
    const fabric = await import("fabric");
    fabricModule = fabric;
    return fabric;
  } catch (error) {
    console.warn(`Fabric.js load attempt ${retryCount + 1} failed:`, error);
    
    if (retryCount < MAX_RETRIES) {
      // Wait before retrying
      await delay(RETRY_DELAY * (retryCount + 1));
      return loadFabricWithRetry(retryCount + 1);
    } else {
      // All retries failed
      throw new Error(`Failed to load Fabric.js after ${MAX_RETRIES} attempts: ${error.message}`);
    }
  }
};

/**
 * Get Fabric.js module with caching and retry logic
 */
export const getFabric = async () => {
  // Return cached module if available
  if (fabricModule) {
    return fabricModule;
  }

  // Return existing promise if loading is in progress
  if (fabricLoadPromise) {
    return fabricLoadPromise;
  }

  // Start loading fabric
  fabricLoadPromise = loadFabricWithRetry();
  
  try {
    const fabric = await fabricLoadPromise;
    fabricLoadPromise = null;
    return fabric;
  } catch (error) {
    fabricLoadPromise = null;
    throw error;
  }
};

/**
 * Reset fabric loader (useful for testing or error recovery)
 */
export const resetFabricLoader = () => {
  fabricModule = null;
  fabricLoadPromise = null;
};

/**
 * Check if Fabric.js is already loaded
 */
export const isFabricLoaded = () => {
  return fabricModule !== null;
};
