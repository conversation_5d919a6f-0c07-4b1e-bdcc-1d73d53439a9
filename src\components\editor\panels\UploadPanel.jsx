"use client";

import { useState } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { addImageToCanvas } from "@/fabric/fabric-utils";
import { X, Upload, Link, Image as ImageIcon, FileImage } from "lucide-react";

export default function UploadPanel() {
  const { setActivePanel, canvas, markAsModified } = useEditorStore();
  const [imageUrl, setImageUrl] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const handleClose = () => {
    setActivePanel('select');
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files?.[0];
    if (!file || !canvas) return;

    setIsUploading(true);
    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const dataUrl = e.target?.result;
        if (dataUrl) {
          await addImageToCanvas(canvas, dataUrl);
          markAsModified();
        }
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error("Error uploading image:", error);
      alert("Failed to upload image. Please try again.");
      setIsUploading(false);
    }
  };

  const addImageFromUrl = async () => {
    if (!canvas || !imageUrl.trim()) return;
    
    setIsUploading(true);
    try {
      await addImageToCanvas(canvas, imageUrl.trim());
      markAsModified();
      setImageUrl('');
    } catch (error) {
      console.error("Error adding image:", error);
      alert("Failed to load image. Please check the URL and try again.");
    }
    setIsUploading(false);
  };

  const sampleImages = [
    {
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      title: 'Mountain Landscape'
    },
    {
      url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop',
      title: 'Forest Path'
    },
    {
      url: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop',
      title: 'Lake View'
    },
    {
      url: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=300&fit=crop',
      title: 'City Skyline'
    },
  ];

  const addSampleImage = async (imageUrl) => {
    if (!canvas) return;
    
    setIsUploading(true);
    try {
      await addImageToCanvas(canvas, imageUrl);
      markAsModified();
    } catch (error) {
      console.error("Error adding sample image:", error);
      alert("Failed to load sample image.");
    }
    setIsUploading(false);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Uploads</h3>
          <p className="text-xs text-gray-500">Upload images to your design</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* File Upload */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Upload from Device</Label>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
            <input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
              id="image-upload"
              disabled={isUploading}
            />
            <label
              htmlFor="image-upload"
              className={`cursor-pointer flex flex-col items-center ${isUploading ? 'opacity-50' : ''}`}
            >
              {isUploading ? (
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
              ) : (
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
              )}
              <span className="text-sm text-gray-600 mb-1">
                {isUploading ? 'Uploading...' : 'Click to upload image'}
              </span>
              <span className="text-xs text-gray-500">
                PNG, JPG, GIF up to 10MB
              </span>
            </label>
          </div>
        </div>

        {/* URL Upload */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Add from URL</Label>
          
          <div className="space-y-2">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Paste image URL here"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  className="pl-10"
                  disabled={isUploading}
                />
              </div>
              <Button
                onClick={addImageFromUrl}
                disabled={!imageUrl.trim() || isUploading}
                size="sm"
              >
                Add
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              Make sure the URL points directly to an image file
            </p>
          </div>
        </div>

        {/* Sample Images */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Sample Images</Label>
          
          <div className="grid grid-cols-2 gap-3">
            {sampleImages.map((image, index) => (
              <button
                key={index}
                onClick={() => addSampleImage(image.url)}
                disabled={isUploading}
                className="group relative bg-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-shadow disabled:opacity-50"
              >
                <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <ImageIcon className="w-8 h-8 text-gray-400" />
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                  <span className="text-white text-sm opacity-0 group-hover:opacity-100 transition-opacity">
                    Add to canvas
                  </span>
                </div>
                <div className="p-2">
                  <p className="text-xs font-medium text-gray-700 truncate">
                    {image.title}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Supported Formats */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Supported Formats</Label>
          
          <div className="grid grid-cols-4 gap-2">
            {['JPG', 'PNG', 'GIF', 'SVG'].map((format) => (
              <div
                key={format}
                className="flex items-center justify-center p-2 bg-gray-100 rounded-md"
              >
                <FileImage className="w-4 h-4 text-gray-500 mr-1" />
                <span className="text-xs text-gray-600">{format}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Tips */}
        <div className="bg-blue-50 rounded-lg p-3">
          <h5 className="text-sm font-medium text-blue-900 mb-1">Tips</h5>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Use high-resolution images for better quality</li>
            <li>• Images will be automatically resized to fit</li>
            <li>• For best results, use images with transparent backgrounds</li>
            <li>• Large files may take longer to upload</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
