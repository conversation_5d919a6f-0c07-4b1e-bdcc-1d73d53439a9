import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Test if we can import Prisma
    let prismaImportError = null;
    let prisma = null;
    
    try {
      const { PrismaClient } = await import('@prisma/client');
      prisma = new PrismaClient();
    } catch (error) {
      prismaImportError = error.message;
    }

    if (prismaImportError) {
      return NextResponse.json({
        success: false,
        error: 'Prisma import failed',
        details: prismaImportError,
        suggestion: 'Try using @prisma/client instead'
      });
    }

    // Test basic database connection
    try {
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      return NextResponse.json({
        success: true,
        message: 'Database connection successful',
        prismaWorking: true,
        testQuery: result
      });
    } catch (dbError) {
      return NextResponse.json({
        success: false,
        error: 'Database connection failed',
        details: dbError.message,
        prismaImported: true
      });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'General error',
      details: error.message,
      stack: error.stack
    });
  }
}
