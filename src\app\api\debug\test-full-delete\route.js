import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { templateId } = await request.json();
    
    if (!templateId) {
      return NextResponse.json({
        success: false,
        error: 'Template ID is required'
      }, { status: 400 });
    }

    // Try to import Prisma client
    const { PrismaClient } = await import('../../../../generated/prisma');
    const prisma = new PrismaClient();
    
    // Check what exists before deletion
    const templatesBefore = await prisma.template.count();
    const designsBefore = await prisma.design.count();
    
    // Check if the specific item exists in templates
    const templateExists = await prisma.template.findUnique({
      where: { id: templateId }
    });
    
    // Check if the specific item exists in designs
    const designExists = await prisma.design.findUnique({
      where: { id: templateId }
    });

    let deletionResult = null;
    let deletedFrom = null;

    // Try to delete from templates first
    if (templateExists) {
      try {
        await prisma.template.delete({
          where: { id: templateId }
        });
        deletionResult = 'success';
        deletedFrom = 'templates';
      } catch (error) {
        deletionResult = `error: ${error.message}`;
        deletedFrom = 'templates';
      }
    }
    // Try to delete from designs if not found in templates
    else if (designExists) {
      try {
        await prisma.design.delete({
          where: { id: templateId }
        });
        deletionResult = 'success';
        deletedFrom = 'designs';
      } catch (error) {
        deletionResult = `error: ${error.message}`;
        deletedFrom = 'designs';
      }
    }

    // Check what exists after deletion
    const templatesAfter = await prisma.template.count();
    const designsAfter = await prisma.design.count();

    // Check if the specific item still exists
    const templateExistsAfter = await prisma.template.findUnique({
      where: { id: templateId }
    });
    
    const designExistsAfter = await prisma.design.findUnique({
      where: { id: templateId }
    });

    return NextResponse.json({
      success: true,
      templateId,
      before: {
        templates: templatesBefore,
        designs: designsBefore,
        templateExists: !!templateExists,
        designExists: !!designExists
      },
      deletion: {
        result: deletionResult,
        deletedFrom: deletedFrom
      },
      after: {
        templates: templatesAfter,
        designs: designsAfter,
        templateExists: !!templateExistsAfter,
        designExists: !!designExistsAfter
      }
    });
  } catch (error) {
    console.error('Database full delete test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
