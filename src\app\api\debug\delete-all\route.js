import { NextResponse } from 'next/server';
import { Pool } from 'pg';

// Create a singleton connection pool
let pool;

function getPool() {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
  }
  return pool;
}

export async function POST() {
  try {
    const client = await getPool().connect();
    
    try {
      // Delete all templates
      const templatesResult = await client.query('DELETE FROM templates');
      
      // Delete all designs
      const designsResult = await client.query('DELETE FROM designs');
      
      return NextResponse.json({
        success: true,
        message: 'All templates and designs deleted successfully',
        deleted: {
          templates: templatesResult.rowCount,
          designs: designsResult.rowCount
        }
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error deleting all templates:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to delete all templates',
    warning: 'This will permanently delete ALL templates and designs!'
  });
}
