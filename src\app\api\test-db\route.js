import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Try to import Prisma client
    const { PrismaClient } = await import('../../../generated/prisma');
    const prisma = new PrismaClient();
    
    // Test database connection
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      result
    });
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
