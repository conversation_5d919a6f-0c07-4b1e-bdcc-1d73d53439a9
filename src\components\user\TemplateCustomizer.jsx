"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTemplateStore } from "@/store/templateStore";
import { useEditorStore } from "@/store";
import { 
  ArrowLeft, 
  Save, 
  Download, 
  User, 
  Edit,
  Type,
  Image as ImageIcon,
  Square,
  Lock,
  Unlock,
  Eye,
  EyeOff
} from "lucide-react";

export default function TemplateCustomizer({ templateId }) {
  const router = useRouter();
  const { templates, selectedTemplate, setSelectedTemplate } = useTemplateStore();
  const { canvas } = useEditorStore();
  
  const [template, setTemplate] = useState(null);
  const [projectName, setProjectName] = useState("");
  const [editableZones, setEditableZones] = useState([]);
  const [selectedZone, setSelectedZone] = useState(null);
  const [showZoneOverlays, setShowZoneOverlays] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (templateId && templates.length > 0) {
      const foundTemplate = templates.find(t => t.id === templateId);
      if (foundTemplate) {
        setTemplate(foundTemplate);
        setSelectedTemplate(foundTemplate);
        setEditableZones(foundTemplate.editableZones || []);
        setProjectName(`My ${foundTemplate.name}`);
        loadTemplateToCanvas(foundTemplate);
      }
    }
    setIsLoading(false);
  }, [templateId, templates, setSelectedTemplate]);

  const loadTemplateToCanvas = async (template) => {
    if (!canvas || !template) return;

    try {
      console.log('Loading template to canvas in TemplateCustomizer:', template.name);

      // Store current design area and corner indicators
      const existingDesignArea = canvas.getObjects().find(obj => obj.id === 'design-area');
      const existingCornerIndicators = canvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));

      // Clear existing objects (except design area and corner indicators)
      const userObjects = canvas.getObjects().filter(obj =>
        !obj.isDesignArea &&
        !obj.isDesignAreaElement &&
        obj.id !== 'design-area' &&
        !obj.id?.startsWith('corner-indicator')
      );

      userObjects.forEach(obj => canvas.remove(obj));

      // Load template objects
      if (template.canvas && template.canvas.objects) {
        const fabric = await import("fabric");

        // Filter out any design area objects from template data
        const templateObjects = template.canvas.objects.filter(obj =>
          !obj.isDesignArea &&
          !obj.isDesignAreaElement &&
          obj.id !== 'design-area' &&
          !obj.id?.startsWith('corner-indicator')
        );

        if (templateObjects.length > 0) {
          fabric.util.enlivenObjects(templateObjects, (objects) => {
            objects.forEach(obj => {
              canvas.add(obj);

              // Check if this object is in an editable zone
              const zone = template.editableZones?.find(z => z.objectId === obj.id);
              if (!zone) {
                // Make non-editable objects non-selectable
                obj.selectable = false;
                obj.evented = false;
                obj.lockMovementX = true;
                obj.lockMovementY = true;
                obj.lockRotation = true;
                obj.lockScalingX = true;
                obj.lockScalingY = true;
                obj.hasControls = false;
                obj.hasBorders = false;
              } else {
                // Apply zone constraints to editable objects
                if (!zone.constraints.resizable) {
                  obj.lockScalingX = true;
                  obj.lockScalingY = true;
                }
                if (!zone.constraints.movable) {
                  obj.lockMovementX = true;
                  obj.lockMovementY = true;
                }
                if (!zone.constraints.rotatable) {
                  obj.lockRotation = true;
                }
              }
            });

            // Ensure design area stays at the back
            if (existingDesignArea) {
              canvas.moveTo(existingDesignArea, 0);
            }

            // Ensure corner indicators stay after design area
            existingCornerIndicators.forEach((indicator, index) => {
              try {
                canvas.moveTo(indicator, index + 1);
              } catch (e) {
                console.warn("Could not move corner indicator:", e);
              }
            });

            canvas.renderAll();
            console.log('Template objects loaded successfully in TemplateCustomizer');
          });
        } else {
          console.log('No template objects to load');
          canvas.renderAll();
        }
      }

      // Update canvas background
      if (template.canvas && template.canvas.backgroundColor) {
        const designArea = canvas.getObjects().find(obj => obj.id === 'design-area');
        if (designArea) {
          designArea.set('fill', template.canvas.backgroundColor);
          canvas.renderAll();
        }
      }

      // Ensure design area exists
      const hasDesignArea = canvas.getObjects().some(obj => obj.id === 'design-area');
      if (!hasDesignArea) {
        console.log('No design area found after template load, this might cause display issues');
      }

    } catch (error) {
      console.error('Error loading template to canvas:', error);
    }
  };

  const handleSaveProject = () => {
    if (!canvas) {
      alert("Canvas not ready. Please wait and try again.");
      return;
    }

    try {
      const canvasData = canvas.toJSON();
      const projectData = {
        id: `project-${Date.now()}`,
        name: projectName,
        templateId: templateId,
        canvasData: canvasData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Save to localStorage (in a real app, this would be saved to a backend)
      const existingProjects = JSON.parse(localStorage.getItem('user-projects') || '[]');
      existingProjects.push(projectData);
      localStorage.setItem('user-projects', JSON.stringify(existingProjects));

      alert('Project saved successfully!');
    } catch (error) {
      console.error('Error saving project:', error);
      alert('Failed to save project. Please try again.');
    }
  };

  const handleExportProject = () => {
    if (!canvas) {
      alert("Canvas not ready. Please wait and try again.");
      return;
    }

    try {
      // Export the design area as image
      const { exportDesignArea } = require("@/fabric/fabric-utils");
      const dataURL = exportDesignArea(canvas);
      
      if (dataURL) {
        // Create download link
        const link = document.createElement('a');
        link.download = `${projectName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.png`;
        link.href = dataURL;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        alert('Failed to export image. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting project:', error);
      alert('Failed to export project. Please try again.');
    }
  };

  const handleBackToGallery = () => {
    router.push('/templates');
  };

  const handleZoneSelect = (zone) => {
    setSelectedZone(zone);
    
    if (canvas) {
      // Find and select the object associated with this zone
      const obj = canvas.getObjects().find(o => o.id === zone.objectId);
      if (obj) {
        canvas.setActiveObject(obj);
        canvas.renderAll();
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Template...</h2>
          <p className="text-gray-600">Preparing customization interface...</p>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Template Not Found</h2>
          <p className="text-gray-600 mb-4">The requested template could not be loaded.</p>
          <button
            onClick={handleBackToGallery}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Gallery
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBackToGallery}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">Customize Template</h1>
                  <p className="text-sm text-gray-500">{template.name}</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowZoneOverlays(!showZoneOverlays)}
                className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                  showZoneOverlays 
                    ? "bg-blue-100 text-blue-700" 
                    : "bg-gray-100 text-gray-600"
                }`}
              >
                {showZoneOverlays ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                Zones
              </button>
              
              <button
                onClick={handleSaveProject}
                className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Save className="w-4 h-4" />
                Save Project
              </button>
              
              <button
                onClick={handleExportProject}
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                Export
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Customization Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Project Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Settings</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project Name
                  </label>
                  <input
                    type="text"
                    value={projectName}
                    onChange={(e) => setProjectName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter project name"
                  />
                </div>

                <div className="text-sm text-gray-600">
                  <p><strong>Template:</strong> {template.name}</p>
                  <p><strong>Category:</strong> {template.category}</p>
                  <p><strong>Editable Areas:</strong> {editableZones.length}</p>
                </div>
              </div>
            </div>

            {/* Editable Zones */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Editable Areas</h3>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {editableZones.map((zone, index) => (
                  <div 
                    key={zone.id} 
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedZone?.id === zone.id 
                        ? "bg-blue-100 border border-blue-300" 
                        : "bg-gray-50 hover:bg-gray-100"
                    }`}
                    onClick={() => handleZoneSelect(zone)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {zone.type === 'text' && <Type className="w-4 h-4 text-blue-600" />}
                        {zone.type === 'image' && <ImageIcon className="w-4 h-4 text-green-600" />}
                        {zone.type === 'shape' && <Square className="w-4 h-4 text-purple-600" />}
                        <span className="text-sm font-medium text-gray-700">
                          {zone.type} Area {index + 1}
                        </span>
                      </div>
                      <Edit className="w-4 h-4 text-gray-400" />
                    </div>
                    
                    {zone.defaultContent && (
                      <p className="text-xs text-gray-500 mt-1 truncate">
                        {zone.defaultContent}
                      </p>
                    )}
                  </div>
                ))}
                
                {editableZones.length === 0 && (
                  <p className="text-sm text-gray-500 text-center py-4">
                    No editable areas in this template
                  </p>
                )}
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-blue-900 mb-2">How to customize:</h4>
              <ul className="text-xs text-blue-800 space-y-1">
                <li>• Click on highlighted areas to edit</li>
                <li>• Double-click text to edit content</li>
                <li>• Use the properties panel for styling</li>
                <li>• Upload images to replace placeholders</li>
                <li>• Save your project when done</li>
              </ul>
            </div>
          </div>

          {/* Canvas Area */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Canvas</h3>
                <div className="text-sm text-gray-500">
                  Click on blue highlighted areas to customize
                </div>
              </div>
              
              {/* Canvas will be rendered here by the main editor */}
              <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Canvas will be integrated here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
