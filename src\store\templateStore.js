"use client";

import { create } from "zustand";
import { v4 as uuidv4 } from "uuid";

export const useTemplateStore = create((set, get) => ({
  // Template state
  templates: [],
  currentTemplate: null,
  selectedTemplate: null,
  isTemplateMode: false,
  loading: false,
  error: null,
  
  // Template categories
  categories: [
    { id: 'business', name: 'Business', description: 'Professional business templates' },
    { id: 'events', name: 'Events', description: 'Event and party templates' },
    { id: 'marketing', name: 'Marketing', description: 'Marketing and promotional templates' },
    { id: 'social', name: 'Social Media', description: 'Social media post templates' },
    { id: 'education', name: 'Education', description: 'Educational templates' },
    { id: 'personal', name: 'Personal', description: 'Personal and creative templates' }
  ],

  // User role state
  userRole: null, // 'admin' or 'user'
  setUserRole: (role) => set({ userRole: role }),

  // Template management actions
  setTemplates: (templates) => set({ templates }),
  setCurrentTemplate: (template) => set({ currentTemplate: template }),
  setSelectedTemplate: (template) => set({ selectedTemplate: template }),
  setIsTemplateMode: (isTemplateMode) => set({ isTemplateMode }),

  // Load templates from database
  loadTemplates: async () => {
    if (typeof window === 'undefined') return [];

    try {
      set({ loading: true, error: null });
      const response = await fetch('/api/templates?type=public');
      const result = await response.json();

      if (result.success) {
        // Store templates in their original database format
        set({ templates: result.templates, loading: false });
        return result.templates;
      } else {
        console.error('Failed to load templates:', result.error);
        set({ templates: [], loading: false, error: result.error });
        return [];
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      set({ templates: [], loading: false, error: error.message });
      return [];
    }
  },



  // Create new template - save to database
  createTemplate: async (templateData) => {
    try {
      const dbTemplateData = {
        name: templateData.name || 'Untitled Template',
        description: templateData.description || '',
        category: templateData.category || 'business',
        width: templateData.canvas?.width || 800,
        height: templateData.canvas?.height || 600,
        canvasData: templateData.canvas || {
          width: 800,
          height: 600,
          backgroundColor: '#ffffff',
          objects: []
        },
        thumbnail: templateData.thumbnail || null,
        isPublic: templateData.isPublic !== false,
        isPremium: false,
        tags: templateData.tags || [],
        editableZones: templateData.editableZones || null,
        createdById: 'demo_user' // For demo purposes
      };

      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dbTemplateData),
      });

      const result = await response.json();

      if (result.success) {
        // Add to local state using database format
        const { templates } = get();
        const updatedTemplates = [...templates, result.template];
        set({ templates: updatedTemplates });

        return result.template;
      } else {
        console.error('Failed to create template:', result.error);
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error creating template:', error);
      throw error;
    }
  },

  // Update existing template - save to database
  updateTemplate: async (templateId, templateData) => {
    try {
      const dbTemplateData = {
        name: templateData.name,
        description: templateData.metadata?.description || templateData.description || '',
        category: templateData.category,
        width: templateData.canvas?.width || 800,
        height: templateData.canvas?.height || 600,
        canvasData: templateData.canvas,
        thumbnail: templateData.thumbnail || null,
        isPublic: templateData.metadata?.isPublic !== false,
        isPremium: false,
        tags: templateData.metadata?.tags || [],
        editableZones: templateData.editableZones || null
      };

      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dbTemplateData),
      });

      const result = await response.json();

      if (result.success) {
        // Update local state with database format
        const { templates } = get();
        const updatedTemplates = templates.map(template =>
          template.id === templateId ? result.template : template
        );

        set({ templates: updatedTemplates });
        return result.template;
      } else {
        console.error('Failed to update template:', result.error);
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error updating template:', error);
      throw error;
    }
  },

  // Delete template - remove from database
  deleteTemplate: async (templateId) => {
    try {
      console.log('Template store: Deleting template with ID:', templateId);

      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'DELETE',
      });

      console.log('Delete API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Delete API error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('Delete API result:', result);

      if (result.success) {
        // Update local state
        const { templates } = get();
        const updatedTemplates = templates.filter(template => template.id !== templateId);
        console.log('Updating local templates, before:', templates.length, 'after:', updatedTemplates.length);
        set({ templates: updatedTemplates });
        return true;
      } else {
        console.error('Failed to delete template:', result.error);
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      throw error;
    }
  },

  // Duplicate template
  duplicateTemplate: (templateId) => {
    const { templates } = get();
    const originalTemplate = templates.find(t => t.id === templateId);
    
    if (originalTemplate) {
      const duplicatedTemplate = {
        ...originalTemplate,
        id: uuidv4(),
        name: `${originalTemplate.name} (Copy)`,
        metadata: {
          ...originalTemplate.metadata,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          version: 1
        }
      };

      const updatedTemplates = [...templates, duplicatedTemplate];
      set({ templates: updatedTemplates });
      get().saveTemplates();
      
      return duplicatedTemplate;
    }
    
    return null;
  },

  // Get templates by category
  getTemplatesByCategory: (categoryId) => {
    const { templates } = get();
    return templates.filter(template => template.category === categoryId);
  },

  // Get public templates (for users)
  getPublicTemplates: () => {
    const { templates } = get();
    return templates.filter(template => template.isPublic);
  },

  // Search templates
  searchTemplates: (query) => {
    const { templates } = get();
    const lowercaseQuery = query.toLowerCase();
    
    return templates.filter(template =>
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description?.toLowerCase().includes(lowercaseQuery) ||
      template.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  },

  // No longer needed - templates are loaded from database

  // Reset store
  resetTemplateStore: () => {
    set({
      templates: [],
      currentTemplate: null,
      selectedTemplate: null,
      isTemplateMode: false,
      userRole: null,
      loading: false,
      error: null
    });
  }
}));
