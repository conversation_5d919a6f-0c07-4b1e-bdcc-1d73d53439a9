# Next.js Poster Editor

A powerful poster editor built with Next.js and Fabric.js, combining the best features from both the client Fabric.js editor and the Vue poster app layout.

## Features

### 🎨 Design Tools
- **Left Sidebar**: Tool selection with templates, images, text, shapes, and uploads
- **Canvas**: Interactive Fabric.js canvas with zoom, pan, and editing controls
- **Right Sidebar**: Layers panel and properties panel for detailed object editing

### 🛠️ Editing Capabilities
- **Shapes**: Rectangle, square, circle, triangle, ellipse, line, star, arrow, polygons, heart
- **Text**: Multiple font families, sizes, styles, and text effects
- **Images**: Upload from file or URL with automatic resizing
- **Templates**: Blank templates and predefined designs
- **Layers**: Full layer management with visibility, locking, and reordering

### 💾 Data Management
- **Auto-save**: Automatic saving to localStorage
- **Export**: PNG export functionality
- **State Management**: Zustand for efficient state management

## Project Structure

```
nextjs-poster-editor/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── globals.css         # Global styles
│   │   ├── layout.js           # Root layout
│   │   └── page.js             # Home page
│   ├── components/
│   │   ├── editor/             # Main editor components
│   │   │   ├── PosterEditor.jsx    # Main editor layout
│   │   │   ├── Header.jsx          # Top header with controls
│   │   │   ├── LeftSidebar.jsx     # Tool selection sidebar
│   │   │   ├── Canvas.jsx          # Fabric.js canvas component
│   │   │   ├── RightSidebar.jsx    # Layers and properties
│   │   │   └── panels/             # Individual tool panels
│   │   │       ├── TemplatesPanel.jsx
│   │   │       ├── TextPanel.jsx
│   │   │       ├── ShapesPanel.jsx
│   │   │       ├── ImagesPanel.jsx
│   │   │       ├── LayersPanel.jsx
│   │   │       └── PropertiesPanel.jsx
│   │   └── ui/                 # Reusable UI components
│   ├── fabric/                 # Fabric.js utilities
│   │   ├── fabric-utils.js     # Core Fabric.js functions
│   │   └── shapes/             # Shape definitions and factory
│   ├── store/                  # Zustand store
│   └── lib/                    # Utility functions
```

## Installation

1. **Install dependencies**:
   ```bash
   cd nextjs-poster-editor
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open in browser**:
   Navigate to `http://localhost:3000`

## Usage

### Getting Started
1. The editor opens with a blank canvas
2. Use the left sidebar to select tools:
   - **Select**: Default selection tool
   - **Design**: Templates and blank canvas
   - **Image**: Upload or add images from URL
   - **Text**: Add various text styles
   - **Shapes**: Add geometric shapes
   - **Upload**: File upload functionality

### Working with Objects
1. **Adding Objects**: Click any tool in the left sidebar and select an option
2. **Selecting Objects**: Click on any object on the canvas
3. **Editing Properties**: Select an object and use the Properties tab in the right sidebar
4. **Managing Layers**: Use the Layers tab to control visibility, locking, and order

### Canvas Controls
- **Zoom**: Use zoom in/out buttons in the canvas toolbar
- **Reset**: Reset zoom and pan to default
- **Delete**: Delete selected objects

## New Features

### 🤖 AI-Powered Image Tools
- **Background Removal**: AI-powered background removal for images
- **Image Upscaling**: Enhance image resolution with AI
- **Image Enhancement**: Improve image quality automatically
- **Colorization**: Add color to black and white images
- **Noise Reduction**: Remove noise from images

### 📋 Template System
- **Admin Interface**: Create and manage poster templates
- **User Interface**: Browse and customize existing templates
- **Editable Zones**: Define specific areas users can modify
- **Template Categories**: Organize templates by category
- **Role-based Access**: Separate admin and user experiences

### 🎯 Enhanced Features
- **Responsive Design**: Adaptive layout that works on all screen sizes
- **Comprehensive Formatting**: Full text formatting, styling, and object properties
- **Advanced Transform Controls**: Rotation, scaling, and positioning
- **Shadow Effects**: Add shadows to any object
- **Image Filters**: Brightness, contrast, saturation, and more

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

### Test Coverage
The project maintains comprehensive test coverage including:
- **Unit Tests**: Individual component and service testing
- **Integration Tests**: Component interaction testing
- **Store Tests**: State management testing
- **AI Service Tests**: AI functionality testing

## Technologies Used

- **Next.js 15**: React framework with App Router
- **React 19**: Latest React features
- **Fabric.js**: Canvas manipulation library
- **Zustand**: Lightweight state management
- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives
- **Lucide React**: Beautiful icons
- **Jest**: Testing framework
- **Testing Library**: React component testing

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Add tests for new functionality
4. Ensure all tests pass
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Saving and Exporting
- **Auto-save**: Changes are automatically saved to localStorage
- **Manual Save**: Click the Save button in the header
- **Export**: Click Export to download as PNG

## Key Components

### Layout Structure
The application follows the Vue poster app layout:
- **Header**: Design name, save status, editing controls, export
- **Left Sidebar**: 100px tool selection + 360px tool panels
- **Canvas**: Centered with controls and zoom functionality
- **Right Sidebar**: 320px with layers and properties tabs

### Fabric.js Integration
- **Canvas Initialization**: Proper setup with drawing brushes and controls
- **Object Management**: Add, edit, delete, clone objects
- **Event Handling**: Selection, modification, and canvas change events
- **Custom Styling**: Blue-themed selection controls

### State Management
- **Canvas State**: Canvas instance and objects
- **UI State**: Active tools, panel visibility, selected objects
- **Design State**: Name, save status, modification tracking
- **Layer State**: Layer list with visibility and lock status

## Customization

### Adding New Shapes
1. Add shape definition to `src/fabric/shapes/shape-definitions.js`
2. Update shape factory in `src/fabric/shapes/shape-factory.js`
3. Add to shapes panel in `src/components/editor/panels/ShapesPanel.jsx`

### Adding New Tools
1. Create new panel component in `src/components/editor/panels/`
2. Add tool definition to `src/components/editor/LeftSidebar.jsx`
3. Update store state if needed

### Styling
- **Global Styles**: `src/app/globals.css`
- **Tailwind Config**: `tailwind.config.js`
- **Component Styles**: Inline Tailwind classes

## Technical Details

### Dependencies
- **Next.js 15.2.4**: React framework
- **React 19**: UI library
- **Fabric.js 6.6.2**: Canvas manipulation
- **Zustand 5.0.3**: State management
- **Tailwind CSS 3.3.6**: Styling
- **Radix UI**: Accessible UI components
- **Lucide React**: Icons

### Browser Support
- Modern browsers with ES6+ support
- Canvas API support required
- File API for uploads

### Performance
- Dynamic imports for Fabric.js
- Debounced auto-save
- Efficient re-renders with Zustand

## Troubleshooting

### Common Issues
1. **Canvas not loading**: Check browser console for Fabric.js errors
2. **Images not loading**: Verify CORS settings for external URLs
3. **Save not working**: Check localStorage availability
4. **Performance issues**: Reduce canvas object count

### Development
- Use browser dev tools for debugging
- Check console for Fabric.js warnings
- Monitor network tab for image loading issues

## Future Enhancements

### Planned Features
- **Undo/Redo**: History stack implementation
- **Collaboration**: Real-time editing
- **More Templates**: Expanded template library
- **Advanced Text**: Rich text editing
- **Filters**: Image filters and effects
- **Animation**: Object animations
- **Cloud Save**: Server-side persistence

### Performance Improvements
- **Virtual Scrolling**: For large layer lists
- **Canvas Optimization**: Object pooling
- **Image Optimization**: WebP support
- **Lazy Loading**: Template thumbnails

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
