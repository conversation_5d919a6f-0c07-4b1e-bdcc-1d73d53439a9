"use client";

import { useEditorStore } from "@/store";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import LayersPanel from "./panels/LayersPanel";
import PropertiesPanel from "./panels/PropertiesPanel";
import { Layers, Settings, X } from "lucide-react";

export default function RightSidebar() {
  const { 
    showLayers, 
    showProperties, 
    setShowLayers, 
    setShowProperties,
    selectedObject 
  } = useEditorStore();

  // Show sidebar if either layers or properties should be shown
  const shouldShowSidebar = showLayers || showProperties;

  if (!shouldShowSidebar) {
    return null;
  }

  return (
    <div className="w-[320px] bg-white border-l h-full flex flex-col">
      <Tabs defaultValue="layers" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 m-2">
          <TabsTrigger value="layers" className="flex items-center gap-2">
            <Layers className="w-4 h-4" />
            Layers
          </TabsTrigger>
          <TabsTrigger 
            value="properties" 
            className="flex items-center gap-2"
            disabled={!selectedObject}
          >
            <Settings className="w-4 h-4" />
            Properties
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="layers" className="flex-1 m-0">
          <LayersPanel />
        </TabsContent>
        
        <TabsContent value="properties" className="flex-1 m-0">
          {selectedObject ? (
            <PropertiesPanel />
          ) : (
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center text-gray-500">
                <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">Select an object to view properties</p>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
