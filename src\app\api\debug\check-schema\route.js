import { NextResponse } from 'next/server';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

export async function GET() {
  try {
    const client = await pool.connect();
    
    try {
      // Get templates table schema
      const templatesSchema = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'templates'
        ORDER BY ordinal_position
      `);
      
      // Get designs table schema
      const designsSchema = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'designs'
        ORDER BY ordinal_position
      `);
      
      return NextResponse.json({
        success: true,
        schemas: {
          templates: templatesSchema.rows,
          designs: designsSchema.rows
        }
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error checking schema:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
