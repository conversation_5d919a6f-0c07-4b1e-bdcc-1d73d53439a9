"use client";

import { useState, useEffect } from "react";
import { useEditorStore } from "@/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { addImageToCanvas } from "@/fabric/fabric-utils";
import { searchPhotos, getFeaturedPhotos, getPopularCategories, trackDownload, isUnsplashConfigured } from "@/services/unsplash";
import { X, Upload, Link, Image as ImageIcon, Search, Loader2, ExternalLink, Grid, Camera } from "lucide-react";

export default function EnhancedImagesPanel() {
  const { setActivePanel, canvas, markAsModified } = useEditorStore();
  const [imageUrl, setImageUrl] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [featuredImages, setFeaturedImages] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingFeatured, setIsLoadingFeatured] = useState(false);
  const [activeTab, setActiveTab] = useState("featured"); // "featured", "search", "upload"
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  const handleClose = () => {
    setActivePanel('select');
  };

  // Load featured images on component mount
  useEffect(() => {
    loadFeaturedImages();
  }, []);

  const loadFeaturedImages = async () => {
    setIsLoadingFeatured(true);
    try {
      const images = await getFeaturedPhotos(1, 20);
      setFeaturedImages(images);
    } catch (error) {
      console.error("Error loading featured images:", error);
    } finally {
      setIsLoadingFeatured(false);
    }
  };

  const handleSearch = async (query = searchQuery, page = 1) => {
    if (!query.trim()) return;

    setIsSearching(true);
    try {
      const results = await searchPhotos(query, page, 20);
      if (page === 1) {
        setSearchResults(results.results);
      } else {
        setSearchResults(prev => [...prev, ...results.results]);
      }
      setCurrentPage(page);
      setHasMore(page < results.totalPages);
      setActiveTab("search");
    } catch (error) {
      console.error("Error searching images:", error);
      alert("Failed to search images. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  const handleLoadMore = () => {
    if (!isSearching && hasMore) {
      handleSearch(searchQuery, currentPage + 1);
    }
  };

  const handleCategorySearch = (category) => {
    setSearchQuery(category);
    handleSearch(category, 1);
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files?.[0];
    if (!file || !canvas) return;

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const dataUrl = e.target?.result;
        if (dataUrl) {
          await addImageToCanvas(canvas, dataUrl);
          markAsModified();
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error("Error uploading image:", error);
      alert("Failed to upload image. Please try again.");
    }
  };

  const addImageFromUrl = async () => {
    if (!canvas || !imageUrl.trim()) return;
    
    try {
      await addImageToCanvas(canvas, imageUrl.trim());
      markAsModified();
      setImageUrl('');
    } catch (error) {
      console.error("Error adding image:", error);
      alert("Failed to load image. Please check the URL and try again.");
    }
  };

  const addUnsplashImage = async (image) => {
    if (!canvas) return;
    
    try {
      // Track download for Unsplash API compliance
      if (image.downloadUrl) {
        await trackDownload(image.downloadUrl);
      }
      
      await addImageToCanvas(canvas, image.url);
      markAsModified();
    } catch (error) {
      console.error("Error adding Unsplash image:", error);
      alert("Failed to add image. Please try again.");
    }
  };

  const ImageGrid = ({ images, isLoading }) => (
    <div className="grid grid-cols-2 gap-2 p-4">
      {images.map((image) => (
        <div key={image.id} className="group relative">
          <button
            onClick={() => addUnsplashImage(image)}
            className="w-full aspect-square overflow-hidden rounded-lg border border-gray-200 hover:border-blue-400 transition-colors"
          >
            <img
              src={image.thumb}
              alt={image.alt}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform"
            />
          </button>
          <div className="absolute bottom-1 left-1 right-1 bg-black/50 text-white text-xs p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="truncate">{image.alt}</div>
            {image.author && (
              <div className="truncate text-gray-300">by {image.author}</div>
            )}
          </div>
        </div>
      ))}
      {isLoading && (
        <div className="col-span-2 flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2 text-sm text-gray-500">Loading images...</span>
        </div>
      )}
    </div>
  );

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-sm font-medium">Images</h3>
          <p className="text-xs text-gray-500">Add images to your design</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex border-b">
        <button
          onClick={() => setActiveTab("featured")}
          className={`flex-1 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === "featured"
              ? "border-blue-500 text-blue-600"
              : "border-transparent text-gray-500 hover:text-gray-700"
          }`}
        >
          <Grid className="h-4 w-4 inline mr-1" />
          Featured
        </button>
        <button
          onClick={() => setActiveTab("search")}
          className={`flex-1 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === "search"
              ? "border-blue-500 text-blue-600"
              : "border-transparent text-gray-500 hover:text-gray-700"
          }`}
        >
          <Search className="h-4 w-4 inline mr-1" />
          Search
        </button>
        <button
          onClick={() => setActiveTab("upload")}
          className={`flex-1 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === "upload"
              ? "border-blue-500 text-blue-600"
              : "border-transparent text-gray-500 hover:text-gray-700"
          }`}
        >
          <Upload className="h-4 w-4 inline mr-1" />
          Upload
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Featured Tab */}
        {activeTab === "featured" && (
          <div>
            {!isUnsplashConfigured() && (
              <div className="p-4 bg-yellow-50 border-b">
                <p className="text-xs text-yellow-800">
                  <strong>Demo Mode:</strong> Configure Unsplash API key for full functionality
                </p>
              </div>
            )}
            <ImageGrid images={featuredImages} isLoading={isLoadingFeatured} />
          </div>
        )}

        {/* Search Tab */}
        {activeTab === "search" && (
          <div>
            {/* Search Input */}
            <div className="p-4 border-b">
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="Search for images..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1"
                />
                <Button
                  onClick={() => handleSearch()}
                  disabled={isSearching || !searchQuery.trim()}
                  size="sm"
                >
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Popular Categories */}
            <div className="p-4 border-b">
              <h4 className="text-xs font-medium text-gray-700 mb-2">Popular Categories</h4>
              <div className="flex flex-wrap gap-1">
                {getPopularCategories().slice(0, 8).map((category) => (
                  <button
                    key={category}
                    onClick={() => handleCategorySearch(category)}
                    className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            {/* Search Results */}
            <ImageGrid images={searchResults} isLoading={isSearching} />

            {/* Load More Button */}
            {hasMore && searchResults.length > 0 && (
              <div className="p-4">
                <Button
                  onClick={handleLoadMore}
                  disabled={isSearching}
                  variant="outline"
                  className="w-full"
                >
                  {isSearching ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading...
                    </>
                  ) : (
                    "Load More"
                  )}
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Upload Tab */}
        {activeTab === "upload" && (
          <div className="p-4 space-y-4">
            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload from Computer
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    PNG, JPG, GIF up to 10MB
                  </p>
                </label>
              </div>
            </div>

            {/* URL Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Add from URL
              </label>
              <div className="flex space-x-2">
                <Input
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  className="flex-1"
                />
                <Button
                  onClick={addImageFromUrl}
                  disabled={!imageUrl.trim()}
                  size="sm"
                >
                  <Link className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      {isUnsplashConfigured() && (
        <div className="p-2 border-t bg-gray-50">
          <p className="text-xs text-gray-500 text-center">
            Photos by{" "}
            <a
              href="https://unsplash.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline"
            >
              Unsplash
            </a>
          </p>
        </div>
      )}
    </div>
  );
}
