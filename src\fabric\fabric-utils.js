import { shapeDefinitions } from "./shapes/shape-definitions";
import { createShape } from "./shapes/shape-factory";
import { DrawingSessionManager } from "./drawing-session-manager";
import { getFabric } from "./fabric-loader";

export const initializeFabric = async (canvasEl, containerEl) => {
  try {
    const { Canvas, PencilBrush } = await getFabric();

    const canvas = new Canvas(canvasEl, {
      preserveObjectStacking: true,
      isDrawingMode: false,
      renderOnAddRemove: true,
      selection: true,
      allowTouchScrolling: false,
      // Improve image rendering quality
      imageSmoothingEnabled: true,
      enableRetinaScaling: true,
      // Prevent rendering artifacts
      backgroundColor: 'transparent',
    });

    //drawing init
    const brush = new PencilBrush(canvas);
    brush.color = "#000000";
    brush.width = 5;
    canvas.freeDrawingBrush = brush;

    return canvas;
  } catch (e) {
    console.error("Failed to load fabric", e);
    return null;
  }
};

export const centerCanvas = (canvas) => {
  if (!canvas || !canvas.wrapperEl) return;

  const canvasWrapper = canvas.wrapperEl;

  // Set canvas wrapper to fill the entire container
  canvasWrapper.style.width = "100%";
  canvasWrapper.style.height = "100%";
  canvasWrapper.style.position = "absolute";
  canvasWrapper.style.top = "0";
  canvasWrapper.style.left = "0";
  canvasWrapper.style.transform = "none";
};

// Export only the design area as image
// Filter to get only user objects (exclude design area elements)
export const getUserObjects = (canvas) => {
  if (!canvas) return [];

  return canvas.getObjects().filter(obj =>
    !obj.isDesignArea &&
    !obj.isDesignAreaElement &&
    obj.id !== 'design-area' &&
    !obj.id?.startsWith('corner-indicator') &&
    !obj.excludeFromExport
  );
};

// Enhanced history management system for step-by-step undo/redo
// Get current canvas state with only user objects
export const getCurrentCanvasState = (canvas) => {
  if (!canvas) return null;

  const userObjects = getUserObjects(canvas);

  return {
    objects: userObjects.map(obj => obj.toObject(['id'])),
    timestamp: Date.now(),
    isCanvasState: true
  };
};

// Create a detailed history entry for specific operations
export const createHistoryEntry = (canvas, operation, objectId = null, previousState = null, newState = null) => {
  if (!canvas) return null;

  const userObjects = getUserObjects(canvas);

  return {
    operation,
    objectId,
    previousState,
    newState,
    canvasState: {
      objects: userObjects.map(obj => obj.toObject(['id'])),
      timestamp: Date.now(),
      isCanvasState: true
    },
    timestamp: Date.now()
  };
};

// Capture object state for history tracking
export const captureObjectState = (obj) => {
  if (!obj) return null;

  try {
    return {
      id: obj.id,
      left: obj.left,
      top: obj.top,
      scaleX: obj.scaleX,
      scaleY: obj.scaleY,
      angle: obj.angle,
      flipX: obj.flipX,
      flipY: obj.flipY,
      opacity: obj.opacity,
      visible: obj.visible,
      // Capture type-specific properties
      ...(obj.type === 'i-text' && {
        text: obj.text,
        fontSize: obj.fontSize,
        fontFamily: obj.fontFamily,
        fill: obj.fill,
        textAlign: obj.textAlign
      }),
      ...(obj.type === 'path' && {
        path: obj.path,
        pathOffset: obj.pathOffset,
        stroke: obj.stroke,
        strokeWidth: obj.strokeWidth,
        strokeLineCap: obj.strokeLineCap,
        strokeLineJoin: obj.strokeLineJoin,
        fill: obj.fill
      }),
      ...(obj.fill && typeof obj.fill === 'string' && { fill: obj.fill }),
      ...(obj.stroke && { stroke: obj.stroke }),
      ...(obj.strokeWidth && { strokeWidth: obj.strokeWidth }),
      // Add other properties as needed
      fullObject: obj.toObject(['id'])
    };
  } catch (error) {
    console.error('Error capturing object state:', error);
    return {
      id: obj.id,
      left: obj.left,
      top: obj.top,
      scaleX: obj.scaleX || 1,
      scaleY: obj.scaleY || 1,
      angle: obj.angle || 0,
      flipX: obj.flipX || false,
      flipY: obj.flipY || false,
      opacity: obj.opacity || 1,
      visible: obj.visible !== false,
      fullObject: null // Fallback without full object
    };
  }
};

// Enhanced History Manager Class
export class CanvasHistoryManager {
  constructor(canvas, maxHistorySize = 50, onStateChange = null, drawingSessionOptions = {}) {
    this.canvas = canvas;
    this.history = [];
    this.currentIndex = -1;
    this.maxHistorySize = maxHistorySize;
    this.isUndoRedoInProgress = false;
    this.isTrackingDisabled = false; // Flag to temporarily disable tracking
    this.objectStates = new Map(); // Track object states before modifications
    this.onStateChange = onStateChange; // Callback to notify when state changes
    this.lastRedoTime = 0; // Throttle rapid redo calls

    // Drawing session manager
    this.drawingSessionManager = new DrawingSessionManager(canvas, {
      sessionTimeout: drawingSessionOptions.sessionTimeout || 3000,
      maxSessionPaths: drawingSessionOptions.maxSessionPaths || 50,
      onSessionStart: (session) => this.onDrawingSessionStart(session),
      onSessionEnd: (session) => this.onDrawingSessionEnd(session),
      onPathAdded: (pathData, session) => this.onPathAddedToSession(pathData, session)
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    if (!this.canvas) return;

    // Track object modifications with before/after states
    this.canvas.on('object:moving', this.handleObjectMoving.bind(this));
    this.canvas.on('object:scaling', this.handleObjectScaling.bind(this));
    this.canvas.on('object:rotating', this.handleObjectRotating.bind(this));
    this.canvas.on('object:modified', this.handleObjectModified.bind(this));

    // Track object additions and removals
    this.canvas.on('object:added', this.handleObjectAdded.bind(this));
    this.canvas.on('object:removed', this.handleObjectRemoved.bind(this));

    // Track drawing operations
    this.canvas.on('path:created', this.handlePathCreated.bind(this));

    // Track text editing
    this.canvas.on('text:editing:entered', this.handleTextEditingStarted.bind(this));
    this.canvas.on('text:editing:exited', this.handleTextEditingFinished.bind(this));

    // Track layer operations (stacking order changes) - DISABLED for now due to initialization issues
    // this.setupLayerTracking();
  }

  setupLayerTracking() {
    if (!this.canvas) return;

    // Store original canvas methods to wrap them with history tracking
    // Use bind to ensure proper context - Updated for Fabric.js v6
    const originalBringForward = this.canvas.bringObjectForward?.bind(this.canvas);
    const originalSendBackwards = this.canvas.sendObjectBackwards?.bind(this.canvas);
    const originalBringToFront = this.canvas.bringObjectToFront?.bind(this.canvas);
    const originalSendToBack = this.canvas.sendObjectToBack?.bind(this.canvas);

    // Only wrap methods that exist
    if (originalBringForward) {
      this.canvas.bringObjectForward = (object, intersecting) => {
        if (!this.isUndoRedoInProgress && !this.isTrackingDisabled && !this.isDesignAreaElement(object)) {
          const beforeIndex = this.canvas.getObjects().indexOf(object);
          const result = originalBringForward(object, intersecting);
          const afterIndex = this.canvas.getObjects().indexOf(object);

          if (beforeIndex !== afterIndex) {
            this.addHistoryEntry('layer', object.id,
              { operation: 'bringForward', fromIndex: beforeIndex, toIndex: afterIndex },
              { operation: 'sendBackwards', fromIndex: afterIndex, toIndex: beforeIndex }
            );
          }
          return result;
        }
        return originalBringForward(object, intersecting);
      };
    }

    if (originalSendBackwards) {
      this.canvas.sendObjectBackwards = (object, intersecting) => {
        if (!this.isUndoRedoInProgress && !this.isTrackingDisabled && !this.isDesignAreaElement(object)) {
          const beforeIndex = this.canvas.getObjects().indexOf(object);
          const result = originalSendBackwards(object, intersecting);
          const afterIndex = this.canvas.getObjects().indexOf(object);

          if (beforeIndex !== afterIndex) {
            this.addHistoryEntry('layer', object.id,
              { operation: 'sendBackwards', fromIndex: beforeIndex, toIndex: afterIndex },
              { operation: 'bringForward', fromIndex: afterIndex, toIndex: beforeIndex }
            );
          }
          return result;
        }
        return originalSendBackwards(object, intersecting);
      };
    }

    if (originalBringToFront) {
      this.canvas.bringObjectToFront = (object) => {
        if (!this.isUndoRedoInProgress && !this.isTrackingDisabled && !this.isDesignAreaElement(object)) {
          const beforeIndex = this.canvas.getObjects().indexOf(object);
          const result = originalBringToFront(object);
          const afterIndex = this.canvas.getObjects().indexOf(object);

          if (beforeIndex !== afterIndex) {
            this.addHistoryEntry('layer', object.id,
              { operation: 'bringToFront', fromIndex: beforeIndex, toIndex: afterIndex },
              { operation: 'moveToIndex', fromIndex: afterIndex, toIndex: beforeIndex }
            );
          }
          return result;
        }
        return originalBringToFront(object);
      };
    }

    if (originalSendToBack) {
      this.canvas.sendObjectToBack = (object) => {
        if (!this.isUndoRedoInProgress && !this.isTrackingDisabled && !this.isDesignAreaElement(object)) {
          const beforeIndex = this.canvas.getObjects().indexOf(object);
          const result = originalSendToBack(object);
          const afterIndex = this.canvas.getObjects().indexOf(object);

          if (beforeIndex !== afterIndex) {
            this.addHistoryEntry('layer', object.id,
              { operation: 'sendToBack', fromIndex: beforeIndex, toIndex: afterIndex },
              { operation: 'moveToIndex', fromIndex: afterIndex, toIndex: beforeIndex }
            );
          }
          return result;
        }
        return originalSendToBack(object);
      };
    }
  }

  handleObjectMoving(e) {
    this.captureObjectStateBefore(e.target);
  }

  handleObjectScaling(e) {
    this.captureObjectStateBefore(e.target);
  }

  handleObjectRotating(e) {
    this.captureObjectStateBefore(e.target);
  }

  handleObjectModified(e) {
    if (this.isUndoRedoInProgress || this.isTrackingDisabled) return;

    const obj = e.target;
    if (this.isDesignAreaElement(obj)) return;

    const beforeState = this.objectStates.get(obj.id);
    const afterState = captureObjectState(obj);

    if (beforeState && afterState) {
      this.addHistoryEntry('modify', obj.id, beforeState, afterState);
    }

    // Clean up the stored state
    this.objectStates.delete(obj.id);
  }

  handleObjectAdded(e) {
    if (this.isUndoRedoInProgress || this.isTrackingDisabled) return;

    const obj = e.target;
    if (this.isDesignAreaElement(obj)) return;

    // Skip tracking for paths - they will be tracked by path:created event
    if (obj.type === 'path') {
      console.log(`Path object added (will be tracked by path:created): ${obj.id || 'no-id'} (${obj.type})`);
      return;
    }

    // Ensure object has an ID
    if (!obj.id) {
      obj.id = `object-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }

    console.log(`Object added: ${obj.id} (${obj.type})`);

    // Add to history after a short delay to ensure object is fully initialized
    setTimeout(() => {
      if (!this.isUndoRedoInProgress && !this.isTrackingDisabled) {
        this.addHistoryEntry('add', obj.id, null, captureObjectState(obj));
      }
    }, 50);
  }

  handleObjectRemoved(e) {
    if (this.isUndoRedoInProgress || this.isTrackingDisabled) return;

    const obj = e.target;
    if (this.isDesignAreaElement(obj)) return;

    this.addHistoryEntry('remove', obj.id, captureObjectState(obj), null);
  }

  handlePathCreated(e) {
    if (this.isUndoRedoInProgress || this.isTrackingDisabled) return;

    const path = e.path;
    if (path) {
      // Ensure the path has an ID
      if (!path.id) {
        path.id = `path-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      }

      console.log(`Path created: ${path.id}`);

      // Add path to drawing session
      this.drawingSessionManager.addPathToSession(path);

      // Add to history after a short delay to ensure path is fully initialized
      setTimeout(() => {
        if (!this.isUndoRedoInProgress && !this.isTrackingDisabled) {
          // Capture path data in a more reliable format for restoration
          const pathState = {
            id: path.id,
            pathData: path.path, // The SVG path string
            fullObject: path.toObject(['id']), // Full object data
            // Additional path-specific properties for manual recreation
            stroke: path.stroke,
            strokeWidth: path.strokeWidth,
            fill: path.fill,
            left: path.left,
            top: path.top,
            scaleX: path.scaleX,
            scaleY: path.scaleY,
            angle: path.angle,
            opacity: path.opacity,
            // Add session information
            sessionId: this.drawingSessionManager.currentSession?.id
          };

          this.addHistoryEntry('draw', path.id, null, pathState);
        }
      }, 50);
    }
  }

  handleTextEditingStarted(e) {
    this.captureObjectStateBefore(e.target);
  }

  handleTextEditingFinished(e) {
    if (this.isUndoRedoInProgress) return;

    const obj = e.target;
    const beforeState = this.objectStates.get(obj.id);
    const afterState = captureObjectState(obj);

    if (beforeState && afterState && beforeState.text !== afterState.text) {
      this.addHistoryEntry('text-edit', obj.id, beforeState, afterState);
    }

    this.objectStates.delete(obj.id);
  }

  captureObjectStateBefore(obj) {
    if (this.isUndoRedoInProgress || this.isDesignAreaElement(obj)) return;

    if (!this.objectStates.has(obj.id)) {
      this.objectStates.set(obj.id, captureObjectState(obj));
    }
  }

  isDesignAreaElement(obj) {
    return obj.isDesignArea ||
           obj.isDesignAreaElement ||
           obj.id === 'design-area' ||
           obj.id?.startsWith('corner-indicator') ||
           obj.excludeFromExport;
  }

  addHistoryEntry(operation, objectId, previousState, newState) {
    // Remove any future history if we're not at the end
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }

    const historyEntry = createHistoryEntry(this.canvas, operation, objectId, previousState, newState);

    if (historyEntry) {
      this.history.push(historyEntry);
      this.currentIndex++;

      console.log(`Added history entry: ${operation} (${objectId}). Index: ${this.currentIndex}, Length: ${this.history.length}`);

      // Limit history size
      if (this.history.length > this.maxHistorySize) {
        this.history.shift();
        this.currentIndex--;
      }

      // Notify state change
      if (this.onStateChange) {
        this.onStateChange();
      }
    }
  }

  canUndo() {
    // Can undo if we have history and we're not at the initial state (index 0)
    return this.currentIndex > 0;
  }

  canRedo() {
    return this.currentIndex < this.history.length - 1;
  }

  // Force reset undo/redo state - useful for debugging
  forceResetUndoRedoState() {
    console.log('🔧 Force resetting undo/redo state...');
    this.isUndoRedoInProgress = false;
    this.isTrackingDisabled = false;
    console.log('✅ Undo/redo state reset');
  }

  async undo() {
    console.log(`Undo called. Can undo: ${this.canUndo()}, Current index: ${this.currentIndex}, History length: ${this.history.length}`);

    if (!this.canUndo()) return false;

    this.isUndoRedoInProgress = true;

    try {
      const historyEntry = this.history[this.currentIndex];

      console.log(`Undoing operation: ${historyEntry.operation} for object: ${historyEntry.objectId}`);

      await this.applyHistoryEntry(historyEntry, 'undo');
      this.currentIndex--;

      console.log(`Undo completed. New index: ${this.currentIndex}, Can redo: ${this.canRedo()}`);

      // Notify state change
      if (this.onStateChange) {
        this.onStateChange();
      }

      return true;
    } catch (error) {
      console.error('Undo failed:', error);
      return false;
    } finally {
      this.isUndoRedoInProgress = false;
    }
  }

  async redo() {
    console.log(`=== REDO CALLED ===`);
    console.log(`Can redo: ${this.canRedo()}`);
    console.log(`Current index: ${this.currentIndex}`);
    console.log(`History length: ${this.history.length}`);
    console.log(`Is undo/redo in progress: ${this.isUndoRedoInProgress}`);
    console.log(`Is tracking disabled: ${this.isTrackingDisabled}`);

    if (!this.canRedo()) {
      console.log('❌ Cannot redo - exiting');
      return false;
    }

    if (this.isUndoRedoInProgress) {
      console.log('❌ Redo already in progress - exiting');
      return false;
    }

    // Add a small delay to prevent rapid successive calls
    if (this.lastRedoTime && Date.now() - this.lastRedoTime < 100) {
      console.log('❌ Redo called too quickly - throttling');
      return false;
    }
    this.lastRedoTime = Date.now();

    // Ensure canvas is available
    if (!this.canvas) {
      console.error('❌ Canvas is not available for redo');
      return false;
    }

    this.isUndoRedoInProgress = true;

    try {
      const newIndex = this.currentIndex + 1;
      const historyEntry = this.history[newIndex];

      console.log(`📝 Redoing operation: ${historyEntry.operation} for object: ${historyEntry.objectId}`);
      console.log(`📝 History entry details:`, {
        operation: historyEntry.operation,
        objectId: historyEntry.objectId,
        hasNewState: !!historyEntry.newState,
        hasPreviousState: !!historyEntry.previousState,
        newStateType: historyEntry.newState?.fullObject?.type || 'unknown'
      });

      // Check canvas state before redo
      const canvasObjectsBefore = this.canvas.getObjects().filter(obj =>
        !obj.isDesignArea && !obj.isDesignAreaElement
      );
      console.log(`📊 Canvas objects before redo: ${canvasObjectsBefore.length}`);

      // Special handling for redoing from initial state (index 0)
      if (this.currentIndex === 0) {
        console.log('🏁 Redoing from initial state - ensuring canvas is clean');
        // Make sure canvas is in initial state before applying redo
        const userObjects = getUserObjects(this.canvas);
        if (userObjects.length > 0) {
          console.log(`🧹 Cleaning ${userObjects.length} unexpected objects before redo`);
          userObjects.forEach(obj => this.canvas.remove(obj));
          this.canvas.renderAll();
        }
      }

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Redo operation timed out')), 10000); // Increased timeout
      });

      await Promise.race([
        this.applyHistoryEntry(historyEntry, 'redo'),
        timeoutPromise
      ]);

      this.currentIndex = newIndex;

      // Check canvas state after redo
      const canvasObjectsAfter = this.canvas.getObjects().filter(obj =>
        !obj.isDesignArea && !obj.isDesignAreaElement
      );
      console.log(`📊 Canvas objects after redo: ${canvasObjectsAfter.length}`);

      // Verify the specific object was restored for 'add' operations
      if (historyEntry.operation === 'add' && canvasObjectsAfter.length === canvasObjectsBefore.length) {
        console.error(`❌ REDO VERIFICATION FAILED: Object ${historyEntry.objectId} was not actually added to canvas`);
        console.log(`Expected object count: ${canvasObjectsBefore.length + 1}, Actual: ${canvasObjectsAfter.length}`);

        // Try to find the object
        const targetObj = this.canvas.getObjects().find(o => o.id === historyEntry.objectId);
        if (targetObj) {
          console.log(`🔍 Object exists but may be filtered out:`, {
            id: targetObj.id,
            type: targetObj.type,
            isDesignArea: targetObj.isDesignArea,
            isDesignAreaElement: targetObj.isDesignAreaElement,
            visible: targetObj.visible
          });
        } else {
          console.log(`🔍 Object ${historyEntry.objectId} not found on canvas at all`);
        }
      }

      console.log(`✅ Redo completed. New index: ${this.currentIndex}, Can undo: ${this.canUndo()}, Can redo: ${this.canRedo()}`);

      // Force canvas re-render
      this.canvas.renderAll();

      // Notify state change
      if (this.onStateChange) {
        this.onStateChange();
      }

      return true;
    } catch (error) {
      console.error('❌ Redo failed:', error);
      console.error('Error details:', error.stack);

      // Force canvas re-render even on failure
      this.canvas.renderAll();

      return false;
    } finally {
      // Always reset the flag in finally block
      this.isUndoRedoInProgress = false;
      console.log(`=== REDO FINISHED (flag reset) ===`);
    }
  }

  async applyHistoryEntry(entry, direction) {
    const { operation, objectId, previousState, newState } = entry;

    console.log(`🔄 Applying ${direction} for operation: ${operation}, objectId: ${objectId}`);

    try {
      switch (operation) {
        case 'add':
          console.log(`📦 ${direction === 'undo' ? 'Removing' : 'Restoring'} added object: ${objectId}`);
          if (direction === 'undo') {
            await this.removeObject(objectId);
          } else {
            if (!newState) {
              console.error(`❌ Cannot restore added object - newState is null for ${objectId}`);
              throw new Error(`Missing newState for add operation: ${objectId}`);
            }
            console.log(`🔧 Restoring object with newState:`, {
              id: newState.id,
              type: newState.fullObject?.type,
              hasFullObject: !!newState.fullObject
            });
            await this.restoreObject(newState);
          }
          break;

        case 'remove':
          console.log(`🗑️ ${direction === 'undo' ? 'Restoring' : 'Removing'} removed object: ${objectId}`);
          if (direction === 'undo') {
            await this.restoreObject(previousState);
          } else {
            await this.removeObject(objectId);
          }
          break;

        case 'modify':
        case 'text-edit':
          const targetState = direction === 'undo' ? previousState : newState;
          console.log(`✏️ ${direction === 'undo' ? 'Reverting' : 'Applying'} ${operation} for object: ${objectId}`);
          if (targetState) {
            await this.updateObject(targetState);
          }
          break;

        case 'draw':
          console.log(`🎨 ${direction === 'undo' ? 'Removing' : 'Restoring'} drawing: ${objectId}`);
          if (direction === 'undo') {
            await this.removeObject(objectId);
          } else {
            if (!newState) {
              console.error(`❌ Cannot restore drawing - newState is null for ${objectId}`);
              throw new Error(`Missing newState for draw operation: ${objectId}`);
            }
            console.log(`🔍 Restoring drawing with newState:`, {
              id: newState.id,
              hasPathData: !!newState.pathData,
              hasFullObject: !!newState.fullObject,
              type: newState.fullObject?.type
            });
            await this.restorePathObject(newState);
          }
          break;

        case 'layer':
          console.log(`📚 Layer operations temporarily disabled`);
          // Layer operations temporarily disabled due to initialization issues
          break;

        case 'initial':
          console.log(`🏁 Applying initial state - clearing all objects`);
          // Initial state - clear all user objects
          const userObjects = getUserObjects(this.canvas);
          userObjects.forEach(obj => this.canvas.remove(obj));
          break;

        default:
          console.warn(`❓ Unknown operation type: ${operation}`);
      }

      console.log(`✅ Successfully applied ${direction} for ${operation}`);
      this.canvas.renderAll();
    } catch (error) {
      console.error(`❌ Failed to apply ${direction} for operation ${operation}:`, error);
      throw error;
    }
  }

  async removeObject(objectId) {
    const obj = this.canvas.getObjects().find(o => o.id === objectId);
    if (obj && !this.isDesignAreaElement(obj)) {
      this.canvas.remove(obj);
    }
  }

  async restorePathObject(pathState) {
    console.log(`🎨 restorePathObject called for:`, pathState?.id || 'unknown');

    if (!pathState || !pathState.pathData) {
      console.error(`❌ Invalid pathState for restoration:`, pathState);
      return;
    }

    return new Promise(async (resolve) => {
      try {
        const fabric = await getFabric();

        console.log(`🔧 Creating path from data: ${pathState.id}`);

        // Remove any existing object with the same ID first
        const existingObj = this.canvas.getObjects().find(o => o.id === pathState.id);
        if (existingObj && !this.isDesignAreaElement(existingObj)) {
          this.canvas.remove(existingObj);
        }

        // Create new path object using the saved path data
        const pathObj = new fabric.Path(pathState.pathData, {
          id: pathState.id,
          stroke: pathState.stroke,
          strokeWidth: pathState.strokeWidth,
          fill: pathState.fill,
          left: pathState.left,
          top: pathState.top,
          scaleX: pathState.scaleX,
          scaleY: pathState.scaleY,
          angle: pathState.angle,
          opacity: pathState.opacity
        });

        this.canvas.add(pathObj);
        this.canvas.renderAll();

        console.log(`✅ Path restored successfully: ${pathState.id}`);
        resolve();

      } catch (error) {
        console.error(`❌ Failed to restore path ${pathState.id}:`, error);
        // Fallback to regular object restoration
        try {
          if (pathState.fullObject) {
            await this.restoreObject(pathState);
          } else {
            console.warn(`⚠️ No fallback data available for ${pathState.id}`);
            resolve(); // Resolve to not block operation
          }
        } catch (fallbackError) {
          console.error(`❌ Fallback restoration also failed:`, fallbackError);
          resolve(); // Resolve to not block operation
        }
      }
    });
  }

  async restoreObject(objectState) {
    console.log(`🔧 restoreObject called for:`, objectState?.id || 'unknown');

    if (!objectState || !objectState.fullObject) {
      console.error(`❌ Invalid objectState for restoration:`, objectState);
      return;
    }

    return new Promise(async (resolve, reject) => {
      try {
        const fabric = await getFabric();

        // Use standard restoration for all objects including paths
        console.log(`Restoring object: ${objectState.id} (type: ${objectState.fullObject.type})`);

        // Remove any existing object with the same ID first
        const existingObj = this.canvas.getObjects().find(o => o.id === objectState.id);
        if (existingObj && !this.isDesignAreaElement(existingObj)) {
          console.log(`Removing existing object with ID: ${objectState.id}`);
          this.canvas.remove(existingObj);
        }

        // For simple objects, try fallback first as it's more reliable
        const simpleTypes = ['rect', 'rectangle', 'Rect', 'Rectangle', 'circle', 'Circle', 'ellipse', 'Ellipse', 'triangle', 'Triangle', 'polygon', 'Polygon', 'line', 'Line'];
        if (simpleTypes.includes(objectState.fullObject.type)) {
          console.log(`🚀 Using direct fallback for simple object type: ${objectState.fullObject.type}`);
          try {
            await this.fallbackObjectRestore(objectState, resolve);
            return; // Exit early if fallback succeeds
          } catch (fallbackError) {
            console.log(`⚠️ Fallback failed, trying enlivenObjects: ${fallbackError.message}`);
            // Continue to enlivenObjects if fallback fails
          }
        }

        // Set a timeout for enlivenObjects since it can hang for Path objects
        let resolved = false;
        const timeoutId = setTimeout(() => {
          if (!resolved) {
            resolved = true;
            console.warn(`⚠️ enlivenObjects timed out for ${objectState.id}, attempting fallback restoration`);

            // Try fallback restoration for simple objects
            this.fallbackObjectRestore(objectState, resolve).catch(fallbackError => {
              console.error(`❌ Fallback restoration failed:`, fallbackError);
              resolve(); // Resolve to not block the operation
            });
          }
        }, 2000); // 2 second timeout for object restoration

        // Regular object restoration using enlivenObjects
        fabric.util.enlivenObjects([objectState.fullObject], (objects) => {
          if (resolved) return; // Already timed out

          try {
            clearTimeout(timeoutId);
            resolved = true;

            if (objects && objects.length > 0) {
              const obj = objects[0];
              obj.id = objectState.id;

              this.canvas.add(obj);
              console.log(`✅ Object restored successfully: ${objectState.id}`);
              resolve();
            } else {
              console.warn(`⚠️ No objects created for: ${objectState.id}, trying fallback`);
              this.fallbackObjectRestore(objectState, resolve).catch(fallbackError => {
                console.error(`❌ Fallback restoration failed:`, fallbackError);
                resolve();
              });
            }
          } catch (error) {
            console.error(`❌ Error in enlivenObjects callback:`, error);
            // Try fallback restoration
            this.fallbackObjectRestore(objectState, resolve).catch(fallbackError => {
              console.error(`❌ Fallback restoration failed:`, fallbackError);
              resolve(); // Resolve instead of reject to not block operation
            });
          }
        });
      } catch (error) {
        console.error('Failed to restore object:', error);
        // Try fallback restoration before rejecting
        this.fallbackObjectRestore(objectState, resolve).catch(fallbackError => {
          console.error(`❌ All restoration methods failed:`, fallbackError);
          reject(error);
        });
      }
    });
  }

  // Fallback object restoration for when enlivenObjects fails
  async fallbackObjectRestore(objectState, resolve) {
    console.log(`🔄 Attempting fallback restoration for: ${objectState.id}`);

    try {
      const fabric = await getFabric();
      const objData = objectState.fullObject;
      let obj = null;

      // Create object based on type
      switch (objData.type) {
        case 'rect':
        case 'rectangle':
        case 'Rect':
        case 'Rectangle':
          obj = new fabric.Rect(objData);
          break;
        case 'circle':
        case 'Circle':
          obj = new fabric.Circle(objData);
          break;
        case 'ellipse':
        case 'Ellipse':
          obj = new fabric.Ellipse(objData);
          break;
        case 'triangle':
        case 'Triangle':
          obj = new fabric.Triangle(objData);
          break;
        case 'polygon':
        case 'Polygon':
          obj = new fabric.Polygon(objData.points || [], objData);
          break;
        case 'line':
        case 'Line':
          obj = new fabric.Line([objData.x1 || 0, objData.y1 || 0, objData.x2 || 100, objData.y2 || 100], objData);
          break;
        case 'text':
        case 'textbox':
        case 'i-text':
        case 'Text':
        case 'Textbox':
        case 'IText':
          obj = new fabric.Textbox(objData.text || 'Text', objData);
          break;
        case 'image':
        case 'Image':
          // For images, we need the src - this is more complex
          console.warn(`⚠️ Image fallback restoration not fully implemented for: ${objectState.id}`);
          resolve();
          return;
        case 'path':
        case 'Path':
          // For paths, we need the path data - this is more complex
          if (objectState.pathData) {
            obj = new fabric.Path(objectState.pathData, objData);
          } else {
            console.warn(`⚠️ Path fallback restoration requires pathData for: ${objectState.id}`);
            resolve();
            return;
          }
          break;
        default:
          console.warn(`⚠️ Unknown object type for fallback restoration: ${objData.type}`);
          console.log(`Available objData:`, objData);
          resolve();
          return;
      }

      if (obj) {
        obj.id = objectState.id;

        // Ensure the object is properly configured
        obj.set({
          selectable: true,
          evented: true,
          visible: true
        });

        this.canvas.add(obj);
        this.canvas.renderAll();

        // Verify the object was actually added
        const addedObj = this.canvas.getObjects().find(o => o.id === objectState.id);
        if (addedObj) {
          console.log(`✅ Fallback restoration successful: ${objectState.id} (verified on canvas)`);
        } else {
          console.error(`❌ Fallback restoration failed: ${objectState.id} not found on canvas after add`);
        }
      } else {
        console.error(`❌ Failed to create object in fallback for: ${objectState.id}`);
        throw new Error(`Failed to create object of type: ${objData.type}`);
      }

      resolve();
    } catch (error) {
      console.error(`❌ Fallback restoration failed for ${objectState.id}:`, error);
      resolve(); // Still resolve to not block operation
    }
  }

  async updateObject(objectState) {
    const obj = this.canvas.getObjects().find(o => o.id === objectState.id);
    if (obj && !this.isDesignAreaElement(obj)) {
      // Special handling for paths - they can't be easily updated, so restore instead
      if (obj.type === 'path' || objectState.fullObject?.type === 'path') {
        console.log(`Path update requires restoration: ${objectState.id}`);
        await this.restoreObject(objectState);
      } else {
        // Regular object update
        obj.set({
          left: objectState.left,
          top: objectState.top,
          scaleX: objectState.scaleX,
          scaleY: objectState.scaleY,
          angle: objectState.angle,
          flipX: objectState.flipX,
          flipY: objectState.flipY,
          opacity: objectState.opacity,
          visible: objectState.visible,
          ...(objectState.text !== undefined && { text: objectState.text }),
          ...(objectState.fontSize && { fontSize: objectState.fontSize }),
          ...(objectState.fontFamily && { fontFamily: objectState.fontFamily }),
          ...(objectState.fill && { fill: objectState.fill }),
          ...(objectState.stroke && { stroke: objectState.stroke }),
          ...(objectState.strokeWidth && { strokeWidth: objectState.strokeWidth }),
          ...(objectState.textAlign && { textAlign: objectState.textAlign })
        });
        obj.setCoords();
      }
    } else if (!obj) {
      // If object doesn't exist, try to restore it from the full object state
      console.warn(`Object with ID ${objectState.id} not found, attempting to restore from full state`);
      if (objectState.fullObject) {
        await this.restoreObject(objectState);
      }
    }
  }

  async applyLayerOperation(objectId, layerState) {
    const obj = this.canvas.getObjects().find(o => o.id === objectId);
    if (!obj || this.isDesignAreaElement(obj)) return;

    const { operation, toIndex } = layerState;

    console.log(`Applying layer operation: ${operation} for object: ${objectId} to index: ${toIndex}`);

    switch (operation) {
      case 'bringForward':
        this.canvas.bringObjectForward(obj);
        break;
      case 'sendBackwards':
        this.canvas.sendObjectBackwards(obj);
        break;
      case 'bringToFront':
        this.canvas.bringObjectToFront(obj);
        break;
      case 'sendToBack':
        this.canvas.sendObjectToBack(obj);
        break;
      case 'moveToIndex':
        // Move object to specific index
        this.canvas.moveTo(obj, toIndex);
        break;
    }
  }

  clearHistory() {
    this.history = [];
    this.currentIndex = -1;
    this.objectStates.clear();
  }

  saveInitialState() {
    // Save the initial empty state
    this.addHistoryEntry('initial', null, null, null);
  }

  // Methods to control tracking
  disableTracking() {
    this.isTrackingDisabled = true;
  }

  enableTracking() {
    this.isTrackingDisabled = false;
  }

  // Execute a function with tracking disabled
  withoutTracking(fn) {
    const wasDisabled = this.isTrackingDisabled;
    this.disableTracking();
    try {
      return fn();
    } finally {
      if (!wasDisabled) {
        this.enableTracking();
      }
    }
  }

  // Debug method to inspect history state
  debugHistory() {
    console.log('=== History Debug Info ===');
    console.log(`Current Index: ${this.currentIndex}`);
    console.log(`History Length: ${this.history.length}`);
    console.log(`Can Undo: ${this.canUndo()}`);
    console.log(`Can Redo: ${this.canRedo()}`);
    console.log(`Is Undo/Redo in Progress: ${this.isUndoRedoInProgress}`);
    console.log(`Is Tracking Disabled: ${this.isTrackingDisabled}`);
    console.log('History entries:');
    this.history.forEach((entry, index) => {
      const marker = index === this.currentIndex ? ' <-- CURRENT' : '';
      console.log(`  ${index}: ${entry.operation} (${entry.objectId})${marker}`);
    });
    console.log('========================');
  }

  // Drawing session callbacks
  onDrawingSessionStart(session) {
    console.log(`🎨 Drawing session started: ${session.id}`);
    // Notify store or UI about session start
    if (this.onStateChange) {
      this.onStateChange();
    }
  }

  onDrawingSessionEnd(session) {
    console.log(`🏁 Drawing session ended: ${session.id} (${session.paths.length} paths)`);
    // Notify store or UI about session end
    if (this.onStateChange) {
      this.onStateChange();
    }
  }

  onPathAddedToSession(pathData, session) {
    console.log(`➕ Path ${pathData.id} added to session ${session.id}`);
  }

  // Handle tool changes to end drawing sessions
  onToolChange(newTool) {
    if (this.drawingSessionManager) {
      this.drawingSessionManager.onToolChange(newTool);
    }
  }

  // Handle drawing mode changes
  onDrawingModeChange(isDrawing) {
    if (this.drawingSessionManager) {
      this.drawingSessionManager.onDrawingModeChange(isDrawing);
    }
  }

  // Get drawing session manager
  getDrawingSessionManager() {
    return this.drawingSessionManager;
  }

  destroy() {
    if (this.canvas) {
      this.canvas.off('object:moving');
      this.canvas.off('object:scaling');
      this.canvas.off('object:rotating');
      this.canvas.off('object:modified');
      this.canvas.off('object:added');
      this.canvas.off('object:removed');
      this.canvas.off('path:created');
      this.canvas.off('text:editing:entered');
      this.canvas.off('text:editing:exited');

      // Note: We don't restore original canvas methods here as they might be used elsewhere
      // The wrapped methods will be garbage collected with the history manager
    }

    // Destroy drawing session manager
    if (this.drawingSessionManager) {
      this.drawingSessionManager.destroy();
    }

    this.clearHistory();
  }
}

// Restore canvas state while preserving design area
export const restoreCanvasState = async (canvas, canvasState) => {
  if (!canvas || !canvasState || !canvasState.isCanvasState) return false;

  return new Promise(async (resolve, reject) => {
    try {
      // Remove only user objects, keep design area elements
      const userObjects = getUserObjects(canvas);
      userObjects.forEach(obj => canvas.remove(obj));

      // Restore user objects from state
      if (canvasState.objects && canvasState.objects.length > 0) {
        const fabric = await getFabric();
        fabric.util.enlivenObjects(canvasState.objects, (objects) => {
          objects.forEach(obj => {
            canvas.add(obj);
          });

          // Ensure design area elements stay at the bottom
          const designAreaElements = canvas.getObjects().filter(obj =>
            obj.isDesignArea ||
            obj.isDesignAreaElement ||
            obj.id === 'design-area' ||
            obj.id?.startsWith('corner-indicator')
          );

          designAreaElements.forEach(element => {
            try {
              if (typeof canvas.sendToBack === 'function') {
                canvas.sendToBack(element);
              } else if (typeof canvas.sendObjectToBack === 'function') {
                canvas.sendObjectToBack(element);
              } else {
                canvas.moveTo(element, 0);
              }
            } catch (e) {
              console.warn("Could not send design area element to back:", e);
            }
          });

          canvas.renderAll();
          resolve(true);
        });
      } else {
        canvas.renderAll();
        resolve(true);
      }
    } catch (error) {
      console.error('Error restoring canvas state:', error);
      canvas.renderAll();
      reject(error);
    }
  });
};

// Restore user objects from history state while preserving design area
export const restoreUserObjectsFromState = async (canvas, historyState) => {
  if (!canvas || !historyState) return Promise.resolve();

  return new Promise(async (resolve, reject) => {
    try {
      // Store design area elements before clearing
      const designAreaElements = canvas.getObjects().filter(obj =>
        obj.isDesignArea ||
        obj.isDesignAreaElement ||
        obj.id === 'design-area' ||
        obj.id?.startsWith('corner-indicator')
      );

      // Remove only user objects, keep design area elements
      const userObjects = getUserObjects(canvas);
      userObjects.forEach(obj => canvas.remove(obj));

      // Load user objects from history
      if (historyState.objects && historyState.objects.length > 0) {
        // Use enlivenObjects to properly restore objects
        const fabric = await getFabric();
        fabric.util.enlivenObjects(historyState.objects, (objects) => {
          objects.forEach(obj => {
            canvas.add(obj);
          });

          // Ensure design area elements stay at the bottom
          designAreaElements.forEach(element => {
            try {
              if (typeof canvas.sendToBack === 'function') {
                canvas.sendToBack(element);
              } else if (typeof canvas.sendObjectToBack === 'function') {
                canvas.sendObjectToBack(element);
              } else {
                canvas.moveTo(element, 0);
              }
            } catch (e) {
              console.warn("Could not send design area element to back:", e);
            }
          });

          canvas.renderAll();
          resolve();
        });
      } else {
        canvas.renderAll();
        resolve();
      }
    } catch (error) {
      console.error('Error restoring user objects:', error);
      canvas.renderAll();
      reject(error);
    }
  });
};

// Get design area bounds
export const getDesignAreaBounds = (canvas) => {
  if (!canvas) return null;

  const designArea = canvas.getObjects().find(obj => obj.id === 'design-area');
  if (!designArea) return null;

  return {
    left: designArea.left,
    top: designArea.top,
    width: designArea.width,
    height: designArea.height,
    centerX: designArea.left + designArea.width / 2,
    centerY: designArea.top + designArea.height / 2
  };
};

// Update design area size and background color
export const updateDesignArea = (canvas, width, height, backgroundColor, containerEl = null) => {
  if (!canvas || !canvas.getObjects) return;

  try {
    // Find existing design area
    const designArea = canvas.getObjects().find(obj => obj.id === 'design-area');
    if (!designArea) return;

    // Get the container dimensions (the actual viewport)
    let containerWidth, containerHeight;

    // Try to use the passed container element first
    if (containerEl) {
      containerWidth = containerEl.clientWidth;
      containerHeight = containerEl.clientHeight;
    } else if (canvas.wrapperEl && canvas.wrapperEl.parentElement) {
      const container = canvas.wrapperEl.parentElement;
      containerWidth = container.clientWidth;
      containerHeight = container.clientHeight;
    } else {
      // Fallback to canvas dimensions
      containerWidth = canvas.getWidth();
      containerHeight = canvas.getHeight();
    }

    // Ensure we have valid dimensions
    if (!containerWidth || !containerHeight) return;

    // Calculate center position for the new size
    const centerX = containerWidth / 2;
    const centerY = containerHeight / 2;

    // Update design area properties
    designArea.set({
      left: centerX - width / 2,
      top: centerY - height / 2,
      width: width,
      height: height,
      fill: backgroundColor
    });

    // Update coordinates and render
    designArea.setCoords();

    // Ensure canvas dimensions match container
    if (canvas.wrapperEl && canvas.wrapperEl.parentElement) {
      const container = canvas.wrapperEl.parentElement;
      canvas.setDimensions({
        width: container.clientWidth,
        height: container.clientHeight
      });
    }

    canvas.renderAll();

    // Update corner indicators positions
    updateCornerIndicators(canvas, centerX - width / 2, centerY - height / 2, width, height);

    // Ensure design area stays at the bottom
    try {
      if (typeof canvas.sendToBack === 'function') {
        canvas.sendToBack(designArea);
      } else if (typeof canvas.sendObjectToBack === 'function') {
        canvas.sendObjectToBack(designArea);
      } else {
        canvas.moveTo(designArea, 0);
      }
    } catch (e) {
      console.warn("Could not send design area to back:", e);
    }

    const cornerIndicators = canvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));
    cornerIndicators.forEach(indicator => {
      try {
        if (typeof canvas.sendToBack === 'function') {
          canvas.sendToBack(indicator);
        } else if (typeof canvas.sendObjectToBack === 'function') {
          canvas.sendObjectToBack(indicator);
        } else {
          canvas.moveTo(indicator, 0);
        }
      } catch (e) {
        console.warn("Could not send corner indicator to back:", e);
      }
    });

    canvas.renderAll();
  } catch (e) {
    console.error("Failed to update design area:", e);
  }
};

// Update corner indicators positions
export const updateCornerIndicators = (canvas, left, top, width, height) => {
  if (!canvas) return;

  try {
    const cornerSize = 12;
    const cornerThickness = 3;

    // Find all corner indicators
    const corners = {
      'corner-indicator-tl-h': { left: left - cornerThickness, top: top - cornerThickness, width: cornerSize, height: cornerThickness },
      'corner-indicator-tl-v': { left: left - cornerThickness, top: top - cornerThickness, width: cornerThickness, height: cornerSize },
      'corner-indicator-tr-h': { left: left + width - cornerSize + cornerThickness, top: top - cornerThickness, width: cornerSize, height: cornerThickness },
      'corner-indicator-tr-v': { left: left + width, top: top - cornerThickness, width: cornerThickness, height: cornerSize },
      'corner-indicator-bl-h': { left: left - cornerThickness, top: top + height, width: cornerSize, height: cornerThickness },
      'corner-indicator-bl-v': { left: left - cornerThickness, top: top + height - cornerSize + cornerThickness, width: cornerThickness, height: cornerSize },
      'corner-indicator-br-h': { left: left + width - cornerSize + cornerThickness, top: top + height, width: cornerSize, height: cornerThickness },
      'corner-indicator-br-v': { left: left + width, top: top + height - cornerSize + cornerThickness, width: cornerThickness, height: cornerSize }
    };

    // Update each corner indicator
    Object.keys(corners).forEach(cornerId => {
      const corner = canvas.getObjects().find(obj => obj.id === cornerId);
      if (corner) {
        corner.set(corners[cornerId]);
      }
    });

    canvas.renderAll();
  } catch (e) {
    console.error("Failed to update corner indicators:", e);
  }
};

// Position object within design area
export const positionObjectInDesignArea = async (canvas, object, position = 'center') => {
  if (!canvas || !object) return;

  const bounds = getDesignAreaBounds(canvas);
  if (!bounds) return;

  switch (position) {
    case 'center':
      object.set({
        left: bounds.centerX,
        top: bounds.centerY
      });
      break;
    case 'top-left':
      object.set({
        left: bounds.left + 20,
        top: bounds.top + 20
      });
      break;
    case 'random':
      const margin = 50;
      object.set({
        left: bounds.left + margin + Math.random() * (bounds.width - 2 * margin),
        top: bounds.top + margin + Math.random() * (bounds.height - 2 * margin)
      });
      break;
    default:
      // Center by default
      object.set({
        left: bounds.centerX,
        top: bounds.centerY
      });
  }

  object.setCoords();

  try {
    // Add a subtle scale animation when positioning in design area
    const fabric = await getFabric();
    const originalScaleX = object.scaleX || 1;
    const originalScaleY = object.scaleY || 1;

    object.set({
      scaleX: originalScaleX * 0.8,
      scaleY: originalScaleY * 0.8
    });

    canvas.renderAll();

    // Animate back to original size
    object.animate({
      scaleX: originalScaleX,
      scaleY: originalScaleY
    }, {
      duration: 200,
      easing: fabric.util.ease.easeOutQuad,
      onChange: canvas.renderAll.bind(canvas)
    });
  } catch (e) {
    // Fallback without animation
    canvas.renderAll();
  }
};

export const exportDesignArea = (canvas) => {
  if (!canvas) return null;

  try {
    // Find the design area object
    const designArea = canvas.getObjects().find(obj => obj.id === 'design-area');
    if (!designArea) return null;

    // Get design area bounds
    const designAreaLeft = designArea.left;
    const designAreaTop = designArea.top;
    const designAreaWidth = designArea.width;
    const designAreaHeight = designArea.height;

    // Temporarily hide the design area border for export
    const originalStroke = designArea.stroke;
    const originalStrokeDashArray = designArea.strokeDashArray;
    designArea.set({
      stroke: 'transparent',
      strokeDashArray: null
    });

    // Export the specific area using Fabric's built-in method
    const dataURL = canvas.toDataURL({
      format: 'png',
      quality: 1,
      left: designAreaLeft,
      top: designAreaTop,
      width: designAreaWidth,
      height: designAreaHeight,
      multiplier: 1
    });

    // Restore design area border
    designArea.set({
      stroke: originalStroke,
      strokeDashArray: originalStrokeDashArray
    });
    canvas.renderAll();

    return dataURL;
  } catch (e) {
    console.error('Failed to export design area:', e);
    return null;
  }
};



export const addShapeToCanvas = async (canvas, shapeType, customProps = {}) => {
  if (!canvas) return null;

  console.log(`Adding shape: ${shapeType}`);

  try {
    const fabricModule = await getFabric();

    // Get design area bounds for positioning
    const bounds = getDesignAreaBounds(canvas);
    const defaultPosition = bounds ? {
      left: bounds.centerX,
      top: bounds.centerY
    } : {
      left: 100,
      top: 100
    };

    console.log(`Creating shape with position:`, defaultPosition);

    const shape = createShape(fabricModule, shapeType, shapeDefinitions, {
      ...defaultPosition,
      ...customProps,
    });

    if (shape) {
      shape.id = `${shapeType}-${Date.now()}`;

      console.log(`Adding shape to canvas: ${shape.id}`);
      canvas.add(shape);

      // Position the shape in the design area if bounds are available
      if (bounds) {
        positionObjectInDesignArea(canvas, shape, 'center');
      }

      canvas.setActiveObject(shape);
      canvas.renderAll();

      console.log(`Shape added successfully: ${shape.id}`);
      return shape;
    } else {
      console.error(`Failed to create shape: ${shapeType}`);
    }
  } catch (e) {
    console.error("Error adding shape:", e);
  }
  return null;
};

export const addTextToCanvas = async (
  canvas,
  text,
  options = {},
  withBackground = false
) => {
  if (!canvas) return null;

  console.log(`Adding text: "${text}"`);

  try {
    const { IText } = await getFabric();

    // Get design area bounds for positioning
    const bounds = getDesignAreaBounds(canvas);
    const defaultPosition = bounds ? {
      left: bounds.centerX,
      top: bounds.centerY
    } : {
      left: 100,
      top: 100
    };

    const defaultProps = {
      ...defaultPosition,
      fontSize: 24,
      fontFamily: "Arial",
      fill: "#000000",
      padding: withBackground ? 10 : 0,
      textAlign: "left",
      id: `text-${Date.now()}`,
    };

    console.log(`Creating text object with props:`, defaultProps);

    const textObj = new IText(text, {
      ...defaultProps,
      ...options,
    });

    console.log(`Adding text to canvas: ${textObj.id}`);
    canvas.add(textObj);

    // Position the text in the design area if bounds are available
    if (bounds) {
      positionObjectInDesignArea(canvas, textObj, 'center');
    }

    canvas.setActiveObject(textObj);
    canvas.renderAll();

    console.log(`Text added successfully: ${textObj.id}`);
    return textObj;
  } catch (e) {
    console.error("Error adding text:", e);
    return null;
  }
};

export const addImageToCanvas = async (canvas, imageUrl) => {
  if (!canvas) return null;

  try {
    const fabric = await getFabric();

    // Use FabricImage.fromURL directly without double loading
    const image = await fabric.FabricImage.fromURL(imageUrl, {
      crossOrigin: 'anonymous'
    });

    // Get design area bounds for positioning
    const bounds = getDesignAreaBounds(canvas);
    const defaultPosition = bounds ? {
      left: bounds.centerX,
      top: bounds.centerY
    } : {
      left: 100,
      top: 100
    };

    // Set image properties
    image.set({
      id: `image-${Date.now()}`,
      ...defaultPosition,
      padding: 10,
      cornerSize: 10,
      // Ensure clean rendering
      imageSmoothing: true,
      // Prevent artifacts during scaling
      strokeWidth: 0,
      stroke: null,
    });

    // Scale image if too large
    const maxDimension = 400;
    if (image.width > maxDimension || image.height > maxDimension) {
      const scale = Math.min(maxDimension / image.width, maxDimension / image.height);
      image.scale(scale);
    }

    // Add to canvas
    canvas.add(image);

    // Position the image in the design area if bounds are available
    if (bounds) {
      positionObjectInDesignArea(canvas, image, 'center');
    }

    canvas.setActiveObject(image);
    canvas.renderAll();

    console.log(`Image added successfully: ${image.id}`);
    return image;

  } catch (error) {
    console.error("Error adding image:", error);
    return null;
  }
};

export const toggleDrawingMode = (
  canvas,
  isDrawingMode,
  drawingColor = "#000000",
  brushWidth = 5
) => {
  if (!canvas) return null;

  try {
    canvas.isDrawingMode = isDrawingMode;

    if (isDrawingMode) {
      // Enable drawing mode
      canvas.freeDrawingBrush.color = drawingColor;
      canvas.freeDrawingBrush.width = brushWidth;
      // Disable object selection when drawing
      canvas.selection = false;
      // Deselect any currently selected objects
      canvas.discardActiveObject();
      console.log('Drawing mode enabled');
    } else {
      // Disable drawing mode
      // Re-enable object selection when not drawing
      canvas.selection = true;
      console.log('Drawing mode disabled');
    }

    canvas.renderAll();
    return true;
  } catch (e) {
    console.error('Error toggling drawing mode:', e);
    return false;
  }
};

export const toggleEraseMode = async (
  canvas,
  isErasing,
  previousColor = "#000000",
  eraserWidth = 20
) => {
  if (!canvas) return false;

  try {
    if (isErasing) {
      // Import EraserBrush from fabric
      const { EraserBrush } = await import("fabric");

      // Create and set eraser brush
      const eraserBrush = new EraserBrush(canvas);
      eraserBrush.width = eraserWidth;
      eraserBrush.color = "#000000"; // Color doesn't matter for eraser

      canvas.freeDrawingBrush = eraserBrush;
      canvas.isDrawingMode = true;

      console.log('Eraser mode enabled with proper EraserBrush');
    } else {
      // Import PencilBrush from fabric
      const { PencilBrush } = await import("fabric");

      // Restore normal pencil brush
      const pencilBrush = new PencilBrush(canvas);
      pencilBrush.color = previousColor;
      pencilBrush.width = 5;

      canvas.freeDrawingBrush = pencilBrush;

      console.log('Eraser mode disabled, restored pencil brush');
    }

    canvas.renderAll();
    return true;
  } catch (e) {
    console.error('Error toggling eraser mode:', e);
    return false;
  }
};

export const updateDrawingBrush = (canvas, properties = {}) => {
  if (!canvas || !canvas.freeDrawingBrush) return false;

  try {
    const { color, width, opacity } = properties;
    if (color !== undefined) {
      canvas.freeDrawingBrush.color = color;
    }

    if (width !== undefined) {
      canvas.freeDrawingBrush.width = width;
    }

    if (opacity !== undefined) {
      canvas.freeDrawingBrush.opacity = opacity;
    }

    return true;
  } catch (e) {
    return false;
  }
};

export const cloneSelectedObject = async (canvas) => {
  if (!canvas) return;

  const activeObject = canvas.getActiveObject();
  if (!activeObject) return;

  // Prevent cloning of design area elements
  if (activeObject.id === 'design-area' ||
      activeObject.isDesignArea ||
      activeObject.isDesignAreaElement ||
      activeObject.isCornerIndicator) {
    console.warn("Cannot clone design area elements");
    return null;
  }

  try {
    const clonedObj = await activeObject.clone();

    clonedObj.set({
      left: activeObject.left + 10,
      top: activeObject.top + 10,
      id: `${activeObject.type || "object"}-${Date.now()}`,
    });

    canvas.add(clonedObj);
    canvas.renderAll();

    return clonedObj;
  } catch (e) {
    console.error("Error while cloning", e);

    return null;
  }
};

export const deletedSelectedObject = async (canvas) => {
  if (!canvas) return;

  const activeObject = canvas.getActiveObject();

  if (!activeObject) return;

  // Prevent deletion of design area elements
  if (activeObject.id === 'design-area' ||
      activeObject.isDesignArea ||
      activeObject.isDesignAreaElement ||
      activeObject.isCornerIndicator) {
    console.warn("Cannot delete design area elements");
    return false;
  }

  try {
    canvas.remove(activeObject);
    canvas.discardActiveObject();
    canvas.renderAll();

    return true;
  } catch (e) {
    console.error("Error while deleting", e);
    return false;
  }
};



export const customizeBoundingBox = (canvas) => {
  if (!canvas) return;

  try {
    canvas.on("object:added", (e) => {
      if (e.target) {
        e.target.set({
          borderColor: "#00ffe7",
          cornerColor: "#000000",
          cornerStrokeColor: "#00ffe7",
          cornerSize: 10,
          transparentCorners: false,
        });
      }
    });

    canvas.getObjects().forEach((obj) => {
      obj.set({
        borderColor: "#00ffe7",
        cornerColor: "#000000",
        cornerStrokeColor: "#00ffe7",
        cornerSize: 10,
        transparentCorners: false,
      });
    });

    canvas.renderAll();
  } catch (e) {
    console.error("Failed to customise bounding box", e);
  }
};

export const togglePanMode = (canvas, isPanMode) => {
  if (!canvas) {
    console.error("Canvas not provided to togglePanMode");
    return false;
  }

  try {
    if (isPanMode) {
      // Enable pan mode
      canvas.selection = false; // Disable object selection
      canvas.isDrawingMode = false; // Disable drawing mode
      canvas.discardActiveObject(); // Deselect any objects

      // Make objects non-selectable and non-movable
      canvas.forEachObject((obj) => {
        // Don't affect design area elements
        if (!obj.isDesignArea && !obj.isDesignAreaElement && !obj.isCornerIndicator) {
          obj.selectable = false;
          obj.evented = false;
        }
      });

      // Change cursor to indicate pan mode
      canvas.defaultCursor = 'grab';
      canvas.hoverCursor = 'grab';
      canvas.moveCursor = 'grabbing';

      console.log('Pan mode enabled');
    } else {
      // Disable pan mode
      canvas.selection = true; // Re-enable object selection

      // Make objects selectable and movable again
      canvas.forEachObject((obj) => {
        // Don't affect design area elements
        if (!obj.isDesignArea && !obj.isDesignAreaElement && !obj.isCornerIndicator) {
          obj.selectable = true;
          obj.evented = true;
        }
      });

      // Reset cursors
      canvas.defaultCursor = 'default';
      canvas.hoverCursor = 'move';
      canvas.moveCursor = 'move';

      console.log('Pan mode disabled');
    }

    canvas.renderAll();
    return true;
  } catch (e) {
    console.error('Error toggling pan mode:', e);
    return false;
  }
};

// Enhanced responsive canvas utilities
export const maintainObjectRelativePositions = (canvas, oldDimensions, newDimensions) => {
  if (!canvas || !oldDimensions || !newDimensions) return;

  const scaleX = newDimensions.width / oldDimensions.width;
  const scaleY = newDimensions.height / oldDimensions.height;

  // Use the smaller scale to maintain aspect ratio
  const scale = Math.min(scaleX, scaleY);

  const objects = canvas.getObjects().filter(obj =>
    obj.id !== 'design-area' &&
    !obj.id?.startsWith('corner-indicator') &&
    !obj.isDesignArea &&
    !obj.isDesignAreaElement
  );

  objects.forEach(obj => {
    const currentLeft = obj.left;
    const currentTop = obj.top;

    // Calculate new position maintaining relative position
    const newLeft = currentLeft * scaleX;
    const newTop = currentTop * scaleY;

    obj.set({
      left: newLeft,
      top: newTop,
      scaleX: obj.scaleX * scale,
      scaleY: obj.scaleY * scale
    });

    obj.setCoords();
  });

  canvas.renderAll();
};

export const getResponsiveCanvasDimensions = (containerWidth, containerHeight, designWidth, designHeight) => {
  const padding = 40; // Minimum padding around design area
  const maxWidth = containerWidth - padding * 2;
  const maxHeight = containerHeight - padding * 2;

  // Calculate scale to fit design area within container
  const scaleX = maxWidth / designWidth;
  const scaleY = maxHeight / designHeight;
  const scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%

  return {
    canvasWidth: containerWidth,
    canvasHeight: containerHeight,
    designWidth: designWidth * scale,
    designHeight: designHeight * scale,
    scale
  };
};

export const ensureObjectsWithinBounds = (canvas, designArea) => {
  if (!canvas || !designArea) return;

  const objects = canvas.getObjects().filter(obj =>
    obj.id !== 'design-area' &&
    !obj.id?.startsWith('corner-indicator') &&
    !obj.isDesignArea &&
    !obj.isDesignAreaElement
  );

  const bounds = {
    left: designArea.left,
    top: designArea.top,
    right: designArea.left + designArea.width,
    bottom: designArea.top + designArea.height
  };

  objects.forEach(obj => {
    const objBounds = obj.getBoundingRect();
    let needsUpdate = false;
    let newLeft = obj.left;
    let newTop = obj.top;

    // Check if object is outside design area bounds
    if (objBounds.left < bounds.left) {
      newLeft = bounds.left + (obj.left - objBounds.left);
      needsUpdate = true;
    }
    if (objBounds.top < bounds.top) {
      newTop = bounds.top + (obj.top - objBounds.top);
      needsUpdate = true;
    }
    if (objBounds.left + objBounds.width > bounds.right) {
      newLeft = bounds.right - objBounds.width + (obj.left - objBounds.left);
      needsUpdate = true;
    }
    if (objBounds.top + objBounds.height > bounds.bottom) {
      newTop = bounds.bottom - objBounds.height + (obj.top - objBounds.top);
      needsUpdate = true;
    }

    if (needsUpdate) {
      obj.set({ left: newLeft, top: newTop });
      obj.setCoords();
    }
  });

  canvas.renderAll();
};

export const handleResponsiveResize = (canvas, containerEl, designWidth, designHeight, backgroundColor) => {
  if (!canvas || !containerEl) return;

  const containerWidth = containerEl.clientWidth;
  const containerHeight = containerEl.clientHeight;

  // Store old canvas dimensions
  const oldCanvasWidth = canvas.getWidth();
  const oldCanvasHeight = canvas.getHeight();

  // Update canvas dimensions to match container
  canvas.setDimensions({
    width: containerWidth,
    height: containerHeight
  });

  // Get responsive dimensions for design area
  const responsiveDims = getResponsiveCanvasDimensions(
    containerWidth,
    containerHeight,
    designWidth,
    designHeight
  );

  // Update design area with new dimensions
  updateDesignArea(canvas, responsiveDims.designWidth, responsiveDims.designHeight, backgroundColor, containerEl);

  // Ensure objects stay within bounds
  const designArea = canvas.getObjects().find(obj => obj.id === 'design-area');
  if (designArea) {
    ensureObjectsWithinBounds(canvas, designArea);
  }

  canvas.renderAll();
};

// Utility function to maintain proper layer ordering
export const maintainLayerOrder = (canvas) => {
  if (!canvas) return;

  const designArea = canvas.getObjects().find(obj => obj.id === 'design-area');
  const cornerIndicators = canvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));

  // Ensure design area is at the very bottom
  if (designArea) {
    try {
      canvas.sendToBack(designArea);
      // Additional check to ensure it's at index 0
      const objects = canvas.getObjects();
      const designAreaIndex = objects.indexOf(designArea);
      if (designAreaIndex > 0) {
        canvas.moveTo && canvas.moveTo(designArea, 0);
      }
    } catch (e) {
      console.warn("Could not maintain design area at back:", e);
    }
  }

  // Ensure corner indicators are also at the bottom (but above design area)
  cornerIndicators.forEach((indicator) => {
    try {
      canvas.sendToBack(indicator);
    } catch (e) {
      console.warn("Could not maintain corner indicator at back:", e);
    }
  });
};

// Setup layer order maintenance for a canvas
export const setupLayerOrderMaintenance = (canvas) => {
  if (!canvas) return;

  // Remove existing listener to avoid duplicates
  canvas.off('object:added', maintainLayerOrder);

  // Add listener to maintain layer order when objects are added
  canvas.on('object:added', () => maintainLayerOrder(canvas));

  // Also maintain order on object modifications
  canvas.on('object:modified', () => maintainLayerOrder(canvas));
};


