"use client";

import { useState } from "react";
import { useEditorStore } from "@/store";
import { aiImageService } from "@/services/aiImageService";
import {
  Scissors,
  ArrowUp,
  Sparkles,
  Palette,
  Volume2,
  Loader2,
  Wand2,
  Zap,
  RefreshCw
} from "lucide-react";

export default function AIImageToolbar({ selectedObject, onClose }) {
  const { canvas, markAsModified } = useEditorStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingOperation, setProcessingOperation] = useState(null);

  if (!selectedObject || selectedObject.type !== "image") {
    return null;
  }

  const aiOperations = [
    {
      id: 'removeBackground',
      label: 'Remove Background',
      icon: Scissors,
      description: 'Remove background using AI',
      color: 'bg-red-500 hover:bg-red-600'
    },
    {
      id: 'upscale',
      label: 'Upscale 2x',
      icon: ArrowUp,
      description: 'Increase image resolution',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      id: 'enhance',
      label: 'Enhance',
      icon: Sparkles,
      description: 'Improve image quality',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      id: 'colorize',
      label: 'Colorize',
      icon: Palette,
      description: 'Add color to B&W images',
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      id: 'denoise',
      label: 'Denoise',
      icon: Volume2,
      description: 'Remove image noise',
      color: 'bg-orange-500 hover:bg-orange-600'
    }
  ];

  const handleAIOperation = async (operation) => {
    if (!selectedObject || !canvas || isProcessing) return;

    setIsProcessing(true);
    setProcessingOperation(operation.id);

    try {
      // Get current image data
      const imageDataUrl = aiImageService.getImageDataFromFabricObject(selectedObject);
      
      let processedImageUrl;
      
      // Apply the selected AI operation
      switch (operation.id) {
        case 'removeBackground':
          processedImageUrl = await aiImageService.removeBackground(imageDataUrl);
          break;
        case 'upscale':
          processedImageUrl = await aiImageService.upscaleImage(imageDataUrl, 2);
          break;
        case 'enhance':
          processedImageUrl = await aiImageService.enhanceImage(imageDataUrl);
          break;
        case 'colorize':
          processedImageUrl = await aiImageService.colorizeImage(imageDataUrl);
          break;
        case 'denoise':
          processedImageUrl = await aiImageService.denoiseImage(imageDataUrl);
          break;
        default:
          throw new Error('Unknown operation');
      }

      // Load the processed image
      const fabric = await import("fabric");
      fabric.Image.fromURL(processedImageUrl, (processedImg) => {
        // Preserve the original object's properties
        const originalProps = {
          left: selectedObject.left,
          top: selectedObject.top,
          scaleX: selectedObject.scaleX,
          scaleY: selectedObject.scaleY,
          angle: selectedObject.angle,
          opacity: selectedObject.opacity,
          flipX: selectedObject.flipX,
          flipY: selectedObject.flipY,
          id: selectedObject.id,
          name: selectedObject.name || `AI ${operation.label} Image`
        };

        // Apply the preserved properties to the new image
        processedImg.set(originalProps);

        // Remove the old image and add the new one
        canvas.remove(selectedObject);
        canvas.add(processedImg);
        canvas.setActiveObject(processedImg);
        canvas.renderAll();
        
        markAsModified();
        
        // Show success message
        console.log(`${operation.label} applied successfully!`);
      }, {
        crossOrigin: 'anonymous'
      });

    } catch (error) {
      console.error(`Error applying ${operation.label}:`, error);
      alert(`Failed to apply ${operation.label}. ${error.message}`);
    } finally {
      setIsProcessing(false);
      setProcessingOperation(null);
    }
  };

  return (
    <div className="absolute top-0 left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 transform -translate-y-full -mt-2">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Wand2 className="w-5 h-5 text-purple-600" />
          <h3 className="text-sm font-semibold text-gray-900">AI Image Tools</h3>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* AI Operations */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
        {aiOperations.map((operation) => {
          const IconComponent = operation.icon;
          const isCurrentlyProcessing = isProcessing && processingOperation === operation.id;
          
          return (
            <button
              key={operation.id}
              onClick={() => handleAIOperation(operation)}
              disabled={isProcessing}
              className={`
                relative flex flex-col items-center gap-2 p-3 rounded-lg text-white transition-all
                ${isProcessing && processingOperation !== operation.id 
                  ? 'opacity-50 cursor-not-allowed' 
                  : operation.color
                }
                ${isCurrentlyProcessing ? 'animate-pulse' : 'hover:scale-105'}
              `}
              title={operation.description}
            >
              {isCurrentlyProcessing ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <IconComponent className="w-5 h-5" />
              )}
              <span className="text-xs font-medium text-center leading-tight">
                {operation.label}
              </span>
              
              {isCurrentlyProcessing && (
                <div className="absolute inset-0 bg-black bg-opacity-20 rounded-lg flex items-center justify-center">
                  <div className="text-xs font-medium">Processing...</div>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
            <span className="text-sm text-blue-800">
              Processing your image with AI... This may take a few moments.
            </span>
          </div>
        </div>
      )}

      {/* Info */}
      <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="flex items-start gap-2">
          <Zap className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
          <div className="text-xs text-gray-600">
            <p className="font-medium mb-1">AI-Powered Image Enhancement</p>
            <p>These tools use artificial intelligence to automatically improve your images. Processing may take a few seconds.</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={() => {
              // Reset all filters
              if (selectedObject && canvas) {
                selectedObject.filters = [];
                selectedObject.applyFilters();
                canvas.renderAll();
                markAsModified();
              }
            }}
            disabled={isProcessing}
            className="h-7 text-xs px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
          >
            <RefreshCw className="w-3 h-3" />
            Reset
          </button>
        </div>
        
        <div className="text-xs text-gray-500">
          {aiOperations.length} AI tools available
        </div>
      </div>
    </div>
  );
}
