# Database Setup Guide

This guide will help you set up PostgreSQL database for the Poster Editor application.

## Prerequisites

1. **PostgreSQL installed and running**
   - Download from: https://www.postgresql.org/download/
   - Make sure PostgreSQL service is running on port 5432

2. **Database created**
   - Create a database named `pix` (or update the DATABASE_URL in .env.local)

## Setup Steps

### 1. Verify Database Connection

First, make sure your PostgreSQL server is running and accessible:

```bash
# Test connection (replace with your credentials)
psql -h localhost -p 5432 -U postgres -d pix
```

### 2. Update Environment Variables

Check your `.env.local` file and ensure the DATABASE_URL is correct:

```env
DATABASE_URL="postgresql://postgres:M@kerere1@localhost:5432/pix"
```

### 3. Generate Prisma Client

```bash
npx prisma generate
```

### 4. Push Database Schema

```bash
npx prisma db push
```

This will create all the necessary tables:
- `users` - User accounts
- `designs` - User designs with canvas data
- `templates` - Reusable templates
- `assets` - Uploaded images and resources
- `design_history` - Version control for designs
- `categories` - Template categories

### 5. (Optional) Seed Database

You can create a seed script to add sample data:

```bash
npx prisma db seed
```

## Database Schema

### Tables Created:

1. **users**
   - id, email, name, image
   - createdAt, updatedAt

2. **designs**
   - id, name, description
   - width, height, canvasData (JSON)
   - thumbnail, isPublic
   - userId (foreign key)
   - createdAt, updatedAt

3. **templates**
   - id, name, description, category
   - width, height, canvasData (JSON)
   - thumbnail, isPublic, isPremium
   - tags, editableZones (JSON)
   - createdById (foreign key)
   - createdAt, updatedAt

4. **assets**
   - id, name, type, url
   - size, width, height, mimeType
   - tags, isPublic
   - createdAt, updatedAt

5. **design_history**
   - id, designId, canvasData (JSON)
   - version, comment
   - createdAt

6. **categories**
   - id, name, description
   - color, icon
   - createdAt, updatedAt

## Troubleshooting

### Connection Issues

1. **Port 5432 not accessible**
   - Check if PostgreSQL is running: `sudo service postgresql status`
   - Check if port is correct in DATABASE_URL

2. **Authentication failed**
   - Verify username and password in DATABASE_URL
   - Check PostgreSQL user permissions

3. **Database doesn't exist**
   - Create database: `createdb pix`
   - Or use pgAdmin to create the database

### Common Commands

```bash
# Check Prisma status
npx prisma status

# Reset database (WARNING: This will delete all data)
npx prisma db push --force-reset

# View database in browser
npx prisma studio
```

## Using the Database

Once set up, you can:

1. **Save designs** - Click "Save to DB" button in the editor
2. **Load designs** - Use the API endpoints to load saved designs
3. **Create templates** - Save designs as public templates
4. **Manage assets** - Upload and organize images

## API Endpoints

- `GET /api/designs` - Get user designs or public designs
- `POST /api/designs` - Create new design
- `GET /api/designs/[id]` - Get specific design
- `PUT /api/designs/[id]` - Update design
- `DELETE /api/designs/[id]` - Delete design

## Next Steps

1. Set up user authentication (NextAuth.js)
2. Implement file upload for assets
3. Add template management interface
4. Create design gallery/browse feature
