// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and user management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  image     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  designs   Design[]
  templates Template[]

  @@map("users")
}

// Design model for saving user designs
model Design {
  id          String   @id @default(cuid())
  name        String
  description String?
  width       Int      @default(800)
  height      Int      @default(600)
  canvasData  Json     // Fabric.js canvas JSON data
  thumbnail   String?  // Base64 or URL to thumbnail image
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("designs")
}

// Template model for reusable templates
model Template {
  id            String   @id @default(cuid())
  name          String
  description   String?
  category      String   @default("general")
  width         Int      @default(800)
  height        Int      @default(600)
  canvasData    Json     // Fabric.js canvas JSON data
  thumbnail     String?  // Base64 or URL to thumbnail image
  isPublic      Boolean  @default(true)
  isPremium     Boolean  @default(false)
  tags          String[] // Array of tags for searching
  editableZones Json?    // Define which parts users can edit
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  createdById String
  createdBy   User   @relation(fields: [createdById], references: [id], onDelete: Cascade)

  @@map("templates")
}

// Asset model for managing uploaded images and resources
model Asset {
  id        String    @id @default(cuid())
  name      String
  type      AssetType
  url       String    // URL to the asset
  size      Int?      // File size in bytes
  width     Int?      // Image width
  height    Int?      // Image height
  mimeType  String?   // MIME type
  tags      String[]  // Array of tags for organization
  isPublic  Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@map("assets")
}

// Enum for asset types
enum AssetType {
  IMAGE
  ICON
  FONT
  SHAPE
  BACKGROUND
}

// Design history for version control
model DesignHistory {
  id         String   @id @default(cuid())
  designId   String
  canvasData Json     // Fabric.js canvas JSON data at this point
  version    Int      // Version number
  comment    String?  // Optional comment about changes
  createdAt  DateTime @default(now())

  @@map("design_history")
}

// Categories for organizing templates
model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  color       String?  // Hex color for UI
  icon        String?  // Icon name or URL
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("categories")
}
