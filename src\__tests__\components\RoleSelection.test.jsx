/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import RoleSelection from '../../components/RoleSelection';
import { useTemplateStore } from '../../store/templateStore';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}));

// Mock template store
jest.mock('../../store/templateStore', () => ({
  useTemplateStore: jest.fn()
}));

describe('RoleSelection Component', () => {
  const mockPush = jest.fn();
  const mockSetUserRole = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    useRouter.mockReturnValue({
      push: mockPush
    });

    useTemplateStore.mockReturnValue({
      setUserRole: mockSetUserRole
    });
  });

  test('renders role selection interface', () => {
    render(<RoleSelection />);

    expect(screen.getByText('Poster Editor')).toBeInTheDocument();
    expect(screen.getByText('Template Creator')).toBeInTheDocument();
    expect(screen.getByText('Template User')).toBeInTheDocument();
    expect(screen.getByText('Admin Mode')).toBeInTheDocument();
    expect(screen.getByText('User Mode')).toBeInTheDocument();
  });

  test('displays role features correctly', () => {
    render(<RoleSelection />);

    // Check admin features
    expect(screen.getByText('Full canvas editor access')).toBeInTheDocument();
    expect(screen.getByText('Create template layouts')).toBeInTheDocument();
    expect(screen.getByText('Define editable zones')).toBeInTheDocument();

    // Check user features
    expect(screen.getByText('Browse template gallery')).toBeInTheDocument();
    expect(screen.getByText('Customize editable areas')).toBeInTheDocument();
    expect(screen.getByText('Export final designs')).toBeInTheDocument();
  });

  test('navigates to admin dashboard when admin role is selected', async () => {
    render(<RoleSelection />);

    const adminCard = screen.getByText('Template Creator').closest('div');
    fireEvent.click(adminCard);

    await waitFor(() => {
      expect(mockSetUserRole).toHaveBeenCalledWith('admin');
      expect(mockPush).toHaveBeenCalledWith('/admin');
    });
  });

  test('navigates to template gallery when user role is selected', async () => {
    render(<RoleSelection />);

    const userCard = screen.getByText('Template User').closest('div');
    fireEvent.click(userCard);

    await waitFor(() => {
      expect(mockSetUserRole).toHaveBeenCalledWith('user');
      expect(mockPush).toHaveBeenCalledWith('/templates');
    });
  });

  test('navigates to free editor when skip button is clicked', async () => {
    render(<RoleSelection />);

    const skipButton = screen.getByText('Skip to Free Editor');
    fireEvent.click(skipButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/editor');
    });
  });

  test('displays role descriptions correctly', () => {
    render(<RoleSelection />);

    expect(screen.getByText(/Create and manage poster templates/)).toBeInTheDocument();
    expect(screen.getByText(/Select and customize existing templates/)).toBeInTheDocument();
  });

  test('shows role switching information', () => {
    render(<RoleSelection />);

    expect(screen.getByText('You can switch roles anytime')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(<RoleSelection />);

    const adminCard = screen.getByText('Template Creator').closest('div');
    const userCard = screen.getByText('Template User').closest('div');

    expect(adminCard).toHaveClass('cursor-pointer');
    expect(userCard).toHaveClass('cursor-pointer');
  });

  test('displays icons correctly', () => {
    render(<RoleSelection />);

    // Check for icon containers (we can't easily test the actual icons)
    const iconContainers = screen.getAllByRole('generic').filter(
      el => el.className.includes('bg-gradient-to-r')
    );
    
    expect(iconContainers.length).toBeGreaterThan(0);
  });

  test('handles hover effects', () => {
    render(<RoleSelection />);

    const adminCard = screen.getByText('Template Creator').closest('div');
    
    expect(adminCard).toHaveClass('hover:scale-105');
    expect(adminCard).toHaveClass('transition-all');
  });

  test('displays get started buttons', () => {
    render(<RoleSelection />);

    const getStartedButtons = screen.getAllByText('Get Started');
    expect(getStartedButtons).toHaveLength(2);
  });
});
