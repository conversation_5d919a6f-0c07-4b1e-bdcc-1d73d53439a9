"use client";

import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);

    this.setState({
      errorInfo: errorInfo
    });

    // Auto-retry for chunk loading errors
    if (this.isChunkLoadError(error) && this.state.retryCount < 3) {
      setTimeout(() => {
        this.handleRetry();
      }, 1000 * (this.state.retryCount + 1));
    }
  }

  isChunkLoadError = (error) => {
    return error.name === 'ChunkLoadError' ||
           error.message?.includes('Loading chunk') ||
           error.message?.includes('ChunkLoadError');
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  }

  render() {
    if (this.state.hasError) {
      const isChunkError = this.isChunkLoadError(this.state.error);

      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-50">
          <div className="text-center p-8 max-w-md">
            <div className="text-red-500 text-6xl mb-4">
              {isChunkError ? '🔄' : '⚠️'}
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {isChunkError ? 'Loading Error' : 'Something went wrong'}
            </h2>
            <p className="text-gray-600 mb-6">
              {isChunkError
                ? 'There was an issue loading the application. This usually resolves with a refresh.'
                : 'The poster editor encountered an error. Please refresh the page to try again.'
              }
            </p>

            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>

              <button
                onClick={() => window.location.reload()}
                className="w-full bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Refresh Page
              </button>
            </div>

            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-gray-500">
                  Error Details (Development)
                </summary>
                <div className="mt-2 text-xs bg-red-50 p-2 rounded overflow-auto max-h-40">
                  <div className="text-red-600 mb-2">
                    <strong>Error:</strong> {this.state.error?.toString()}
                  </div>
                  {this.state.errorInfo && (
                    <div className="text-gray-600">
                      <strong>Component Stack:</strong>
                      <pre className="whitespace-pre-wrap text-xs">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
