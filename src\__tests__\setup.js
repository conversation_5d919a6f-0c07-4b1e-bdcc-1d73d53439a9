/**
 * Test setup file for Jest
 * Configures global mocks and test utilities
 */

import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn()
  })),
  usePathname: jest.fn(() => '/'),
  useSearchParams: jest.fn(() => new URLSearchParams())
}));

// Mock Next.js dynamic imports
jest.mock('next/dynamic', () => (fn) => {
  const DynamicComponent = (props) => {
    const Component = fn();
    return <Component {...props} />;
  };
  DynamicComponent.displayName = 'DynamicComponent';
  return DynamicComponent;
});

// Mock Fabric.js
jest.mock('fabric', () => ({
  fabric: {
    Canvas: jest.fn(() => ({
      add: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      renderAll: jest.fn(),
      setActiveObject: jest.fn(),
      getActiveObject: jest.fn(),
      discardActiveObject: jest.fn(),
      getObjects: jest.fn(() => []),
      on: jest.fn(),
      off: jest.fn(),
      setDimensions: jest.fn(),
      toJSON: jest.fn(() => ({ objects: [] })),
      loadFromJSON: jest.fn(),
      bringObjectForward: jest.fn(),
      sendObjectBackwards: jest.fn(),
      sendObjectToBack: jest.fn(),
      bringObjectToFront: jest.fn(),
      selection: true,
      defaultCursor: 'default',
      hoverCursor: 'move',
      moveCursor: 'move'
    })),
    Object: jest.fn(() => ({
      set: jest.fn(),
      get: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
      getBoundingRect: jest.fn(() => ({ left: 0, top: 0, width: 100, height: 100 })),
      setCoords: jest.fn(),
      animate: jest.fn()
    })),
    Text: jest.fn(),
    Textbox: jest.fn(),
    Image: {
      fromURL: jest.fn((url, callback) => {
        const mockImage = {
          set: jest.fn(),
          get: jest.fn(),
          on: jest.fn(),
          off: jest.fn(),
          getBoundingRect: jest.fn(() => ({ left: 0, top: 0, width: 100, height: 100 })),
          setCoords: jest.fn()
        };
        callback && callback(mockImage);
      }),
      filters: {
        Brightness: jest.fn(),
        Contrast: jest.fn(),
        Saturation: jest.fn(),
        HueRotation: jest.fn(),
        Blur: jest.fn(),
        Sepia: jest.fn(),
        Grayscale: jest.fn()
      }
    },
    Rect: jest.fn(),
    Circle: jest.fn(),
    Triangle: jest.fn(),
    Ellipse: jest.fn(),
    ActiveSelection: jest.fn(),
    util: {
      enlivenObjects: jest.fn((objects, callback) => {
        callback && callback(objects);
      })
    }
  }
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// Mock FileReader
global.FileReader = jest.fn(() => ({
  readAsDataURL: jest.fn(),
  readAsText: jest.fn(),
  onload: null,
  onerror: null,
  result: 'data:image/png;base64,mockdata'
}));

// Mock HTMLCanvasElement
HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
  fillRect: jest.fn(),
  clearRect: jest.fn(),
  getImageData: jest.fn(() => ({
    data: new Uint8ClampedArray(400),
    width: 10,
    height: 10
  })),
  putImageData: jest.fn(),
  createImageData: jest.fn(() => ({
    data: new Uint8ClampedArray(400),
    width: 10,
    height: 10
  })),
  setTransform: jest.fn(),
  drawImage: jest.fn(),
  save: jest.fn(),
  restore: jest.fn(),
  beginPath: jest.fn(),
  moveTo: jest.fn(),
  lineTo: jest.fn(),
  closePath: jest.fn(),
  stroke: jest.fn(),
  fill: jest.fn(),
  measureText: jest.fn(() => ({ width: 100 })),
  imageSmoothingEnabled: true,
  imageSmoothingQuality: 'high'
}));

HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/png;base64,mockdata');
HTMLCanvasElement.prototype.toBlob = jest.fn((callback) => {
  callback(new Blob(['mock'], { type: 'image/png' }));
});

// Mock Image constructor
global.Image = class {
  constructor() {
    this.onload = null;
    this.onerror = null;
    this.src = '';
    this.width = 100;
    this.height = 100;
  }
  
  set src(value) {
    this._src = value;
    // Simulate successful image load
    setTimeout(() => {
      if (this.onload) this.onload();
    }, 0);
  }
  
  get src() {
    return this._src;
  }
};

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
  log: jest.fn(),
  info: jest.fn()
};

// Mock fetch for API calls
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob())
  })
);

// Setup test environment
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
  
  // Reset localStorage
  localStorageMock.getItem.mockReturnValue(null);
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  
  // Reset sessionStorage
  sessionStorageMock.getItem.mockReturnValue(null);
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
  
  // Reset fetch
  global.fetch.mockClear();
});

// Cleanup after each test
afterEach(() => {
  // Clean up any timers
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});
