{"name": "nextjs-poster-editor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@fal-ai/client": "^1.6.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@xenova/transformers": "^2.17.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fabric": "^6.6.2", "lucide-react": "^0.486.0", "next": "15.2.4", "pg": "^8.16.3", "prisma": "^6.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "replicate": "^1.0.1", "tailwind-merge": "^3.1.0", "together-ai": "^0.20.0", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "autoprefixer": "^10.4.16", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7"}}