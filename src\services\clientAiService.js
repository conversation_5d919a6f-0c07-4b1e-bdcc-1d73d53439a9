// Client-side AI operations using Transformers.js
// This provides fallback functionality when API keys are not available

let transformersLoaded = false;
let pipeline = null;

// Initialize the background removal pipeline
async function initializeBackgroundRemoval() {
  if (transformersLoaded && pipeline) {
    return pipeline;
  }

  try {
    // Dynamic import to avoid SSR issues
    const { pipeline: createPipeline } = await import('@xenova/transformers');
    
    // Initialize the image segmentation pipeline
    pipeline = await createPipeline('image-segmentation', 'Xenova/modnet');
    transformersLoaded = true;
    
    console.log('Client-side background removal initialized');
    return pipeline;
  } catch (error) {
    console.error('Failed to initialize client-side background removal:', error);
    throw new Error('Failed to load background removal model');
  }
}

// Remove background from image using client-side AI
export async function removeBackgroundClientSide(imageUrl) {
  try {
    console.log('Starting client-side background removal for:', imageUrl);

    // Initialize the pipeline if not already done
    const segmentationPipeline = await initializeBackgroundRemoval();

    // Create an image element to load the image
    const img = new Image();
    img.crossOrigin = 'anonymous';

    return new Promise((resolve, reject) => {
      img.onload = async () => {
        try {
          // Create a canvas to process the image
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          canvas.width = img.width;
          canvas.height = img.height;

          // Draw the image on canvas
          ctx.drawImage(img, 0, 0);

          // Get image data
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

          // Run segmentation
          console.log('Running background segmentation...');
          const result = await segmentationPipeline(canvas);

          // Process the segmentation result
          if (result && result.length > 0) {
            // Find the person/object mask (usually the first result)
            const mask = result[0];

            // Create a new canvas for the result
            const resultCanvas = document.createElement('canvas');
            const resultCtx = resultCanvas.getContext('2d');
            resultCanvas.width = canvas.width;
            resultCanvas.height = canvas.height;

            // Draw the original image
            resultCtx.drawImage(img, 0, 0);

            // Apply the mask to remove background
            const resultImageData = resultCtx.getImageData(0, 0, resultCanvas.width, resultCanvas.height);
            const maskImageData = mask.mask;

            // Apply transparency based on mask
            for (let i = 0; i < resultImageData.data.length; i += 4) {
              const pixelIndex = i / 4;
              const maskValue = maskImageData[pixelIndex];

              // If mask value is low, make pixel transparent
              if (maskValue < 128) {
                resultImageData.data[i + 3] = 0; // Set alpha to 0 (transparent)
              }
            }

            resultCtx.putImageData(resultImageData, 0, 0);

            // Convert to blob and return URL
            resultCanvas.toBlob((blob) => {
              const url = URL.createObjectURL(blob);
              console.log('Client-side background removal completed');
              resolve(url);
            }, 'image/png');
          } else {
            reject(new Error('No segmentation result'));
          }
        } catch (error) {
          console.error('Error during segmentation:', error);
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = imageUrl;
    });
  } catch (error) {
    console.error('Client-side background removal failed:', error);
    throw error;
  }
}

// Enhanced background removal with dimension preservation
export async function removeBackgroundClientSideWithDimensions(imageUrl, targetDimensions) {
  try {
    console.log('Starting dimension-preserving background removal for:', imageUrl);
    console.log('Target dimensions:', targetDimensions);

    // Create an image element to load the image
    const img = new Image();
    img.crossOrigin = 'anonymous';

    return new Promise((resolve, reject) => {
      img.onload = async () => {
        try {
          // Create a canvas with the exact target dimensions
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          canvas.width = targetDimensions.width;
          canvas.height = targetDimensions.height;

          // Draw the image scaled to fit the target dimensions
          ctx.drawImage(img, 0, 0, targetDimensions.width, targetDimensions.height);

          // Use a simpler approach for background removal that preserves dimensions
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // Simple edge-based background removal
          // This is a basic implementation - you can enhance it with more sophisticated algorithms
          const threshold = 30; // Adjust this value for better results

          for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // Calculate if pixel is likely background (edges of image or similar colors)
            const x = (i / 4) % canvas.width;
            const y = Math.floor((i / 4) / canvas.width);

            // Check if pixel is near edges
            const nearEdge = x < threshold || x > canvas.width - threshold ||
                           y < threshold || y > canvas.height - threshold;

            // Simple color-based background detection (you can improve this)
            const isLikelyBackground = nearEdge ||
              (Math.abs(r - g) < 20 && Math.abs(g - b) < 20 && Math.abs(r - b) < 20 &&
               (r + g + b) / 3 > 200); // Light colored backgrounds

            if (isLikelyBackground) {
              data[i + 3] = 0; // Make transparent
            }
          }

          // Apply the modified image data
          ctx.putImageData(imageData, 0, 0);

          // Convert to blob and return URL
          canvas.toBlob((blob) => {
            const url = URL.createObjectURL(blob);
            console.log('Dimension-preserving background removal completed');
            console.log('Output dimensions:', canvas.width, 'x', canvas.height);
            resolve(url);
          }, 'image/png');

        } catch (error) {
          console.error('Error during dimension-preserving background removal:', error);
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = imageUrl;
    });
  } catch (error) {
    console.error('Dimension-preserving background removal failed:', error);
    throw error;
  }
}

// Check if client-side background removal is available
export function isClientSideBackgroundRemovalAvailable() {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return false;
  }
  
  // Check if WebGL is available (required for Transformers.js)
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  } catch (error) {
    return false;
  }
}

// Simple image upscaling using canvas (basic fallback)
export async function upscaleImageClientSide(imageUrl, scaleFactor = 2) {
  try {
    console.log('Starting client-side image upscaling for:', imageUrl);
    
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    return new Promise((resolve, reject) => {
      img.onload = () => {
        try {
          // Create canvas with scaled dimensions
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          const newWidth = img.width * scaleFactor;
          const newHeight = img.height * scaleFactor;
          
          canvas.width = newWidth;
          canvas.height = newHeight;
          
          // Use image smoothing for better quality
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          
          // Draw scaled image
          ctx.drawImage(img, 0, 0, newWidth, newHeight);
          
          // Convert to blob and return URL
          canvas.toBlob((blob) => {
            const url = URL.createObjectURL(blob);
            console.log('Client-side image upscaling completed');
            resolve(url);
          }, 'image/png');
        } catch (error) {
          console.error('Error during upscaling:', error);
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      
      img.src = imageUrl;
    });
  } catch (error) {
    console.error('Client-side image upscaling failed:', error);
    throw error;
  }
}
