"use client";

import { use } from "react";
import TemplateCreator from "@/components/admin/TemplateCreator";
import ClientWrapper from "@/components/ClientWrapper";
import ErrorBoundary from "@/components/ErrorBoundary";

export default function EditTemplatePage({ params }) {
  const { id } = use(params);

  return (
    <ErrorBoundary>
      <ClientWrapper>
        <TemplateCreator templateId={id} />
      </ClientWrapper>
    </ErrorBoundary>
  );
}
