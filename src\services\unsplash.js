/**
 * Unsplash API Service
 * Provides functions to interact with the Unsplash API for image search and retrieval
 */

const UNSPLASH_API_URL = 'https://api.unsplash.com';
const ACCESS_KEY = process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY;

// Check if API key is configured
const isConfigured = () => {
  return ACCESS_KEY && ACCESS_KEY !== 'demo_access_key' && ACCESS_KEY !== 'your_unsplash_access_key_here';
};

// Default headers for API requests
const getHeaders = () => ({
  'Authorization': `Client-ID ${ACCESS_KEY}`,
  'Content-Type': 'application/json',
});

/**
 * Search for photos on Unsplash
 * @param {string} query - Search query
 * @param {number} page - Page number (default: 1)
 * @param {number} perPage - Number of results per page (default: 20, max: 30)
 * @param {string} orientation - Image orientation ('landscape', 'portrait', 'squarish')
 * @returns {Promise<Object>} Search results
 */
export const searchPhotos = async (query, page = 1, perPage = 20, orientation = null) => {
  if (!isConfigured()) {
    console.warn('Unsplash API key not configured, returning demo images');
    return getDemoImages(query);
  }

  try {
    const params = new URLSearchParams({
      query,
      page: page.toString(),
      per_page: Math.min(perPage, 30).toString(),
    });

    if (orientation) {
      params.append('orientation', orientation);
    }

    const response = await fetch(`${UNSPLASH_API_URL}/search/photos?${params}`, {
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Unsplash API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      results: data.results.map(photo => ({
        id: photo.id,
        url: photo.urls.regular,
        thumb: photo.urls.thumb,
        small: photo.urls.small,
        full: photo.urls.full,
        alt: photo.alt_description || photo.description || 'Unsplash photo',
        author: photo.user.name,
        authorUrl: photo.user.links.html,
        downloadUrl: photo.links.download_location,
        width: photo.width,
        height: photo.height,
      })),
      total: data.total,
      totalPages: data.total_pages,
    };
  } catch (error) {
    console.error('Error searching Unsplash photos:', error);
    throw error;
  }
};

/**
 * Get featured/curated photos
 * @param {number} page - Page number (default: 1)
 * @param {number} perPage - Number of results per page (default: 20, max: 30)
 * @returns {Promise<Array>} Featured photos
 */
export const getFeaturedPhotos = async (page = 1, perPage = 20) => {
  if (!isConfigured()) {
    console.warn('Unsplash API key not configured, returning demo images');
    return getDemoImages('featured').results;
  }

  try {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: Math.min(perPage, 30).toString(),
    });

    const response = await fetch(`${UNSPLASH_API_URL}/photos?${params}`, {
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Unsplash API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return data.map(photo => ({
      id: photo.id,
      url: photo.urls.regular,
      thumb: photo.urls.thumb,
      small: photo.urls.small,
      full: photo.urls.full,
      alt: photo.alt_description || photo.description || 'Unsplash photo',
      author: photo.user.name,
      authorUrl: photo.user.links.html,
      downloadUrl: photo.links.download_location,
      width: photo.width,
      height: photo.height,
    }));
  } catch (error) {
    console.error('Error fetching featured photos:', error);
    throw error;
  }
};

/**
 * Get photos by category/collection
 * @param {string} category - Category name
 * @param {number} page - Page number (default: 1)
 * @param {number} perPage - Number of results per page (default: 20)
 * @returns {Promise<Array>} Category photos
 */
export const getPhotosByCategory = async (category, page = 1, perPage = 20) => {
  return searchPhotos(category, page, perPage);
};

/**
 * Track photo download (required by Unsplash API guidelines)
 * @param {string} downloadUrl - Download URL from photo object
 */
export const trackDownload = async (downloadUrl) => {
  if (!isConfigured() || !downloadUrl) return;

  try {
    await fetch(downloadUrl, {
      headers: getHeaders(),
    });
  } catch (error) {
    console.error('Error tracking download:', error);
  }
};

/**
 * Demo images for when API key is not configured
 * @param {string} query - Search query (used for demo categorization)
 * @returns {Object} Demo search results
 */
const getDemoImages = (query = 'nature') => {
  const demoImages = [
    {
      id: 'demo-1',
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
      thumb: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=150&fit=crop',
      small: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      full: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=900&fit=crop',
      alt: 'Mountain landscape',
      author: 'Demo Author',
      authorUrl: '#',
      downloadUrl: null,
      width: 1200,
      height: 900,
    },
    {
      id: 'demo-2',
      url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop',
      thumb: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=200&h=150&fit=crop',
      small: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop',
      full: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1200&h=900&fit=crop',
      alt: 'Forest path',
      author: 'Demo Author',
      authorUrl: '#',
      downloadUrl: null,
      width: 1200,
      height: 900,
    },
    {
      id: 'demo-3',
      url: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop',
      thumb: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=200&h=150&fit=crop',
      small: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop',
      full: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=900&fit=crop',
      alt: 'Lake view',
      author: 'Demo Author',
      authorUrl: '#',
      downloadUrl: null,
      width: 1200,
      height: 900,
    },
    {
      id: 'demo-4',
      url: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800&h=600&fit=crop',
      thumb: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=200&h=150&fit=crop',
      small: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=300&fit=crop',
      full: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=1200&h=900&fit=crop',
      alt: 'City skyline',
      author: 'Demo Author',
      authorUrl: '#',
      downloadUrl: null,
      width: 1200,
      height: 900,
    },
    {
      id: 'demo-5',
      url: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=800&h=600&fit=crop',
      thumb: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=200&h=150&fit=crop',
      small: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=400&h=300&fit=crop',
      full: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=1200&h=900&fit=crop',
      alt: 'Desert sunset',
      author: 'Demo Author',
      authorUrl: '#',
      downloadUrl: null,
      width: 1200,
      height: 900,
    },
    {
      id: 'demo-6',
      url: 'https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=800&h=600&fit=crop',
      thumb: 'https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=200&h=150&fit=crop',
      small: 'https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=400&h=300&fit=crop',
      full: 'https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=1200&h=900&fit=crop',
      alt: 'Ocean waves',
      author: 'Demo Author',
      authorUrl: '#',
      downloadUrl: null,
      width: 1200,
      height: 900,
    },
  ];

  return {
    results: demoImages,
    total: demoImages.length,
    totalPages: 1,
  };
};

/**
 * Get popular search categories
 * @returns {Array} Popular categories
 */
export const getPopularCategories = () => [
  'nature',
  'business',
  'technology',
  'people',
  'food',
  'travel',
  'architecture',
  'animals',
  'sports',
  'art',
  'fashion',
  'music',
];

/**
 * Check if Unsplash API is properly configured
 * @returns {boolean} Configuration status
 */
export const isUnsplashConfigured = isConfigured;
