"use client";

import { useEffect, useState } from "react";
import { useTemplateStore } from "@/store/templateStore";
import { useEditorStore } from "@/store";
import PosterEditor from "./PosterEditor";
import TemplateCreator from "../admin/TemplateCreator";

export default function TemplateAwareEditor({ mode = "editor", templateId = null }) {
  const { userRole, currentTemplate, setCurrentTemplate, templates, loadTemplates } = useTemplateStore();
  const { canvas, setIsTemplateMode, setName } = useEditorStore();
  const [isLoading, setIsLoading] = useState(true);

  // Load templates on mount
  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  useEffect(() => {
    // Set template mode based on the mode prop
    setIsTemplateMode(mode === "template-create" || mode === "template-edit");

    // Load template if templateId is provided
    if (templateId && templates.length > 0) {
      const template = templates.find(t => t.id === templateId);
      if (template) {
        console.log('TemplateAwareEditor: Setting template name to:', template.name);
        setCurrentTemplate(template);
        setName(template.name); // Set the editor name to template name
        loadTemplateToCanvas(template);
      } else {
        console.log('TemplateAwareEditor: Template not found with ID:', templateId);
      }
    } else if (templateId) {
      console.log('TemplateAwareEditor: Waiting for templates to load, templateId:', templateId, 'templates count:', templates.length);
    }

    setIsLoading(false);
  }, [mode, templateId, templates, setIsTemplateMode, setCurrentTemplate, setName]);

  const loadTemplateToCanvas = async (template) => {
    if (!canvas || !template) return;

    try {
      console.log('Loading template to canvas in TemplateAwareEditor:', template.name);

      // Store current design area and corner indicators
      const existingDesignArea = canvas.getObjects().find(obj => obj.id === 'design-area');
      const existingCornerIndicators = canvas.getObjects().filter(obj => obj.id?.startsWith('corner-indicator'));

      // Clear existing objects (except design area and corner indicators)
      const userObjects = canvas.getObjects().filter(obj =>
        !obj.isDesignArea &&
        !obj.isDesignAreaElement &&
        obj.id !== 'design-area' &&
        !obj.id?.startsWith('corner-indicator')
      );

      userObjects.forEach(obj => canvas.remove(obj));

      // Load template objects
      if (template.canvas && template.canvas.objects) {
        const fabric = await import("fabric");

        // Filter out any design area objects from template data
        const templateObjects = template.canvas.objects.filter(obj =>
          !obj.isDesignArea &&
          !obj.isDesignAreaElement &&
          obj.id !== 'design-area' &&
          !obj.id?.startsWith('corner-indicator')
        );

        if (templateObjects.length > 0) {
          fabric.util.enlivenObjects(templateObjects, (objects) => {
            objects.forEach(obj => {
              // Add template object to canvas
              canvas.add(obj);

              // If in user mode, check if this object is in an editable zone
              if (mode === "template-customize") {
                const zone = template.editableZones?.find(z => z.objectId === obj.id);
                if (!zone) {
                  // Make non-editable objects non-selectable for users
                  obj.selectable = false;
                  obj.evented = false;
                  obj.lockMovementX = true;
                  obj.lockMovementY = true;
                  obj.lockRotation = true;
                  obj.lockScalingX = true;
                  obj.lockScalingY = true;
                }
              }
            });

            // Ensure design area stays at the back
            if (existingDesignArea) {
              canvas.moveTo(existingDesignArea, 0);
            }

            // Ensure corner indicators stay after design area
            existingCornerIndicators.forEach((indicator, index) => {
              try {
                canvas.moveTo(indicator, index + 1);
              } catch (e) {
                console.warn("Could not move corner indicator:", e);
              }
            });

            canvas.renderAll();
            console.log('Template objects loaded successfully in TemplateAwareEditor');
          });
        } else {
          console.log('No template objects to load');
          canvas.renderAll();
        }
      }

      // Update canvas background if specified
      if (template.canvas && template.canvas.backgroundColor) {
        // Update design area background color
        const designArea = canvas.getObjects().find(obj => obj.id === 'design-area');
        if (designArea) {
          designArea.set('fill', template.canvas.backgroundColor);
          canvas.renderAll();
        }
      }

      // Ensure design area exists
      const hasDesignArea = canvas.getObjects().some(obj => obj.id === 'design-area');
      if (!hasDesignArea) {
        console.log('No design area found after template load, this might cause display issues');
      }

    } catch (error) {
      console.error('Error loading template to canvas:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Editor...</h2>
          <p className="text-gray-600">Preparing template-aware interface...</p>
        </div>
      </div>
    );
  }

  // Render appropriate editor based on mode
  switch (mode) {
    case "template-create":
    case "template-edit":
      return <TemplateCreator templateId={templateId} />;
    
    case "template-customize":
      return (
        <div className="template-customize-mode">
          <PosterEditor />
          {/* Add template customization overlay/controls here */}
        </div>
      );
    
    default:
      return <PosterEditor />;
  }
}
