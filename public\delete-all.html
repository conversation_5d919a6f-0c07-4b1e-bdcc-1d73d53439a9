<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete All Templates</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .warning {
            background-color: #fee;
            border: 1px solid #fcc;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            color: #c33;
        }
        .button {
            background-color: #dc3545;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .button:hover {
            background-color: #c82333;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .button.secondary:hover {
            background-color: #5a6268;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Delete All Templates</h1>
        
        <div class="warning">
            <strong>⚠️ WARNING:</strong> This action will permanently delete ALL templates and designs from the database. This cannot be undone!
        </div>
        
        <p>This will delete all data from both the <code>templates</code> and <code>designs</code> tables, giving you a completely fresh start.</p>
        
        <button class="button" onclick="deleteAll()">Delete All Templates</button>
        <button class="button secondary" onclick="window.location.href='/admin'">Cancel - Go to Admin</button>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        async function deleteAll() {
            if (!confirm('Are you absolutely sure you want to delete ALL templates and designs? This cannot be undone!')) {
                return;
            }
            
            if (!confirm('This is your final warning. All templates and designs will be permanently deleted. Continue?')) {
                return;
            }
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            
            try {
                const response = await fetch('/api/debug/delete-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                resultDiv.style.display = 'block';
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Success!</strong><br>
                        Deleted ${result.deleted.templates} templates and ${result.deleted.designs} designs.<br>
                        <a href="/admin">Go to Admin Dashboard</a>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ Error:</strong><br>
                        ${result.error}
                    `;
                }
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Network Error:</strong><br>
                    ${error.message}
                `;
            }
        }
    </script>
</body>
</html>
