// Verification script for the enhanced undo/redo system
// Run this in the browser console when the app is loaded

console.log('🔍 Verifying Enhanced Undo/Redo Implementation...');

// Check if the required modules are available
async function verifyImplementation() {
  try {
    // 1. Check if CanvasHistoryManager is exported
    const fabricUtils = await import('./src/fabric/fabric-utils.js');
    console.log('✅ fabric-utils module loaded');
    
    if (fabricUtils.CanvasHistoryManager) {
      console.log('✅ CanvasHistoryManager class is exported');
    } else {
      console.error('❌ CanvasHistoryManager class not found');
      return false;
    }

    // 2. Check if store has the new methods
    const store = await import('./src/store/index.js');
    console.log('✅ Store module loaded');
    
    // 3. Check if the store has the enhanced methods
    const storeState = store.useEditorStore.getState();
    
    const requiredMethods = [
      'initializeHistoryManager',
      'undo',
      'redo', 
      'canUndo',
      'canRedo',
      'clearHistory'
    ];
    
    const missingMethods = requiredMethods.filter(method => typeof storeState[method] !== 'function');
    
    if (missingMethods.length === 0) {
      console.log('✅ All required store methods are present');
    } else {
      console.error('❌ Missing store methods:', missingMethods);
      return false;
    }

    // 4. Check if historyManager property exists
    if ('historyManager' in storeState) {
      console.log('✅ historyManager property exists in store');
    } else {
      console.error('❌ historyManager property not found in store');
      return false;
    }

    console.log('🎉 Implementation verification completed successfully!');
    console.log('📝 To test the functionality:');
    console.log('   1. Open the app in browser');
    console.log('   2. Add some objects to the canvas');
    console.log('   3. Move, scale, or rotate objects');
    console.log('   4. Use the undo/redo buttons');
    console.log('   5. Each operation should be undoable step by step');
    
    return true;
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
    return false;
  }
}

// Function to test the history manager in browser
function testHistoryManagerInBrowser() {
  console.log('🧪 Testing History Manager in Browser...');

  // Get the store state
  const storeState = window.__ZUSTAND_STORE__?.getState?.() ||
                     window.useEditorStore?.getState?.();

  if (!storeState) {
    console.error('❌ Could not access store state');
    return;
  }

  console.log('Store state:', {
    hasCanvas: !!storeState.canvas,
    hasHistoryManager: !!storeState.historyManager,
    canUndo: storeState.canUndo(),
    canRedo: storeState.canRedo()
  });

  if (storeState.historyManager) {
    console.log('History Manager state:', {
      historyLength: storeState.historyManager.history.length,
      currentIndex: storeState.historyManager.currentIndex,
      canUndo: storeState.historyManager.canUndo(),
      canRedo: storeState.historyManager.canRedo(),
      isTrackingDisabled: storeState.historyManager.isTrackingDisabled
    });
  }
}

// Function to test adding objects manually
async function testAddShape() {
  console.log('🔧 Testing shape addition...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas) {
    console.error('❌ No canvas available');
    return;
  }

  try {
    // Import the function
    const { addShapeToCanvas } = await import('./src/fabric/fabric-utils.js');

    // Add a rectangle
    const shape = await addShapeToCanvas(storeState.canvas, 'rectangle');

    if (shape) {
      console.log('✅ Shape added successfully:', shape.id);
    } else {
      console.error('❌ Failed to add shape');
    }
  } catch (error) {
    console.error('❌ Error testing shape addition:', error);
  }
}

// Function to test adding text manually
async function testAddText() {
  console.log('📝 Testing text addition...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas) {
    console.error('❌ No canvas available');
    return;
  }

  try {
    // Import the function
    const { addTextToCanvas } = await import('./src/fabric/fabric-utils.js');

    // Add text
    const text = await addTextToCanvas(storeState.canvas, 'Test Text');

    if (text) {
      console.log('✅ Text added successfully:', text.id);
    } else {
      console.error('❌ Failed to add text');
    }
  } catch (error) {
    console.error('❌ Error testing text addition:', error);
  }
}

// Function to test undo/redo specifically with shapes (not drawing)
async function testUndoRedo() {
  console.log('🧪 Testing Undo/Redo functionality with shapes...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas) {
    console.error('❌ No canvas available');
    return;
  }

  try {
    console.log('📝 Initial state:');
    storeState.debugHistory();

    // Add a shape (not drawing - shapes should work better)
    const { addShapeToCanvas } = await import('./src/fabric/fabric-utils.js');
    console.log('➕ Adding a rectangle...');
    const shape = await addShapeToCanvas(storeState.canvas, 'rectangle');

    if (!shape) {
      console.error('❌ Failed to add shape');
      return;
    }

    console.log('✅ Shape added:', shape.id);

    // Wait a moment for history to be recorded
    setTimeout(() => {
      console.log('📝 After adding shape:');
      storeState.debugHistory();

      console.log('⬅️ Testing undo...');
      storeState.undo().then(() => {
        setTimeout(() => {
          console.log('📝 After undo:');
          storeState.debugHistory();

          console.log('➡️ Testing redo...');
          storeState.redo().then(() => {
            setTimeout(() => {
              console.log('📝 After redo:');
              storeState.debugHistory();
              console.log('🎉 Undo/Redo test completed!');
            }, 100);
          }).catch(error => {
            console.error('❌ Redo failed:', error);
          });
        }, 100);
      }).catch(error => {
        console.error('❌ Undo failed:', error);
      });
    }, 500); // Increased delay to ensure history is recorded

  } catch (error) {
    console.error('❌ Error testing undo/redo:', error);
  }
}

// Function to test drawing undo/redo
async function testDrawingUndoRedo() {
  console.log('🎨 Testing Drawing Undo/Redo...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas) {
    console.error('❌ No canvas available');
    return;
  }

  console.log('📝 Current history:');
  storeState.debugHistory();

  console.log('ℹ️ Please draw some strokes manually, then test:');
  console.log('   1. Draw 2-3 strokes');
  console.log('   2. Run: storeState.undo() - should remove last stroke');
  console.log('   3. Run: storeState.redo() - should restore the stroke');
  console.log('   4. Repeat to test multiple strokes');

  // Auto-test if there are drawing operations in history
  const drawingEntries = storeState.historyManager?.history?.filter(h => h.operation === 'draw') || [];
  if (drawingEntries.length > 0) {
    console.log(`Found ${drawingEntries.length} drawing operations in history`);
    console.log('Testing undo/redo automatically...');

    if (storeState.canUndo()) {
      console.log('⬅️ Testing undo...');
      await storeState.undo();

      setTimeout(() => {
        if (storeState.canRedo()) {
          console.log('➡️ Testing redo...');
          storeState.redo().then(() => {
            console.log('🎉 Drawing undo/redo test completed!');
          }).catch(error => {
            console.error('❌ Drawing redo failed:', error);
          });
        } else {
          console.log('❌ Cannot redo after undo');
        }
      }, 500);
    }
  }
}

// Simple test for basic functionality
async function testBasicFunctionality() {
  console.log('🔧 Testing basic functionality...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState) {
    console.error('❌ No store available');
    return;
  }

  console.log('Store state:', {
    hasCanvas: !!storeState.canvas,
    hasHistoryManager: !!storeState.historyManager,
    canUndo: storeState.canUndo(),
    canRedo: storeState.canRedo(),
    activeTool: storeState.activeTool,
    isPanMode: storeState.isPanMode,
    isDrawingMode: storeState.isDrawingMode
  });

  if (storeState.historyManager) {
    console.log('History manager state:', {
      historyLength: storeState.historyManager.history.length,
      currentIndex: storeState.historyManager.currentIndex,
      isTrackingDisabled: storeState.historyManager.isTrackingDisabled
    });
  }
}

// Test pan functionality
async function testPanMode() {
  console.log('🖱️ Testing Pan Mode...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState) {
    console.error('❌ No store available');
    return;
  }

  console.log('Current tool:', storeState.activeTool);
  console.log('Pan mode active:', storeState.isPanMode);

  if (storeState.activeTool !== 'pan') {
    console.log('ℹ️ Activating pan mode...');
    storeState.setActivePanel('pan');

    setTimeout(() => {
      const newState = window.useEditorStore?.getState?.();
      console.log('After activation:');
      console.log('- Active tool:', newState.activeTool);
      console.log('- Pan mode:', newState.isPanMode);
      console.log('- Canvas cursor:', newState.canvas?.defaultCursor);

      if (newState.isPanMode) {
        console.log('✅ Pan mode activated successfully!');
        console.log('ℹ️ You should see:');
        console.log('  • Yellow "Pan Mode Active" indicator');
        console.log('  • Cursor changes to grab/grabbing');
        console.log('  • Objects become non-selectable');
        console.log('  • Click and drag to pan the canvas');
      } else {
        console.log('❌ Pan mode activation failed');
      }
    }, 100);
  } else {
    console.log('✅ Pan mode is already active');
    console.log('ℹ️ Click and drag on the canvas to pan around');
  }
}

// Test layers functionality
async function testLayers() {
  console.log('📚 Testing Layers Functionality...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas) {
    console.error('❌ No canvas available');
    return;
  }

  console.log('Current layers panel visibility:', storeState.showLayers);

  // Get current objects
  const objects = storeState.canvas.getObjects().filter(obj =>
    !obj.isDesignArea && !obj.isDesignAreaElement && !obj.id?.startsWith('corner-indicator')
  );

  console.log(`Found ${objects.length} user objects on canvas`);

  if (objects.length === 0) {
    console.log('ℹ️ Adding test objects for layer testing...');

    // Add some test objects
    const { addShapeToCanvas, addTextToCanvas } = await import('./src/fabric/fabric-utils.js');

    console.log('➕ Adding rectangle...');
    await addShapeToCanvas(storeState.canvas, 'rectangle');

    setTimeout(async () => {
      console.log('➕ Adding circle...');
      await addShapeToCanvas(storeState.canvas, 'circle');

      setTimeout(async () => {
        console.log('➕ Adding text...');
        await addTextToCanvas(storeState.canvas, 'Layer Test');

        setTimeout(() => {
          testLayerOperations();
        }, 500);
      }, 500);
    }, 500);
  } else {
    testLayerOperations();
  }
}

function testLayerOperations() {
  const storeState = window.useEditorStore?.getState?.();
  const objects = storeState.canvas.getObjects().filter(obj =>
    !obj.isDesignArea && !obj.isDesignAreaElement && !obj.id?.startsWith('corner-indicator')
  );

  console.log('🔍 Testing layer operations...');
  console.log(`Canvas has ${objects.length} objects`);

  objects.forEach((obj, index) => {
    console.log(`Layer ${index + 1}: ${obj.type} (ID: ${obj.id})`);
    console.log(`  - Visible: ${obj.visible !== false}`);
    console.log(`  - Selectable: ${obj.selectable}`);
    console.log(`  - Canvas index: ${storeState.canvas.getObjects().indexOf(obj)}`);
  });

  console.log('✅ Layer testing completed!');
  console.log('ℹ️ You should see:');
  console.log('  • Layers panel on the right showing all objects');
  console.log('  • Top layer appears first in the list');
  console.log('  • Layer controls (up/down/hide/lock/delete) work');
  console.log('  • Selected object is highlighted in layers panel');
}

// Test layer movement specifically
async function testLayerMovement() {
  console.log('🔄 Testing Layer Movement...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas) {
    console.error('❌ No canvas available');
    return;
  }

  const canvas = storeState.canvas;
  const objects = canvas.getObjects().filter(obj =>
    !obj.isDesignArea && !obj.isDesignAreaElement && !obj.id?.startsWith('corner-indicator')
  );

  if (objects.length < 2) {
    console.log('ℹ️ Need at least 2 objects to test layer movement. Adding objects...');

    const { addShapeToCanvas } = await import('./src/fabric/fabric-utils.js');
    await addShapeToCanvas(canvas, 'rectangle');

    setTimeout(async () => {
      await addShapeToCanvas(canvas, 'circle');
      setTimeout(() => {
        testLayerMovement();
      }, 500);
    }, 500);
    return;
  }

  console.log(`Found ${objects.length} objects for testing`);

  // Test moving the bottom object up
  const bottomObject = objects[0]; // First object in array is at bottom
  console.log(`Testing: Moving bottom object (${bottomObject.id}) up...`);

  // Test the canvas methods directly
  console.log('🧪 Testing canvas.bringObjectForward...');
  try {
    const result = canvas.bringObjectForward(bottomObject);
    console.log('✅ bringObjectForward result:', result);
    canvas.renderAll();
  } catch (error) {
    console.error('❌ bringObjectForward failed:', error);
  }

  setTimeout(() => {
    console.log('🧪 Testing canvas.sendObjectBackwards...');
    try {
      const result = canvas.sendObjectBackwards(bottomObject);
      console.log('✅ sendObjectBackwards result:', result);
      canvas.renderAll();

      console.log('🎉 Layer movement tests completed!');
      console.log('ℹ️ All Fabric.js v6 methods are working correctly');
    } catch (error) {
      console.error('❌ sendObjectBackwards failed:', error);
    }
  }, 1000);
}

// Test undo/redo with multiple object types
async function testUndoRedoMultipleTypes() {
  console.log('🔄 Testing Undo/Redo with Multiple Object Types...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas || !storeState.historyManager) {
    console.error('❌ Canvas or history manager not available');
    return;
  }

  const canvas = storeState.canvas;
  const historyManager = storeState.historyManager;

  console.log('📊 Initial state:');
  historyManager.debugHistory();

  // Add different types of shapes
  console.log('➕ Adding multiple object types...');
  const { addShapeToCanvas } = await import('./src/fabric/fabric-utils.js');

  const shapesToAdd = ['rectangle', 'circle', 'ellipse', 'triangle'];

  for (const shape of shapesToAdd) {
    console.log(`Adding ${shape}...`);
    await addShapeToCanvas(canvas, shape);
    await new Promise(resolve => setTimeout(resolve, 200)); // Wait between additions
  }

  console.log('📊 After adding all shapes:');
  historyManager.debugHistory();

  const objectsAfterAdd = canvas.getObjects().filter(obj => !obj.isDesignArea && !obj.isDesignAreaElement);
  console.log(`Objects on canvas: ${objectsAfterAdd.length}`);

  // Undo all operations
  console.log('⏪ Undoing all operations...');
  let undoCount = 0;
  while (historyManager.canUndo()) {
    const result = await historyManager.undo();
    undoCount++;
    console.log(`Undo ${undoCount}: ${result ? 'success' : 'failed'}`);
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('📊 After undoing all:');
  historyManager.debugHistory();
  console.log(`Canvas objects: ${canvas.getObjects().filter(obj => !obj.isDesignArea && !obj.isDesignAreaElement).length}`);

  // Try to redo all operations with throttling
  console.log('⏩ Redoing all operations (with throttling)...');
  let redoCount = 0;
  while (historyManager.canRedo()) {
    console.log(`\n🔄 Attempting redo ${redoCount + 1}...`);
    console.log(`Can redo: ${historyManager.canRedo()}`);
    console.log(`Current index: ${historyManager.currentIndex}`);

    const result = await historyManager.redo();
    redoCount++;
    console.log(`Redo ${redoCount}: ${result ? 'success' : 'failed'}`);

    if (!result) {
      console.error('❌ Redo failed! Debugging...');
      historyManager.debugHistory();
      break;
    }

    // Wait longer between redo operations to prevent conflicts
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  console.log('📊 Final state:');
  historyManager.debugHistory();

  const finalObjects = canvas.getObjects().filter(obj => !obj.isDesignArea && !obj.isDesignAreaElement);
  console.log(`Canvas objects: ${finalObjects.length}`);

  console.log(`✅ Test completed. Undid ${undoCount} operations, redid ${redoCount} operations.`);

  if (undoCount !== redoCount) {
    console.error(`❌ ISSUE DETECTED: Undid ${undoCount} operations but only redid ${redoCount}`);
    console.log('🔧 Try running: window.useEditorStore.getState().historyManager.forceResetUndoRedoState()');
  } else {
    console.log('✅ Undo/Redo working correctly!');
  }

  // Final verification - check if objects are actually visible
  console.log(`📊 Final verification: ${finalObjects.length} objects on canvas`);
  finalObjects.forEach((obj, index) => {
    console.log(`  ${index + 1}. ${obj.type} (ID: ${obj.id}) - visible: ${obj.visible}, selectable: ${obj.selectable}`);
  });

  // Verify we have the expected number of objects
  if (finalObjects.length === shapesToAdd.length) {
    console.log('✅ All objects restored successfully!');
  } else {
    console.error(`❌ Expected ${shapesToAdd.length} objects, but found ${finalObjects.length}`);
  }
}

// Test undo/redo from initial state
async function testUndoRedoFromInitial() {
  console.log('🔄 Testing Undo/Redo from Initial State...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas || !storeState.historyManager) {
    console.error('❌ Canvas or history manager not available');
    return;
  }

  const canvas = storeState.canvas;
  const historyManager = storeState.historyManager;

  console.log('📊 Initial state:');
  historyManager.debugHistory();

  // Add a shape
  console.log('➕ Adding a rectangle...');
  const { addShapeToCanvas } = await import('./src/fabric/fabric-utils.js');
  await addShapeToCanvas(canvas, 'rectangle');

  // Wait for history to update
  await new Promise(resolve => setTimeout(resolve, 100));

  console.log('📊 After adding rectangle:');
  historyManager.debugHistory();

  // Add another shape
  console.log('➕ Adding a circle...');
  await addShapeToCanvas(canvas, 'circle');

  // Wait for history to update
  await new Promise(resolve => setTimeout(resolve, 100));

  console.log('📊 After adding circle:');
  historyManager.debugHistory();

  // Undo all operations
  console.log('⏪ Undoing all operations...');
  let undoCount = 0;
  while (historyManager.canUndo()) {
    const result = await historyManager.undo();
    undoCount++;
    console.log(`Undo ${undoCount}: ${result ? 'success' : 'failed'}`);
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('📊 After undoing all:');
  historyManager.debugHistory();
  console.log(`Canvas objects: ${canvas.getObjects().filter(obj => !obj.isDesignArea && !obj.isDesignAreaElement).length}`);

  // Try to redo all operations
  console.log('⏩ Redoing all operations...');
  let redoCount = 0;
  while (historyManager.canRedo()) {
    console.log(`\n🔄 Attempting redo ${redoCount + 1}...`);
    console.log(`Can redo: ${historyManager.canRedo()}`);
    console.log(`Current index: ${historyManager.currentIndex}`);
    console.log(`History length: ${historyManager.history.length}`);

    const result = await historyManager.redo();
    redoCount++;
    console.log(`Redo ${redoCount}: ${result ? 'success' : 'failed'}`);

    if (!result) {
      console.error('❌ Redo failed! Debugging...');
      historyManager.debugHistory();
      break;
    }

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('📊 Final state:');
  historyManager.debugHistory();
  console.log(`Canvas objects: ${canvas.getObjects().filter(obj => !obj.isDesignArea && !obj.isDesignAreaElement).length}`);

  console.log(`✅ Test completed. Undid ${undoCount} operations, redid ${redoCount} operations.`);

  if (undoCount !== redoCount) {
    console.error(`❌ ISSUE DETECTED: Undid ${undoCount} operations but only redid ${redoCount}`);
    console.log('🔧 Try running: window.useEditorStore.getState().historyManager.forceResetUndoRedoState()');
  } else {
    console.log('✅ Undo/Redo working correctly!');
  }

  // Final verification - check if objects are actually visible
  const finalObjects = canvas.getObjects().filter(obj => !obj.isDesignArea && !obj.isDesignAreaElement);
  console.log(`📊 Final verification: ${finalObjects.length} objects on canvas`);
  finalObjects.forEach((obj, index) => {
    console.log(`  ${index + 1}. ${obj.type} (ID: ${obj.id}) - visible: ${obj.visible}, selectable: ${obj.selectable}`);
  });
}

// Test drawing session functionality
async function testDrawingSessions() {
  console.log('🎨 Testing Drawing Session Grouping...');

  const storeState = window.useEditorStore?.getState?.();
  if (!storeState || !storeState.canvas) {
    console.error('❌ No canvas available');
    return;
  }

  const historyManager = storeState.historyManager;
  const drawingSessionManager = historyManager?.getDrawingSessionManager?.();

  if (!drawingSessionManager) {
    console.error('❌ No drawing session manager available');
    return;
  }

  console.log('✅ Drawing session manager found');
  console.log('Current session timeout:', drawingSessionManager.options.sessionTimeout);

  // Check current state
  const stats = drawingSessionManager.getSessionStats();
  console.log('📊 Session stats:', stats);

  // Test session creation
  console.log('🧪 Testing session creation...');

  if (!stats.currentSession) {
    console.log('ℹ️ No active session. Switch to drawing tool and draw some strokes to test.');
    console.log('Expected behavior:');
    console.log('  1. Switch to drawing tool');
    console.log('  2. Draw multiple strokes');
    console.log('  3. See "Drawing Session X" indicator');
    console.log('  4. Check layers panel for grouped session');
    console.log('  5. Switch tools to end session');
  } else {
    console.log(`✅ Active session: ${stats.currentSession}`);
    console.log(`Paths in session: ${storeState.currentDrawingSession?.paths?.length || 0}`);
  }

  // List all sessions
  const allSessions = drawingSessionManager.getAllSessions();
  console.log(`📚 Total sessions: ${allSessions.length}`);

  allSessions.forEach((session, index) => {
    console.log(`Session ${index + 1}: ${session.name} (${session.paths.length} paths, active: ${session.isActive})`);
  });

  console.log('🎯 To test drawing sessions:');
  console.log('  1. Enable drawing mode');
  console.log('  2. Draw several strokes');
  console.log('  3. Check layers panel for grouped session');
  console.log('  4. Test session controls (hide/show, lock/unlock, delete)');
  console.log('  5. Switch tools to end session');
  console.log('  6. Start new session and repeat');
}

// Export for browser testing
if (typeof window !== 'undefined') {
  window.testHistoryManager = testHistoryManagerInBrowser;
  window.verifyImplementation = verifyImplementation;
  window.testAddShape = testAddShape;
  window.testAddText = testAddText;
  window.testUndoRedo = testUndoRedo;
  window.testDrawingUndoRedo = testDrawingUndoRedo;
  window.testBasicFunctionality = testBasicFunctionality;
  window.testPanMode = testPanMode;
  window.testLayers = testLayers;
  window.testLayerMovement = testLayerMovement;
  window.testDrawingSessions = testDrawingSessions;
  window.testUndoRedoFromInitial = testUndoRedoFromInitial;
  window.testUndoRedoMultipleTypes = testUndoRedoMultipleTypes;
}

// Run verification if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  verifyImplementation();
}
