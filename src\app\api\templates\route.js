import { NextResponse } from 'next/server';
import { saveTemplate, getUserTemplates, getPublicTemplates } from '@/services/templateService';

// GET /api/templates - Get templates (user's templates or public templates)
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type') || 'user'; // 'user' or 'public'

    let result;

    if (type === 'public') {
      result = await getPublicTemplates(page, limit);
    } else if (userId) {
      result = await getUserTemplates(userId, page, limit);
    } else {
      return NextResponse.json(
        { success: false, error: 'User ID is required for user templates' },
        { status: 400 }
      );
    }

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in GET /api/templates:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/templates - Create a new template
export async function POST(request) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.canvasData || !body.createdById) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: name, canvasData, and createdById are required' 
        },
        { status: 400 }
      );
    }

    // Generate thumbnail from canvas data if not provided
    let thumbnail = body.thumbnail;
    if (!thumbnail && body.canvasData) {
      // You can implement thumbnail generation here
      // For now, we'll use a placeholder or the provided thumbnail
      thumbnail = body.thumbnail || null;
    }

    const templateData = {
      name: body.name,
      description: body.description || null,
      category: body.category || 'general',
      width: body.width || 800,
      height: body.height || 600,
      canvasData: body.canvasData,
      thumbnail: thumbnail,
      isPublic: body.isPublic || true,
      isPremium: body.isPremium || false,
      tags: body.tags || [],
      editableZones: body.editableZones || null,
      createdById: body.createdById,
    };

    const result = await saveTemplate(templateData);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/templates:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
