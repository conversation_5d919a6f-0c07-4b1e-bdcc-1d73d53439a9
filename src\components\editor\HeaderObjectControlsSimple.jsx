"use client";

import { useState, useEffect } from "react";
import { useEditorStore } from "@/store";
import { cloneSelectedObject, deletedSelectedObject } from "@/fabric/fabric-utils";
import {
  Copy,
  Trash2,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Minus,
  Plus,
  MoveUp,
  MoveDown
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export default function HeaderObjectControlsSimple() {
  const { canvas, selectedObject, markAsModified } = useEditorStore();
  const [opacity, setOpacity] = useState(100);
  const [isVisible, setIsVisible] = useState(true);
  const [isLocked, setIsLocked] = useState(false);
  
  // Text-specific states
  const [fontSize, setFontSize] = useState(20);
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontWeight, setFontWeight] = useState("normal");
  const [fontStyle, setFontStyle] = useState("normal");
  const [textDecoration, setTextDecoration] = useState("");
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [textAlign, setTextAlign] = useState("left");
  const [textColor, setTextColor] = useState("#000000");

  // Shape-specific states
  const [fillColor, setFillColor] = useState("#000000");
  const [strokeColor, setStrokeColor] = useState("#000000");
  const [strokeWidth, setStrokeWidth] = useState(1);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedObject || !canvas) return;

      // Only handle shortcuts for text objects
      if (selectedObject.type === "textbox" || selectedObject.type === "text" || selectedObject.type === "i-text") {
        if (e.ctrlKey || e.metaKey) {
          switch (e.key.toLowerCase()) {
            case 'b':
              e.preventDefault();
              handleBoldToggle();
              break;
            case 'i':
              e.preventDefault();
              handleItalicToggle();
              break;
            case 'u':
              e.preventDefault();
              handleUnderlineToggle();
              break;
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedObject, canvas]);

  // Update properties when selection changes
  useEffect(() => {
    if (!selectedObject || !canvas) return;

    // Common properties
    setOpacity(Math.round((selectedObject.opacity || 1) * 100));
    setIsVisible(selectedObject.visible !== false);
    setIsLocked(!selectedObject.selectable);

    // Text properties
    if (selectedObject.type === "textbox" || selectedObject.type === "text" || selectedObject.type === "i-text") {
      setFontSize(selectedObject.fontSize || 20);
      setFontFamily(selectedObject.fontFamily || "Arial");
      setFontWeight(selectedObject.fontWeight || "normal");
      setFontStyle(selectedObject.fontStyle || "normal");
      setTextDecoration(selectedObject.underline ? "underline" : "");
      setIsStrikethrough(selectedObject.linethrough || false);
      setTextAlign(selectedObject.textAlign || "left");
      setTextColor(selectedObject.fill || "#000000");
    }

    // Shape properties
    if (selectedObject.type === "rect" || selectedObject.type === "circle" || selectedObject.type === "triangle" || selectedObject.type === "polygon") {
      setFillColor(selectedObject.fill || "#000000");
      setStrokeColor(selectedObject.stroke || "#000000");
      setStrokeWidth(selectedObject.strokeWidth || 1);
    }
  }, [selectedObject, canvas]);

  const updateObjectProperty = (property, value) => {
    if (!selectedObject || !canvas) return;
    selectedObject.set(property, value);
    canvas.renderAll();
    markAsModified();
  };

  // Common controls
  const handleOpacityChange = (value) => {
    const newOpacity = value[0];
    setOpacity(newOpacity);
    updateObjectProperty('opacity', newOpacity / 100);
  };

  const handleDuplicate = async () => {
    if (!canvas || !selectedObject) return;
    await cloneSelectedObject(canvas);
    markAsModified();
  };

  const handleDelete = () => {
    if (!canvas || !selectedObject) return;
    deletedSelectedObject(canvas);
    markAsModified();
  };

  const handleBringForward = () => {
    if (!canvas || !selectedObject) return;
    canvas.bringObjectForward(selectedObject);
    canvas.renderAll();
    markAsModified();
  };

  const handleSendBackward = () => {
    if (!canvas || !selectedObject) return;
    canvas.sendObjectBackwards(selectedObject);
    canvas.renderAll();
    markAsModified();
  };

  const handleToggleVisibility = () => {
    if (!canvas || !selectedObject) return;
    const newVisibility = !isVisible;
    setIsVisible(newVisibility);
    updateObjectProperty('visible', newVisibility);
  };

  const handleToggleLock = () => {
    if (!canvas || !selectedObject) return;
    const newLocked = !isLocked;
    setIsLocked(newLocked);
    selectedObject.set('selectable', !newLocked);
    selectedObject.set('evented', !newLocked);
    canvas.renderAll();
    markAsModified();
  };

  // Text-specific controls
  const handleFontSizeChange = (e) => {
    const newSize = parseInt(e.target.value) || 20;
    setFontSize(newSize);
    updateObjectProperty('fontSize', newSize);
  };

  const handleFontFamilyChange = (value) => {
    setFontFamily(value);
    updateObjectProperty('fontFamily', value);
  };

  const handleBoldToggle = () => {
    const newWeight = fontWeight === "bold" ? "normal" : "bold";
    setFontWeight(newWeight);
    updateObjectProperty('fontWeight', newWeight);
  };

  const handleItalicToggle = () => {
    const newStyle = fontStyle === "italic" ? "normal" : "italic";
    setFontStyle(newStyle);
    updateObjectProperty('fontStyle', newStyle);
  };

  const handleUnderlineToggle = () => {
    const newUnderline = textDecoration === "underline" ? "" : "underline";
    setTextDecoration(newUnderline);
    updateObjectProperty('underline', newUnderline === "underline");
  };

  const handleStrikethroughToggle = () => {
    const newStrikethrough = !isStrikethrough;
    setIsStrikethrough(newStrikethrough);
    updateObjectProperty('linethrough', newStrikethrough);
  };

  const handleTextAlignChange = (align) => {
    setTextAlign(align);
    updateObjectProperty('textAlign', align);
  };

  const handleTextColorChange = (e) => {
    const newColor = e.target.value;
    setTextColor(newColor);
    updateObjectProperty('fill', newColor);
  };

  const handleFontSizeIncrease = () => {
    const newSize = Math.min(fontSize + 1, 200);
    setFontSize(newSize);
    updateObjectProperty('fontSize', newSize);
  };

  const handleFontSizeDecrease = () => {
    const newSize = Math.max(fontSize - 1, 8);
    setFontSize(newSize);
    updateObjectProperty('fontSize', newSize);
  };

  // Shape-specific controls
  const handleFillColorChange = (e) => {
    const newColor = e.target.value;
    setFillColor(newColor);
    updateObjectProperty('fill', newColor);
  };

  const handleStrokeColorChange = (e) => {
    const newColor = e.target.value;
    setStrokeColor(newColor);
    updateObjectProperty('stroke', newColor);
  };

  const handleStrokeWidthChange = (value) => {
    const newWidth = value[0];
    setStrokeWidth(newWidth);
    updateObjectProperty('strokeWidth', newWidth);
  };

  // Don't render if no object is selected
  if (!selectedObject || !canvas) {
    return null;
  }

  const isTextObject = selectedObject.type === "textbox" || selectedObject.type === "text" || selectedObject.type === "i-text";
  const isShapeObject = selectedObject.type === "rect" || selectedObject.type === "circle" || selectedObject.type === "triangle" || selectedObject.type === "polygon";

  return (
    <div className="flex items-center space-x-4 px-4 py-2 bg-gray-50 border-b border-gray-200 overflow-x-auto min-h-[60px]">
      {/* Text Controls */}
      {isTextObject && (
        <div className="flex items-center space-x-1 border-r border-gray-300 pr-4">
          {/* Text Color */}
          <input
            type="color"
            value={textColor}
            onChange={handleTextColorChange}
            className="w-8 h-8 rounded border cursor-pointer"
            title="Text Color"
          />

          {/* Font Family */}
          <Select value={fontFamily} onValueChange={handleFontFamilyChange}>
            <SelectTrigger className="w-24 h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Arial">Arial</SelectItem>
              <SelectItem value="Helvetica">Helvetica</SelectItem>
              <SelectItem value="Times New Roman">Times New Roman</SelectItem>
              <SelectItem value="Georgia">Georgia</SelectItem>
              <SelectItem value="Verdana">Verdana</SelectItem>
              <SelectItem value="Courier New">Courier New</SelectItem>
            </SelectContent>
          </Select>

          {/* Text Formatting */}
          <Button
            variant={fontWeight === "bold" ? "default" : "ghost"}
            size="sm"
            onClick={handleBoldToggle}
            className="h-8 w-8 p-0"
            title="Bold (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant={fontStyle === "italic" ? "default" : "ghost"}
            size="sm"
            onClick={handleItalicToggle}
            className="h-8 w-8 p-0"
            title="Italic (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant={textDecoration === "underline" ? "default" : "ghost"}
            size="sm"
            onClick={handleUnderlineToggle}
            className="h-8 w-8 p-0"
            title="Underline (Ctrl+U)"
          >
            <Underline className="h-4 w-4" />
          </Button>
          <Button
            variant={isStrikethrough ? "default" : "ghost"}
            size="sm"
            onClick={handleStrikethroughToggle}
            className="h-8 w-8 p-0"
            title="Strikethrough"
          >
            <Strikethrough className="h-4 w-4" />
          </Button>

          {/* Text Alignment */}
          <Button
            variant={textAlign === "left" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleTextAlignChange("left")}
            className="h-8 w-8 p-0"
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant={textAlign === "center" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleTextAlignChange("center")}
            className="h-8 w-8 p-0"
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant={textAlign === "right" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleTextAlignChange("right")}
            className="h-8 w-8 p-0"
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Button>
          <Button
            variant={textAlign === "justify" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleTextAlignChange("justify")}
            className="h-8 w-8 p-0"
            title="Justify"
          >
            <AlignJustify className="h-4 w-4" />
          </Button>

          {/* Separator */}
          <div className="w-px h-6 bg-gray-300 mx-1" />

          {/* Font Size Controls */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFontSizeDecrease}
              className="h-8 w-6 p-0"
              title="Decrease Font Size"
            >
              <Minus className="h-3 w-3" />
            </Button>
            <Input
              type="number"
              value={fontSize}
              onChange={handleFontSizeChange}
              className="w-12 h-8 text-center text-xs mx-1"
              min="8"
              max="200"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFontSizeIncrease}
              className="h-8 w-6 p-0"
              title="Increase Font Size"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>

          {/* Layer Controls */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBringForward}
            className="h-8 w-8 p-0"
            title="Bring Forward"
          >
            <MoveUp className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSendBackward}
            className="h-8 w-8 p-0"
            title="Send Backward"
          >
            <MoveDown className="h-4 w-4" />
          </Button>

          {/* Separator */}
          <div className="w-px h-6 bg-gray-300 mx-1" />

          {/* Opacity Control */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-1" title="Opacity">
                <div className="w-4 h-4 bg-black/50 rounded border mr-1" />
                <span className="text-xs">{opacity}%</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">Opacity</div>
                <Slider
                  value={[opacity]}
                  onValueChange={handleOpacityChange}
                  max={100}
                  min={0}
                  step={1}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{opacity}%</div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Visibility and Lock Controls */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleVisibility}
            className="h-8 w-8 p-0"
            title={isVisible ? "Hide" : "Show"}
          >
            {isVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleLock}
            className="h-8 w-8 p-0"
            title={isLocked ? "Unlock" : "Lock"}
          >
            {isLocked ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
          </Button>

          {/* Duplicate and Delete */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDuplicate}
            className="h-8 w-8 p-0"
            title="Duplicate"
          >
            <Copy className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
            title="Delete"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Shape Controls */}
      {isShapeObject && (
        <div className="flex items-center space-x-1 border-r border-gray-300 pr-4">
          <span className="text-xs text-gray-600 font-medium">Shape:</span>

          <input
            type="color"
            value={fillColor}
            onChange={handleFillColorChange}
            className="w-8 h-8 rounded border cursor-pointer"
            title="Fill Color"
          />

          <input
            type="color"
            value={strokeColor}
            onChange={handleStrokeColorChange}
            className="w-8 h-8 rounded border cursor-pointer"
            title="Stroke Color"
          />

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-1 text-xs" title="Stroke Width">
                {strokeWidth}px
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">Stroke Width</div>
                <Slider
                  value={[strokeWidth]}
                  onValueChange={handleStrokeWidthChange}
                  max={20}
                  min={0}
                  step={1}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{strokeWidth}px</div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Layer Controls */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBringForward}
            className="h-8 w-8 p-0"
            title="Bring Forward"
          >
            <MoveUp className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSendBackward}
            className="h-8 w-8 p-0"
            title="Send Backward"
          >
            <MoveDown className="h-4 w-4" />
          </Button>

          {/* Duplicate and Delete */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDuplicate}
            className="h-8 w-8 p-0"
            title="Duplicate"
          >
            <Copy className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
            title="Delete"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Common Object Controls - Only show for non-text objects */}
      {!isTextObject && (
        <div className="flex items-center space-x-1">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-1" title="Opacity">
                <div className="w-4 h-4 bg-black/50 rounded border mr-1" />
                <span className="text-xs">{opacity}%</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">Opacity</div>
                <Slider
                  value={[opacity]}
                  onValueChange={handleOpacityChange}
                  max={100}
                  min={0}
                  step={1}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 text-center">{opacity}%</div>
              </div>
            </PopoverContent>
          </Popover>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleBringForward}
            className="h-8 w-8 p-0"
            title="Bring Forward"
          >
            <MoveUp className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleSendBackward}
            className="h-8 w-8 p-0"
            title="Send Backward"
          >
            <MoveDown className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleVisibility}
            className="h-8 w-8 p-0"
            title={isVisible ? "Hide" : "Show"}
          >
            {isVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleLock}
            className="h-8 w-8 p-0"
            title={isLocked ? "Unlock" : "Lock"}
          >
            {isLocked ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleDuplicate}
            className="h-8 w-8 p-0"
            title="Duplicate"
          >
            <Copy className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
            title="Delete"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
