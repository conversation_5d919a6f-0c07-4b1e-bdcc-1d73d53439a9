"use client";

import { useState, useEffect, useRef } from "react";
import { useEditorStore } from "@/store";
import { cloneSelectedObject, deletedSelectedObject } from "@/fabric/fabric-utils";
import { 
  Copy, 
  Trash2, 
  ChevronUp, 
  ChevronDown, 
  MoreHorizontal,
  Layers,
  Eye,
  EyeOff,
  Lock,
  Unlock
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export default function ObjectControls() {
  const { canvas, selectedObject, markAsModified } = useEditorStore();
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(100);
  const [isVisible, setIsVisible] = useState(true);
  const [isLocked, setIsLocked] = useState(false);
  const controlsRef = useRef(null);

  // Update position when object is selected or moved
  useEffect(() => {
    if (!selectedObject || !canvas) {
      return;
    }

    const updatePosition = () => {
      if (!selectedObject || !canvas) return;

      try {
        // Get object bounding box
        const boundingRect = selectedObject.getBoundingRect();
        const zoom = canvas.getZoom();
        const canvasElement = canvas.getElement();
        const canvasOffset = canvasElement.getBoundingClientRect();

        // Calculate position above the object
        const x = canvasOffset.left + (boundingRect.left + boundingRect.width / 2) * zoom;
        let y = canvasOffset.top + boundingRect.top * zoom - 60; // 60px above the object

        // Ensure the controls don't go off-screen
        const controlsWidth = 280; // Approximate width of controls
        const controlsHeight = 50; // Approximate height of controls

        // Adjust horizontal position if too close to edges
        const adjustedX = Math.max(controlsWidth / 2, Math.min(window.innerWidth - controlsWidth / 2, x));

        // If controls would be above viewport, show them below the object instead
        if (y < 10) {
          y = canvasOffset.top + (boundingRect.top + boundingRect.height) * zoom + 10;
        }

        setPosition({ x: adjustedX, y });
      } catch (error) {
        console.warn('Error updating object controls position:', error);
      }
    };

    // Update position initially
    updatePosition();

    // Update position when object moves or canvas changes
    const handleObjectModified = () => updatePosition();
    const handleCanvasChange = () => updatePosition();

    canvas.on('object:modified', handleObjectModified);
    canvas.on('object:moving', handleObjectModified);
    canvas.on('object:scaling', handleObjectModified);
    canvas.on('object:rotating', handleObjectModified);
    canvas.on('after:render', handleCanvasChange);

    // Update object properties
    setOpacity(Math.round((selectedObject.opacity || 1) * 100));
    setIsVisible(selectedObject.visible !== false);
    setIsLocked(!selectedObject.selectable);

    return () => {
      canvas.off('object:modified', handleObjectModified);
      canvas.off('object:moving', handleObjectModified);
      canvas.off('object:scaling', handleObjectModified);
      canvas.off('object:rotating', handleObjectModified);
      canvas.off('after:render', handleCanvasChange);
    };
  }, [selectedObject, canvas]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedObject || !canvas) return;

      // Delete key
      if (e.key === 'Delete' || e.key === 'Backspace') {
        e.preventDefault();
        handleDelete();
      }

      // Ctrl+D for duplicate
      if (e.ctrlKey && e.key === 'd') {
        e.preventDefault();
        handleDuplicate();
      }

      // Ctrl+] for bring forward
      if (e.ctrlKey && e.key === ']') {
        e.preventDefault();
        handleBringForward();
      }

      // Ctrl+[ for send backward
      if (e.ctrlKey && e.key === '[') {
        e.preventDefault();
        handleSendBackward();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedObject, canvas]);

  // Handle opacity change
  const handleOpacityChange = (value) => {
    const newOpacity = value[0];
    setOpacity(newOpacity);
    if (selectedObject && canvas) {
      selectedObject.set('opacity', newOpacity / 100);
      canvas.renderAll();
      markAsModified();
    }
  };

  // Handle duplicate
  const handleDuplicate = async () => {
    if (!canvas || !selectedObject) return;
    await cloneSelectedObject(canvas);
    markAsModified();
  };

  // Handle delete
  const handleDelete = () => {
    if (!canvas || !selectedObject) return;
    deletedSelectedObject(canvas);
    markAsModified();
  };

  // Handle layer operations
  const handleBringForward = () => {
    if (!canvas || !selectedObject) return;
    canvas.bringObjectForward(selectedObject);
    canvas.renderAll();
    markAsModified();
  };

  const handleSendBackward = () => {
    if (!canvas || !selectedObject) return;
    canvas.sendObjectBackwards(selectedObject);
    canvas.renderAll();
    markAsModified();
  };

  // Handle visibility toggle
  const handleToggleVisibility = () => {
    if (!canvas || !selectedObject) return;
    const newVisibility = !isVisible;
    setIsVisible(newVisibility);
    selectedObject.set('visible', newVisibility);
    canvas.renderAll();
    markAsModified();
  };

  // Handle lock toggle
  const handleToggleLock = () => {
    if (!canvas || !selectedObject) return;
    const newLocked = !isLocked;
    setIsLocked(newLocked);
    selectedObject.set('selectable', !newLocked);
    selectedObject.set('evented', !newLocked);
    canvas.renderAll();
    markAsModified();
  };

  // Don't render if no object is selected
  if (!selectedObject || !canvas) {
    return null;
  }

  return (
    <div
      ref={controlsRef}
      className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex items-center gap-1 animate-in fade-in-0 zoom-in-95 duration-200"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translateX(-50%)',
        pointerEvents: 'auto'
      }}
    >
      {/* Small arrow pointing to the object */}
      <div
        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white border-r border-b border-gray-200 rotate-45"
        style={{ zIndex: -1 }}
      />
      {/* Opacity Control */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <div className="w-4 h-4 bg-black/50 rounded border" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-3">
          <div className="space-y-2">
            <div className="text-sm font-medium">Opacity</div>
            <Slider
              value={[opacity]}
              onValueChange={handleOpacityChange}
              max={100}
              min={0}
              step={1}
              className="w-full"
            />
            <div className="text-xs text-gray-500 text-center">{opacity}%</div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Layer Controls */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Layers className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-40 p-2">
          <div className="space-y-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBringForward}
              className="w-full justify-start h-8"
            >
              <ChevronUp className="h-4 w-4 mr-2" />
              Bring Forward
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSendBackward}
              className="w-full justify-start h-8"
            >
              <ChevronDown className="h-4 w-4 mr-2" />
              Send Backward
            </Button>
          </div>
        </PopoverContent>
      </Popover>

      {/* Visibility Toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleToggleVisibility}
        className="h-8 w-8 p-0"
        title={isVisible ? "Hide" : "Show"}
      >
        {isVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
      </Button>

      {/* Lock Toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleToggleLock}
        className="h-8 w-8 p-0"
        title={isLocked ? "Unlock" : "Lock"}
      >
        {isLocked ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
      </Button>

      {/* Duplicate */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleDuplicate}
        className="h-8 w-8 p-0"
        title="Duplicate"
      >
        <Copy className="h-4 w-4" />
      </Button>

      {/* Delete */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleDelete}
        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
        title="Delete"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}
