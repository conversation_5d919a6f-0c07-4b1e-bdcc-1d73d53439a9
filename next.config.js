/** @type {import('next').NextConfig} */
const nextConfig = {
  // Allow cross-origin requests from local network IPs during development
  experimental: {
    allowedDevOrigins: ['********'],
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  webpack: (config, { isServer }) => {
    // Handle external dependencies
    config.externals.push({
      'utf-8-validate': 'commonjs utf-8-validate',
      'bufferutil': 'commonjs bufferutil',
    });

    // Improve chunk loading reliability
    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            fabric: {
              test: /[\\/]node_modules[\\/]fabric[\\/]/,
              name: 'fabric',
              chunks: 'all',
              priority: 10,
            },
          },
        },
      };
    }

    return config;
  },
}

module.exports = nextConfig
